﻿using Admin.TaskDotNet.Dtos;
using AutoMapper;
using Comman.Services.Interfaces;
using Humanizer;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Api_s.Controllers;
using TaskDotNet.Api_s.Interfaces;
using TaskDotNet.Api_s.Responses;
using TaskDotNet.Api_s.Services;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Helper;
using TaskDotNet.Models;
using TaskDotNetal.Api_s.Models;

namespace TaskDotNet.Api_s
{
    [Route("api/[controller]")]
    [ApiController]
    public class MovingAndCleaningController : ControllerBase
    {

        #region ctor
        private readonly IMovingAndCleaningApiService _movingAndCleaning;
        private readonly ApplicationDbContext _context;

        public MovingAndCleaningController(IMovingAndCleaningApiService movingAndCleaning, ApplicationDbContext context)
        {
            this._movingAndCleaning = movingAndCleaning;
            _context = context;
        }

        #endregion

        #region Create
        [HttpPost("CreateMovingCleaning")]
        public async Task<IActionResult> CreateAsync([FromBody] MovingCleaningDto dto, string lang)
        {

            if (!ModelState.IsValid)
            {
                return BadRequest(new CustomResponse { Code = "400", Message = "Invalid request data." });
            }
            var kanton = _context.PostBoxCities.Where(m => m.PostBox == dto.PostBox)
                    .Select(m => m.Kanton)
                    .FirstOrDefault();

            dto.Kanton = kanton;
            var activity = await _movingAndCleaning.CreateMovingAndCleaningActivityAsync(dto, lang);
            var ordersCount = await _context.OrdersCount.FirstOrDefaultAsync(m => m.WebsiteName == "TaskDotNet");
            ordersCount.T_Mov_Clean += 1;
            
            var notification = new ActivityNotification()
            {
                ActivityType = activity.ActivityType,
                City = activity.City,
                Name = activity.Name,
                PostBox = activity.PostBox,
                Salute = activity.Salute,
                ExcuteDate = activity.CleaningDate,
            };
            _context.ActivityNotifications.Add(notification);

            await _context.SaveChangesAsync();

            return Ok(new ActivityResponse
            {
                Code = "200",
                Status = "Success",
                Message = "Moving and Cleaning activity created successfully!",
                Data = activity
            });

        }

        #endregion

        #region Add Inventory To MovingAndCleaning
        [HttpPost(template: "AddInventoryWithMovingAndCleaning")]
        public async Task<IActionResult> AddInventoryWithMoving(AddInventoryWithMovingAndCleaningRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new CustomResponse { Code = "400", Message = "Invalid request data." });
            }
            var kanton = _context.PostBoxCities.Where(m => m.PostBox == request.MovingDto.PostBox)
                .Select(m => m.Kanton)
                .FirstOrDefault();

            request.MovingDto.Kanton = kanton;
            var activity = await _movingAndCleaning.AddInventoryToMovingAndCleaningAsync(request);
            var ordersCount = await _context.OrdersCount.FirstOrDefaultAsync(m => m.WebsiteName == "TaskDotNet");
            ordersCount.T_Mov_Clean += 1;
            
            var notification = new ActivityNotification()
            {
                ActivityType = activity.ActivityType,
                City = activity.City,
                Name = activity.Name,
                PostBox = activity.PostBox,
                Salute = activity.Salute,
                ExcuteDate = activity.MovingDate,
            };
            _context.ActivityNotifications.Add(notification);

            await _context.SaveChangesAsync();

            return Ok(new ActivityResponse
            {
                Code = "200",
                Status = "Success",
                Message = "Moving and Cleaning activity with inventory added successfully!",
                Data = activity
            });
        }

        #endregion
    }


    public class AddInventoryWithMovingAndCleaningRequest
    {
        public MovingCleaningDto MovingDto { get; set; }
        public IEnumerable<InventoryItemApiDto> InventoryItems { get; set; }
        public string Lang { get; set; }
    }
}
