﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Account" xml:space="preserve">
    <value>Account</value>
  </data>
  <data name="Account Data" xml:space="preserve">
    <value>Account Data</value>
  </data>
  <data name="Account Settings" xml:space="preserve">
    <value>Account Settings</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="Activites setup" xml:space="preserve">
    <value>Adjust activity settings</value>
  </data>
  <data name="Activities List" xml:space="preserve">
    <value>Activities List</value>
  </data>
  <data name="New Activities" xml:space="preserve">
    <value>New Activities</value>
  </data>
  <data name="Mark as Checked" xml:space="preserve">
    <value>Mark as Checked</value>
  </data>
  <data name="Are you sure you want to mark this activity as checked?" xml:space="preserve">
    <value>Are you sure you want to mark this activity as checked?</value>
  </data>
  <data name="Delete Activity" xml:space="preserve">
    <value>Delete Activity</value>
  </data>
  <data name="Are you sure you want to delete this activity? This action cannot be undone!" xml:space="preserve">
    <value>Are you sure you want to delete this activity? This action cannot be undone!</value>
  </data>
  <data name="No New Activities" xml:space="preserve">
    <value>No New Activities</value>
  </data>
  <data name="All activities have been checked." xml:space="preserve">
    <value>All activities have been checked.</value>
  </data>
  <data name="ActivityType" xml:space="preserve">
    <value>Activity Type</value>
  </data>
  <data name="MovingDate" xml:space="preserve">
    <value>Moving Date</value>
  </data>
  <data name="Kanton" xml:space="preserve">
    <value>Canton</value>
  </data>
  <data name="Activity" xml:space="preserve">
    <value>Activities List</value>
  </data>
  <data name="ActivityDetails" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="ActivityDetailsTitle" xml:space="preserve">
    <value>The selected request</value>
  </data>
  <data name="Add to archive" xml:space="preserve">
    <value>Add to archive</value>
  </data>
  <data name="AddAmount" xml:space="preserve">
    <value>Add Amount</value>
  </data>
  <data name="AdditionalServices" xml:space="preserve">
    <value>Additional Services</value>
  </data>
  <data name="Additive" xml:space="preserve">
    <value>Additive</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="Administrator" xml:space="preserve">
    <value>Administrator</value>
  </data>
  <data name="AdvertisingList" xml:space="preserve">
    <value>Advertising directory</value>
  </data>
  <data name="Albanian" xml:space="preserve">
    <value>Albanian</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="All Activity" xml:space="preserve">
    <value>All activities</value>
  </data>
  <data name="All Paertners" xml:space="preserve">
    <value>Partner list</value>
  </data>
  <data name="AlreadyPurchased" xml:space="preserve">
    <value>You have already purchased this order. A second purchase is not possible.</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="AmountMustBeGreaterThanZero" xml:space="preserve">
    <value>The amount must be greater than zero.</value>
  </data>
  <data name="Apartment" xml:space="preserve">
    <value>Apartment</value>
  </data>
  <data name="Approx." xml:space="preserve">
    <value>Ca.</value>
  </data>
  <data name="Arabic" xml:space="preserve">
    <value>Arabic</value>
  </data>
  <data name="Archive" xml:space="preserve">
    <value>Archive</value>
  </data>
  <data name="Archive Content" xml:space="preserve">
    <value>Archive Content</value>
  </data>
  <data name="Archive entry" xml:space="preserve">
    <value>Archive entry:</value>
  </data>
  <data name="Are you sure you want to delete the selected entry?" xml:space="preserve">
    <value>Are you sure you want to delete the selected record?</value>
  </data>
  <data name="Area" xml:space="preserve">
    <value>Area</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>Are you sure you want to proceed with the reset?</value>
  </data>
  <data name="AssembleFurniture" xml:space="preserve">
    <value>Assemble Furniture</value>
  </data>
  <data name="AT_Austria" xml:space="preserve">
    <value>AT–Austria</value>
  </data>
  <data name="Auszug" xml:space="preserve">
    <value>Excerpt</value>
  </data>
  <data name="Background" xml:space="preserve">
    <value>Background</value>
  </data>
  <data name="BackToList" xml:space="preserve">
    <value>Back to List</value>
  </data>
  <data name="BadRequestMessage" xml:space="preserve">
    <value>Something went wrong. Please try again later.</value>
  </data>
  <data name="BadRequestTitle" xml:space="preserve">
    <value>Bad Request ⚠️</value>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>Balance</value>
  </data>
  <data name="BalanceRechargeHistory" xml:space="preserve">
    <value>Balance Recharge History</value>
  </data>
  <data name="Balcony" xml:space="preserve">
    <value>Balcony</value>
  </data>
  <data name="Bank" xml:space="preserve">
    <value>Bank</value>
  </data>
  <data name="Bank Name" xml:space="preserve">
    <value>Bank Name</value>
  </data>
  <data name="Basement, Cellar" xml:space="preserve">
    <value>Basement</value>
  </data>
  <data name="Basic Cleaning" xml:space="preserve">
    <value>Construction Cleaning</value>
  </data>
  <data name="Blocked" xml:space="preserve">
    <value>Blocked</value>
  </data>
  <data name="Both" xml:space="preserve">
    <value>Both together</value>
  </data>
  <data name="BoxCity" xml:space="preserve">
    <value>Postal Code / City</value>
  </data>
  <data name="Boxes" xml:space="preserve">
    <value>Cardboard boxes</value>
  </data>
  <data name="Branch" xml:space="preserve">
    <value>Sector</value>
  </data>
  <data name="Building Cleaning" xml:space="preserve">
    <value>Building Cleaning</value>
  </data>
  <data name="Business" xml:space="preserve">
    <value>Business</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Card number" xml:space="preserve">
    <value>Card number</value>
  </data>
  <data name="Cardboard boxes" xml:space="preserve">
    <value>Cardboard boxes</value>
  </data>
  <data name="CarpetCleaning" xml:space="preserve">
    <value>Carpet Cleaning</value>
  </data>
  <data name="Carpets" xml:space="preserve">
    <value>Carpets</value>
  </data>
  <data name="Cartons" xml:space="preserve">
    <value>Moving boxes</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="Category (room)" xml:space="preserve">
    <value>Category (room)</value>
  </data>
  <data name="Cellar" xml:space="preserve">
    <value>Basement</value>
  </data>
  <data name="Change password" xml:space="preserve">
    <value>Change password</value>
  </data>
  <data name="ChangePasswordHint" xml:space="preserve">
    <value>Leave this field empty if no change is desired.</value>
  </data>
  <data name="Checkout" xml:space="preserve">
    <value>Checkout…</value>
  </data>
  <data name="CheckoutRedirectMessage" xml:space="preserve">
    <value>You are about to top up your balance. Do you want to continue?</value>
  </data>
  <data name="Choose email content" xml:space="preserve">
    <value>Select message</value>
  </data>
  <data name="ChooseActivity" xml:space="preserve">
    <value>Choose the appropriate activity</value>
  </data>
  <data name="CH_Switzerland" xml:space="preserve">
    <value>CH-Switzerland</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="Cleaning" xml:space="preserve">
    <value>Cleaning</value>
  </data>
  <data name="CleaningAndHandover" xml:space="preserve">
    <value>Cleaning and handover of a</value>
  </data>
  <data name="CleaningDate" xml:space="preserve">
    <value>Cleaning date</value>
  </data>
  <data name="CleaningType" xml:space="preserve">
    <value>Cleaning Type</value>
  </data>
  <data name="Click buttons to filter by activity" xml:space="preserve">
    <value>Click the buttons to filter by activity</value>
  </data>
  <data name="Click to buy" xml:space="preserve">
    <value>Buy</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="Company Data" xml:space="preserve">
    <value>Company Data</value>
  </data>
  <data name="Company Move" xml:space="preserve">
    <value>Company Move</value>
  </data>
  <data name="Company Name" xml:space="preserve">
    <value>Company Name</value>
  </data>
  <data name="CompanyDetails" xml:space="preserve">
    <value>Company Details</value>
  </data>
  <data name="CompareAttribute_MustMatch" xml:space="preserve">
    <value>The {0} and {1} fields must match.</value>
  </data>
  <data name="CompleteProfileMessage" xml:space="preserve">
    <value>Please complete your profile information to complete the registration correctly!</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>Confirm</value>
  </data>
  <data name="Confirm New Password" xml:space="preserve">
    <value>Please confirm the new password.</value>
  </data>
  <data name="Confirm Password" xml:space="preserve">
    <value>Confirm Password</value>
  </data>
  <data name="ConfirmationCode" xml:space="preserve">
    <value>Confirmation Code</value>
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value>Confirmed</value>
  </data>
  <data name="ConfirmMessage" xml:space="preserve">
    <value>We have sent you a confirmation code by email. Please enter it here.</value>
  </data>
  <data name="Construction Cleaning" xml:space="preserve">
    <value>Construction Cleaning</value>
  </data>
  <data name="Contact person" xml:space="preserve">
    <value>Contact Person</value>
  </data>
  <data name="ContactDetails" xml:space="preserve">
    <value>Contact Details</value>
  </data>
  <data name="ContactSubTitle" xml:space="preserve">
    <value>Detailed information about the contact request</value>
  </data>
  <data name="ContactUs" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="Create a room" xml:space="preserve">
    <value>Create a room</value>
  </data>
  <data name="Create entry" xml:space="preserve">
    <value>New entry</value>
  </data>
  <data name="Create room content" xml:space="preserve">
    <value>Add room content</value>
  </data>
  <data name="Credit card" xml:space="preserve">
    <value>Credit card</value>
  </data>
  <data name="Croatian" xml:space="preserve">
    <value>Croatian</value>
  </data>
  <data name="Crook" xml:space="preserve">
    <value>Crook</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="Current Balance" xml:space="preserve">
    <value>Current Balance</value>
  </data>
  <data name="Current balance: CHF" xml:space="preserve">
    <value>Current balance: CHF</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="CustomerFocus" xml:space="preserve">
    <value>CustomerFocus</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Information Sheet</value>
  </data>
  <data name="Data imported successfully." xml:space="preserve">
    <value>The data was imported successfully.</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="DateFrom" xml:space="preserve">
    <value>Clear movements: from</value>
  </data>
  <data name="Daten reinigen" xml:space="preserve">
    <value>Clean archive data</value>
  </data>
  <data name="DateTo" xml:space="preserve">
    <value>to</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Delete record" xml:space="preserve">
    <value>Delete entry</value>
  </data>
  <data name="Delete Selected" xml:space="preserve">
    <value>Delete selected entries</value>
  </data>
  <data name="Depending on the card type, you will find these in the marked position on the back of the card" xml:space="preserve">
    <value>Depending on the card type, you will find these at the marked location on the back of the card.</value>
  </data>
  <data name="DE_Germany" xml:space="preserve">
    <value>DE-Germany</value>
  </data>
  <data name="Different" xml:space="preserve">
    <value>Different</value>
  </data>
  <data name="DismantleFurniture" xml:space="preserve">
    <value>Dismantle Furniture</value>
  </data>
  <data name="DismantleLamp" xml:space="preserve">
    <value>Dismantle Lamp</value>
  </data>
  <data name="Disposal" xml:space="preserve">
    <value>Disposal</value>
  </data>
  <data name="Distance" xml:space="preserve">
    <value>Distance</value>
  </data>
  <data name="Doors" xml:space="preserve">
    <value>Doors</value>
  </data>
  <data name="DoReset" xml:space="preserve">
    <value>Are you sure?</value>
  </data>
  <data name="Download" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="ExportExcel" xml:space="preserve">
    <value>Export Excel</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Edit archive data" xml:space="preserve">
    <value>Edit archive data</value>
  </data>
  <data name="Edit category (rooms)" xml:space="preserve">
    <value>Edit category (rooms)</value>
  </data>
  <data name="Edit entry" xml:space="preserve">
    <value>Edit business address</value>
  </data>
  <data name="Edit messages" xml:space="preserve">
    <value>Edit messages</value>
  </data>
  <data name="Edit objects" xml:space="preserve">
    <value>Edit objects</value>
  </data>
  <data name="Edit partner profile" xml:space="preserve">
    <value>Edit partner profile</value>
  </data>
  <data name="Edit Profile Data" xml:space="preserve">
    <value>Edit Profile Data</value>
  </data>
  <data name="Edit room content" xml:space="preserve">
    <value>Edit room content</value>
  </data>
  <data name="efh" xml:space="preserve">
    <value>SFH</value>
  </data>
  <data name="EightFloor" xml:space="preserve">
    <value>8th floor</value>
  </data>
  <data name="Einzug" xml:space="preserve">
    <value>Move-in</value>
  </data>
  <data name="Electrician" xml:space="preserve">
    <value>Electrical work</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="Email confirm" xml:space="preserve">
    <value>Email confirmed</value>
  </data>
  <data name="Email senden" xml:space="preserve">
    <value>Promotional emails</value>
  </data>
  <data name="Email texts" xml:space="preserve">
    <value>Prepare correspondence texts</value>
  </data>
  <data name="EmailAddressAttribute_ValidationError" xml:space="preserve">
    <value>The field {0} does not contain a valid email address.</value>
  </data>
  <data name="EmailAlreadyExistsError" xml:space="preserve">
    <value>The email address already exists.</value>
  </data>
  <data name="EmailConfirmationMessage1" xml:space="preserve">
    <value>You're almost there!</value>
  </data>
  <data name="EmailConfirmationMessage2" xml:space="preserve">
    <value>Only one** step left to get started!</value>
  </data>
  <data name="EmailConfirmationMessage3" xml:space="preserve">
    <value>Email Verification</value>
  </data>
  <data name="EmailConfirmationMessage4" xml:space="preserve">
    <value>An email with a confirmation code will be sent to you.</value>
  </data>
  <data name="EmailConfirmationMessage5" xml:space="preserve">
    <value>After submitting, please enter the confirmation code to verify your identity.</value>
  </data>
  <data name="EmailSenden" xml:space="preserve">
    <value>Send Email</value>
  </data>
  <data name="EmailSentSuccessfully" xml:space="preserve">
    <value>The email has been sent successfully.</value>
  </data>
  <data name="EmailToCustomer" xml:space="preserve">
    <value>Email to customer</value>
  </data>
  <data name="EmptyOrdersMessage" xml:space="preserve">
    <value>Please try again later.</value>
  </data>
  <data name="EmptyOrdersTitle" xml:space="preserve">
    <value>There are currently no orders.</value>
  </data>
  <data name="EmptyPurchasedOrders" xml:space="preserve">
    <value>You have not yet purchased any requests.</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="Enter the ID number. a" xml:space="preserve">
    <value>Enter the ID number</value>
  </data>
  <data name="EnterCompanyName" xml:space="preserve">
    <value>Enter company name</value>
  </data>
  <data name="EnterYourEmail" xml:space="preserve">
    <value>Enter your email address</value>
  </data>
  <data name="Evaluation" xml:space="preserve">
    <value>Evaluation</value>
  </data>
  <data name="Event Cleaning" xml:space="preserve">
    <value>Event Cleaning</value>
  </data>
  <data name="Excellent" xml:space="preserve">
    <value>Excellent</value>
  </data>
  <data name="Excerpt from" xml:space="preserve">
    <value>Excerpt from</value>
  </data>
  <data name="Exec. Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="ExecDate" xml:space="preserve">
    <value>Execution Date</value>
  </data>
  <data name="ExecutionDate" xml:space="preserve">
    <value>Execution Date</value>
  </data>
  <data name="Expiry Date" xml:space="preserve">
    <value>Expiration date</value>
  </data>
  <data name="External" xml:space="preserve">
    <value>External</value>
  </data>
  <data name="Final Cleaning" xml:space="preserve">
    <value>Final Cleaning</value>
  </data>
  <data name="FiveFloor" xml:space="preserve">
    <value>5th Floor</value>
  </data>
  <data name="Flexibility" xml:space="preserve">
    <value>Flexibility</value>
  </data>
  <data name="Flexible" xml:space="preserve">
    <value>Flexibility</value>
  </data>
  <data name="Floor" xml:space="preserve">
    <value>Floor</value>
  </data>
  <data name="Floor Cleaning" xml:space="preserve">
    <value>Floor Cleaning</value>
  </data>
  <data name="FloorAndPanels" xml:space="preserve">
    <value>Floors and Panels</value>
  </data>
  <data name="Focus" xml:space="preserve">
    <value>CustomerFocus</value>
  </data>
  <data name="ForbiddenMessage" xml:space="preserve">
    <value>Access to this resource is not allowed.</value>
  </data>
  <data name="ForbiddenTitle" xml:space="preserve">
    <value>Access Denied 🚫</value>
  </data>
  <data name="ForgotPassword" xml:space="preserve">
    <value>Forgot password?</value>
  </data>
  <data name="ForgotPasswordMessage" xml:space="preserve">
    <value>Please enter your email address. We will then send you instructions to reset your password.</value>
  </data>
  <data name="FourFloor" xml:space="preserve">
    <value>4th Floor</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>French</value>
  </data>
  <data name="From" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="Furniture" xml:space="preserve">
    <value>Furniture</value>
  </data>
  <data name="FurnitureAssembly" xml:space="preserve">
    <value>Furniture Assembly</value>
  </data>
  <data name="FurnitureLift" xml:space="preserve">
    <value>Furniture Lift</value>
  </data>
  <data name="Garage" xml:space="preserve">
    <value>Garage</value>
  </data>
  <data name="Garden" xml:space="preserve">
    <value>Garden</value>
  </data>
  <data name="Gastronomy" xml:space="preserve">
    <value>Gastronomy</value>
  </data>
  <data name="General statistics" xml:space="preserve">
    <value>General Statistics</value>
  </data>
  <data name="German" xml:space="preserve">
    <value>German</value>
  </data>
  <data name="Gisper" xml:space="preserve">
    <value>Plaster worker</value>
  </data>
  <data name="Good" xml:space="preserve">
    <value>Good</value>
  </data>
  <data name="GreaterTenFloor" xml:space="preserve">
    <value>&gt; 10th Floor</value>
  </data>
  <data name="HandOverDate" xml:space="preserve">
    <value>Handover Date</value>
  </data>
  <data name="HasInventory" xml:space="preserve">
    <value>Inventory</value>
  </data>
  <data name="HaveAccount" xml:space="preserve">
    <value>No account yet?</value>
  </data>
  <data name="Healthcare" xml:space="preserve">
    <value>Healthcare</value>
  </data>
  <data name="HeatingAndEnergy" xml:space="preserve">
    <value>Heating and Energy</value>
  </data>
  <data name="HeavyLoad" xml:space="preserve">
    <value>Heavy load</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>Hello</value>
  </data>
  <data name="HighPressure" xml:space="preserve">
    <value>High Pressure</value>
  </data>
  <data name="Hotel Cleaning" xml:space="preserve">
    <value>Hotel Cleaning</value>
  </data>
  <data name="Hotel industry" xml:space="preserve">
    <value>Hotel Industry</value>
  </data>
  <data name="House Keeping" xml:space="preserve">
    <value>Caretaking</value>
  </data>
  <data name="House maintenance" xml:space="preserve">
    <value>Caretaking</value>
  </data>
  <data name="I have read and agree to the general terms and conditions for purchasing inquiries online via TaskDotNets" xml:space="preserve">
    <value>I confirm that I have read and agree to the general terms and conditions (GTC) for the online purchase of requests on TaskDotNets.com.</value>
  </data>
  <data name="IBAN" xml:space="preserve">
    <value>IBAN No.</value>
  </data>
  <data name="ID Number" xml:space="preserve">
    <value>ID Number</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="IndividualActivity" xml:space="preserve">
    <value>Custom task</value>
  </data>
  <data name="Industrial Cleaning" xml:space="preserve">
    <value>Industrial Cleaning</value>
  </data>
  <data name="Information Sheet" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="Inspection" xml:space="preserve">
    <value>Inspection</value>
  </data>
  <data name="InstallLamp" xml:space="preserve">
    <value>Install lamps</value>
  </data>
  <data name="Internal" xml:space="preserve">
    <value>Intern</value>
  </data>
  <data name="Internal/External" xml:space="preserve">
    <value>Intern / External</value>
  </data>
  <data name="InternalServerErrorMessage" xml:space="preserve">
    <value>Internal server error. Please contact support if the problem persists.</value>
  </data>
  <data name="InternalServerErrorTitle" xml:space="preserve">
    <value>Internal Server Error 💥</value>
  </data>
  <data name="International Move" xml:space="preserve">
    <value>International Move</value>
  </data>
  <data name="InvalidAmount" xml:space="preserve">
    <value>Invalid Amount</value>
  </data>
  <data name="InvalidInput" xml:space="preserve">
    <value>Invalid Input</value>
  </data>
  <data name="Inventory" xml:space="preserve">
    <value>Inventory (Rooms)</value>
  </data>
  <data name="Inventory Items" xml:space="preserve">
    <value>Inventory (Content)</value>
  </data>
  <data name="Inventory list" xml:space="preserve">
    <value>Inventory List</value>
  </data>
  <data name="InventoryItem" xml:space="preserve">
    <value>Inventory Item</value>
  </data>
  <data name="Italian" xml:space="preserve">
    <value>Italian</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>Items</value>
  </data>
  <data name="ItemsList" xml:space="preserve">
    <value>Add and edit room contents</value>
  </data>
  <data name="KantonWorkingIn" xml:space="preserve">
    <value>Please select the cantons in which you would like to work</value>
  </data>
  <data name="KitchenConstruction" xml:space="preserve">
    <value>Kitchen Construction</value>
  </data>
  <data name="Kurdish" xml:space="preserve">
    <value>Kurdish</value>
  </data>
  <data name="Laminate" xml:space="preserve">
    <value>Laminate</value>
  </data>
  <data name="Lamps" xml:space="preserve">
    <value>Lamps</value>
  </data>
  <data name="Land" xml:space="preserve">
    <value>Land</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="Lift" xml:space="preserve">
    <value>Lift</value>
  </data>
  <data name="Linoleum" xml:space="preserve">
    <value>Linoleum</value>
  </data>
  <data name="Locksmith" xml:space="preserve">
    <value>Locksmith services</value>
  </data>
  <data name="Log Out" xml:space="preserve">
    <value>Log Out</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="loginDesc" xml:space="preserve">
    <value>Everything under control - smarter, faster, better organized</value>
  </data>
  <data name="LoginHeader" xml:space="preserve">
    <value>Here you get full access to all requests and information.</value>
  </data>
  <data name="Maintenance Cleaning" xml:space="preserve">
    <value>Maintenance cleaning</value>
  </data>
  <data name="Manage Users" xml:space="preserve">
    <value>Manage Users</value>
  </data>
  <data name="Maximum offers" xml:space="preserve">
    <value>Number of allowed sales</value>
  </data>
  <data name="Mechanic" xml:space="preserve">
    <value>Mechanical work</value>
  </data>
  <data name="Message" xml:space="preserve">
    <value>Message</value>
  </data>
  <data name="Miss" xml:space="preserve">
    <value>Miss</value>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="MoreWork" xml:space="preserve">
    <value>More Work</value>
  </data>
  <data name="Move And Clean" xml:space="preserve">
    <value>Move and Clean</value>
  </data>
  <data name="MoveFrom" xml:space="preserve">
    <value>Move from</value>
  </data>
  <data name="MoveTo" xml:space="preserve">
    <value>to</value>
  </data>
  <data name="Moving" xml:space="preserve">
    <value>Moving</value>
  </data>
  <data name="Moving &amp; Cleaning" xml:space="preserve">
    <value>Moving &amp; Cleaning</value>
  </data>
  <data name="Moving and Cleaning" xml:space="preserve">
    <value>Moving and Cleaning</value>
  </data>
  <data name="Moving in" xml:space="preserve">
    <value>Moving to</value>
  </data>
  <data name="MovingAndCleaning" xml:space="preserve">
    <value>Moving &amp; Cleaning</value>
  </data>
  <data name="MovingBoxes" xml:space="preserve">
    <value>Moving Boxes</value>
  </data>
  <data name="Mr" xml:space="preserve">
    <value>Mr.</value>
  </data>
  <data name="Mrs" xml:space="preserve">
    <value>Mrs.</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Name in English" xml:space="preserve">
    <value>Object in English</value>
  </data>
  <data name="Name in French" xml:space="preserve">
    <value>Object in French</value>
  </data>
  <data name="Name in German" xml:space="preserve">
    <value>Object in German</value>
  </data>
  <data name="Name in Italian" xml:space="preserve">
    <value>Object in Italian</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="New entry" xml:space="preserve">
    <value>New entry</value>
  </data>
  <data name="New message" xml:space="preserve">
    <value>New message</value>
  </data>
  <data name="New Password" xml:space="preserve">
    <value>New Password</value>
  </data>
  <data name="NewPasswordMessage" xml:space="preserve">
    <value>Enter and confirm new password.</value>
  </data>
  <data name="NewRequest" xml:space="preserve">
    <value>New Order</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="NineFloor" xml:space="preserve">
    <value>9th Floor</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="NotEnoughBalance" xml:space="preserve">
    <value>Your current balance is not sufficient. Do you want to continue?</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="NotFoundMessage" xml:space="preserve">
    <value>We could not find the requested page.</value>
  </data>
  <data name="NotFoundTitle" xml:space="preserve">
    <value>Page not found ⚠️</value>
  </data>
  <data name="Nothing" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="NotRequired" xml:space="preserve">
    <value>Not required</value>
  </data>
  <data name="Number" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="Object" xml:space="preserve">
    <value>Object</value>
  </data>
  <data name="Office Cleaning" xml:space="preserve">
    <value>Office Cleaning</value>
  </data>
  <data name="Office Rooms" xml:space="preserve">
    <value>Office Rooms</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Old Paassword" xml:space="preserve">
    <value>Old Password</value>
  </data>
  <data name="OneDay" xml:space="preserve">
    <value>1 Day</value>
  </data>
  <data name="OneFloor" xml:space="preserve">
    <value>1st Floor</value>
  </data>
  <data name="OneMonth" xml:space="preserve">
    <value>1 Month</value>
  </data>
  <data name="OneWeek" xml:space="preserve">
    <value>1 Week</value>
  </data>
  <data name="Order-Nr" xml:space="preserve">
    <value>Request No.</value>
  </data>
  <data name="OrderCountTitle" xml:space="preserve">
    <value>Request control form</value>
  </data>
  <data name="OrderNumber" xml:space="preserve">
    <value>Order Number</value>
  </data>
  <data name="OrdersList" xml:space="preserve">
    <value>Orders List</value>
  </data>
  <data name="OrderType" xml:space="preserve">
    <value>Order Type</value>
  </data>
  <data name="OurPartner" xml:space="preserve">
    <value>Our Partner</value>
  </data>
  <data name="Owner" xml:space="preserve">
    <value>Owner</value>
  </data>
  <data name="Packing" xml:space="preserve">
    <value>Packing</value>
  </data>
  <data name="Pages" xml:space="preserve">
    <value>Pages</value>
  </data>
  <data name="Paid costs CHF" xml:space="preserve">
    <value>Paid costs CHF</value>
  </data>
  <data name="Paid costs CHF1" xml:space="preserve">
    <value>Paid costs CHF</value>
  </data>
  <data name="Painting" xml:space="preserve">
    <value>Painting</value>
  </data>
  <data name="Painting &amp; Gipser" xml:space="preserve">
    <value>Painting &amp; Gipser</value>
  </data>
  <data name="PaintingAndGisper" xml:space="preserve">
    <value>Painting &amp; Gipser</value>
  </data>
  <data name="Parquet" xml:space="preserve">
    <value>Parquet</value>
  </data>
  <data name="Partner Data Updated Successfully" xml:space="preserve">
    <value>Partner data updated successfully</value>
  </data>
  <data name="Partner Profile" xml:space="preserve">
    <value>Partner Profile</value>
  </data>
  <data name="Partner Status" xml:space="preserve">
    <value>Partner Status</value>
  </data>
  <data name="Partner-No" xml:space="preserve">
    <value>Partner-No.</value>
  </data>
  <data name="PartnerBalanceHistory" xml:space="preserve">
    <value>Charging transactions</value>
  </data>
  <data name="PartnerDataUpdatedSuccessfully" xml:space="preserve">
    <value>Partner data updated successfully.</value>
  </data>
  <data name="PartnerList" xml:space="preserve">
    <value>Partner list</value>
  </data>
  <data name="PartnerOrders" xml:space="preserve">
    <value>Partner Orders</value>
  </data>
  <data name="PartnerOrdersReport" xml:space="preserve">
    <value>Partner Order Report</value>
  </data>
  <data name="Partner_Block_UID" xml:space="preserve">
    <value>Access Denied: Your access to the platform has been permanently disabled due to previous violations. Re-registration is not possible.
            If you have any questions, please contact <NAME_EMAIL>.</value>
  </data>
  <data name="Partner_Dashboard01" xml:space="preserve">
    <value>Select suitable requests based on the type of activity and region. Only relevant ones will be shown to you. </value>
  </data>
  <data name="Partner_Dashboard02" xml:space="preserve">
    <value>Reserve quickly and contact the customer directly to arrange an appointment or send an offer.</value>
  </data>
  <data name="Partner_Dashboard03" xml:space="preserve">
    <value>The customer expects a suitable offer, reliability, and punctuality.</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="Pay CHF" xml:space="preserve">
    <value>Pay CHF</value>
  </data>
  <data name="Pay now" xml:space="preserve">
    <value>Pay now</value>
  </data>
  <data name="Payment" xml:space="preserve">
    <value>Payment</value>
  </data>
  <data name="PaymentFehler" xml:space="preserve">
    <value>An error occurred during the payment.</value>
  </data>
  <data name="PaymentMethod" xml:space="preserve">
    <value>Payment Method</value>
  </data>
  <data name="PaymentSuccesfully" xml:space="preserve">
    <value>The payment was successfully completed.</value>
  </data>
  <data name="Period" xml:space="preserve">
    <value>Period</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="Piano" xml:space="preserve">
    <value>Piano</value>
  </data>
  <data name="Plates" xml:space="preserve">
    <value>Tiles</value>
  </data>
  <data name="Please choose a payment method!" xml:space="preserve">
    <value>Please choose a payment method!</value>
  </data>
  <data name="Please pay for the selected order" xml:space="preserve">
    <value>Pay for the selected job now.</value>
  </data>
  <data name="PleaseAcceptTerms" xml:space="preserve">
    <value>Please accept the terms and conditions.</value>
  </data>
  <data name="Plumbing" xml:space="preserve">
    <value>Plumbing work</value>
  </data>
  <data name="PName" xml:space="preserve">
    <value>Contact Person</value>
  </data>
  <data name="Portuguese" xml:space="preserve">
    <value>Portuguese</value>
  </data>
  <data name="PostBox" xml:space="preserve">
    <value>PostBox</value>
  </data>
  <data name="Preisfuer" xml:space="preserve">
    <value>Price for</value>
  </data>
  <data name="Press key to show the list of desired activity" xml:space="preserve">
    <value>Please press the button to see the list of desired activity.</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Price</value>
  </data>
  <data name="Price of the cleaning request" xml:space="preserve">
    <value>Price of the cleaning request</value>
  </data>
  <data name="Price of the combined request" xml:space="preserve">
    <value>Price of the combined request</value>
  </data>
  <data name="Price of the moving request" xml:space="preserve">
    <value>Price of the moving request</value>
  </data>
  <data name="Price of the painting request" xml:space="preserve">
    <value>Price of the painting request</value>
  </data>
  <data name="Price of the plastering request" xml:space="preserve">
    <value>Price of the plastering request</value>
  </data>
  <data name="Price/Quality" xml:space="preserve">
    <value>Price / Quality</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="PrintReport" xml:space="preserve">
    <value>Print Report</value>
  </data>
  <data name="Privacy Policy" xml:space="preserve">
    <value>Privacy Policy</value>
  </data>
  <data name="Private Move" xml:space="preserve">
    <value>Private Move</value>
  </data>
  <data name="Process an order" xml:space="preserve">
    <value>TaskDotNet Platform:  &lt;br /&gt;                                                              The solution for productive work</value>
  </data>
  <data name="Purchased Activites" xml:space="preserve">
    <value>Purchased Activities</value>
  </data>
  <data name="Purchased orders" xml:space="preserve">
    <value>Purchased Orders</value>
  </data>
  <data name="PurchaseDate" xml:space="preserve">
    <value>Purchase Date</value>
  </data>
  <data name="Quality" xml:space="preserve">
    <value>Quality</value>
  </data>
  <data name="RangeAttribute_ValidationError" xml:space="preserve">
    <value>The field {0} must be between {1} and {2}.</value>
  </data>
  <data name="ReadTermsAndCondotions" xml:space="preserve">
    <value>I have read and agree to the General Terms and Conditions (GTC).</value>
  </data>
  <data name="Recharge credit" xml:space="preserve">
    <value>Recharge credit</value>
  </data>
  <data name="RechargeCreditHead1" xml:space="preserve">
    <value>Here you have the option to recharge your credit, so you don't have to pay again with every purchase.</value>
  </data>
  <data name="RechargeCreditHead2" xml:space="preserve">
    <value>Your personal data will be used to process your payment, improve your user experience on this website, and provide you with transparent information about the use of your data.</value>
  </data>
  <data name="RechargeCreditHead3" xml:space="preserve">
    <value>Please read our</value>
  </data>
  <data name="RechargeFailedMessage" xml:space="preserve">
    <value>Recharge failed. Please try again later.</value>
  </data>
  <data name="RechargeMessage" xml:space="preserve">
    <value>Successfully added to your balance. Thank you!</value>
  </data>
  <data name="RechargeSuccessfullyMessage" xml:space="preserve">
    <value>The credit has been successfully recharged.</value>
  </data>
  <data name="Recurring Cleaning" xml:space="preserve">
    <value>Recurring Cleaning</value>
  </data>
  <data name="RefrigerationTechnician" xml:space="preserve">
    <value>Refrigeration technician services</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="Register here" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="Register now" xml:space="preserve">
    <value>Register now</value>
  </data>
  <data name="RegisterHeader1" xml:space="preserve">
    <value>The platform for </value>
  </data>
  <data name="RegisterHeader2" xml:space="preserve">
    <value>maximum productivity.</value>
  </data>
  <data name="RegisterHeader3" xml:space="preserve">
    <value>Efficiently connected. </value>
  </data>
  <data name="RegisterHeader4" xml:space="preserve">
    <value>Effectively delivered.</value>
  </data>
  <data name="RegisterHeader5" xml:space="preserve">
    <value>Please enter your credentials to register with TaskDotNet.</value>
  </data>
  <data name="Regular Cleaning" xml:space="preserve">
    <value>Regular cleaning</value>
  </data>
  <data name="Remember Me" xml:space="preserve">
    <value>Stay signed in</value>
  </data>
  <data name="Request List" xml:space="preserve">
    <value>Request List</value>
  </data>
  <data name="RequiredAttribute_ValidationError" xml:space="preserve">
    <value>The {0} field is required.</value>
  </data>
  <data name="ResetDashboard" xml:space="preserve">
    <value>Reset Dashboard</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Reset Password</value>
  </data>
  <data name="ResetPasswordMessage" xml:space="preserve">
    <value>The password reset link has been sent. Please check your email inbox.</value>
  </data>
  <data name="ResetTitle" xml:space="preserve">
    <value>Form of data cleaning</value>
  </data>
  <data name="Role" xml:space="preserve">
    <value>Access right</value>
  </data>
  <data name="Roofer" xml:space="preserve">
    <value>Roofing work</value>
  </data>
  <data name="Room" xml:space="preserve">
    <value>Room</value>
  </data>
  <data name="RoomList" xml:space="preserve">
    <value>List of Main Rooms</value>
  </data>
  <data name="Rooms" xml:space="preserve">
    <value>Room</value>
  </data>
  <data name="Russian" xml:space="preserve">
    <value>Russian</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Save changes" xml:space="preserve">
    <value>Save changes</value>
  </data>
  <data name="Select an activity" xml:space="preserve">
    <value>Select an activity</value>
  </data>
  <data name="Select Branch" xml:space="preserve">
    <value>Select Branch</value>
  </data>
  <data name="Select Template" xml:space="preserve">
    <value>Select Template</value>
  </data>
  <data name="Select the Activity you want" xml:space="preserve">
    <value>Select the activities you want</value>
  </data>
  <data name="Select the cantons in which you would like to work here" xml:space="preserve">
    <value>Select the cantons where you want to work here.</value>
  </data>
  <data name="Send" xml:space="preserve">
    <value>Send</value>
  </data>
  <data name="SendEmail" xml:space="preserve">
    <value>Send Email</value>
  </data>
  <data name="SendEmailTo" xml:space="preserve">
    <value>Send an email to the partner</value>
  </data>
  <data name="SendMessageToTheCustomer" xml:space="preserve">
    <value>Send message to the customer</value>
  </data>
  <data name="SendResetLink" xml:space="preserve">
    <value>Send reset link</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="SevenFloor" xml:space="preserve">
    <value>7th Floor</value>
  </data>
  <data name="Show all Activity" xml:space="preserve">
    <value>Show all Activities</value>
  </data>
  <data name="Show all Archive data" xml:space="preserve">
    <value>Show all Archive data</value>
  </data>
  <data name="Show all Inventory" xml:space="preserve">
    <value>Edit rooms</value>
  </data>
  <data name="Show all Statistics" xml:space="preserve">
    <value>Show all Statistics</value>
  </data>
  <data name="Sightseeing" xml:space="preserve">
    <value>Inspection</value>
  </data>
  <data name="SixFloor" xml:space="preserve">
    <value>6th Floor</value>
  </data>
  <data name="SmallTransport" xml:space="preserve">
    <value>Small Transport</value>
  </data>
  <data name="SoilType" xml:space="preserve">
    <value>Soil Type</value>
  </data>
  <data name="Sold" xml:space="preserve">
    <value>Sold</value>
  </data>
  <data name="SomeThingWentWrong" xml:space="preserve">
    <value>Something went wrong. Please try again later.</value>
  </data>
  <data name="Space" xml:space="preserve">
    <value>Space</value>
  </data>
  <data name="Spanish" xml:space="preserve">
    <value>Spanish</value>
  </data>
  <data name="Start Date" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="Statistics" xml:space="preserve">
    <value>Statistics</value>
  </data>
  <data name="StatisticsPageTitle" xml:space="preserve">
    <value>This table shows the sales movements for all announced products and activities in the current year.</value>
  </data>
  <data name="StatisticsPageTitlePartner" xml:space="preserve">
    <value>This table shows the purchase movements for all products (activities) acquired in the current year.</value>
  </data>
  <data name="StatisticsPageTitlePartner2" xml:space="preserve">
    <value>The partner's purchases for the current year:</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Storage" xml:space="preserve">
    <value>Storage</value>
  </data>
  <data name="Street" xml:space="preserve">
    <value>Street</value>
  </data>
  <data name="StringLengthAttribute_ValidationError" xml:space="preserve">
    <value>The field {0} must be a string with a maximum length of {1}.</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="System setup" xml:space="preserve">
    <value>System setup</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Your TaskDotNet Team</value>
  </data>
  <data name="TenFloor" xml:space="preserve">
    <value>10th Floor</value>
  </data>
  <data name="TermsNotAccepted" xml:space="preserve">
    <value>Terms Not Accepted</value>
  </data>
  <data name="Thank you for your payment..." xml:space="preserve">
    <value>Thank you for your payment.</value>
  </data>
  <data name="The achievements of partnere for the current month" xml:space="preserve">
    <value>Partner services provided in the current year.</value>
  </data>
  <data name="The total: CHF" xml:space="preserve">
    <value>The total: CHF</value>
  </data>
  <data name="PartnerDashboardMessage" xml:space="preserve">
    <value>This project is for collaboration with "TaskDotNet.com" and helps partners track orders, analyze statistics, and make informed decisions based on current data.</value>
  </data>
  <data name="This request will be deleted after archiving!" xml:space="preserve">
    <value>This request will be deleted after archiving!</value>
  </data>
  <data name="This table displays the work activity statistics for the current year" xml:space="preserve">
    <value>This table displays the work activity statistics for the current year.</value>
  </data>
  <data name="ThisIsRequiredRequest" xml:space="preserve">
    <value>This is the specific request.</value>
  </data>
  <data name="ThreeDays" xml:space="preserve">
    <value>3 Days</value>
  </data>
  <data name="ThreeFloor" xml:space="preserve">
    <value>3rd Floor</value>
  </data>
  <data name="ThreeWeeks" xml:space="preserve">
    <value>3 Weeks</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="To" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="To which branch" xml:space="preserve">
    <value>Select industry</value>
  </data>
  <data name="Top Up Balance" xml:space="preserve">
    <value>Recharge credit</value>
  </data>
  <data name="Total volume" xml:space="preserve">
    <value>Total volume</value>
  </data>
  <data name="toTheDrainEdge" xml:space="preserve">
    <value>to the drain edge</value>
  </data>
  <data name="toTheLoadingEdge" xml:space="preserve">
    <value>to the loading edge</value>
  </data>
  <data name="Turkish" xml:space="preserve">
    <value>Turkish</value>
  </data>
  <data name="TwoDays" xml:space="preserve">
    <value>2 Days</value>
  </data>
  <data name="TwoFloor" xml:space="preserve">
    <value>2nd Floor</value>
  </data>
  <data name="TwoWeeks" xml:space="preserve">
    <value>2 Weeks</value>
  </data>
  <data name="UID" xml:space="preserve">
    <value>UID No.</value>
  </data>
  <data name="UnauthorizedMessage" xml:space="preserve">
    <value>Sorry, you do not have access to this page. Please return to the homepage.</value>
  </data>
  <data name="UnauthorizedTitle" xml:space="preserve">
    <value>You are not authorized! 🔐</value>
  </data>
  <data name="Unit" xml:space="preserve">
    <value>Unit</value>
  </data>
  <data name="Unpacking" xml:space="preserve">
    <value>Unpacking</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="Upload File" xml:space="preserve">
    <value>Upload File</value>
  </data>
  <data name="Useless" xml:space="preserve">
    <value>Useless</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="UsersList" xml:space="preserve">
    <value>Users List</value>
  </data>
  <data name="VerifyAccount" xml:space="preserve">
    <value>Please verify your account.</value>
  </data>
  <data name="View All" xml:space="preserve">
    <value>View All</value>
  </data>
  <data name="View order" xml:space="preserve">
    <value>Order preview</value>
  </data>
  <data name="Viewing" xml:space="preserve">
    <value>Inspection</value>
  </data>
  <data name="ViewingDate" xml:space="preserve">
    <value>Viewing Date</value>
  </data>
  <data name="Volume (m3)" xml:space="preserve">
    <value>Volume (m3)</value>
  </data>
  <data name="Volume in m3" xml:space="preserve">
    <value>Volume in m3</value>
  </data>
  <data name="Walls" xml:space="preserve">
    <value>Walls</value>
  </data>
  <data name="WallsAndCeilings" xml:space="preserve">
    <value>Walls and Ceilings</value>
  </data>
  <data name="Washroom" xml:space="preserve">
    <value>Washroom</value>
  </data>
  <data name="Weak" xml:space="preserve">
    <value>Weak</value>
  </data>
  <data name="Website" xml:space="preserve">
    <value>Website</value>
  </data>
  <data name="WelcomeBack" xml:space="preserve">
    <value>Welcome back, partner!</value>
  </data>
  <data name="Welder" xml:space="preserve">
    <value>Welding work</value>
  </data>
  <data name="Window Cleaning" xml:space="preserve">
    <value>Window Cleaning</value>
  </data>
  <data name="Windows" xml:space="preserve">
    <value>Windows</value>
  </data>
  <data name="Windows Cleaning" xml:space="preserve">
    <value>Window Cleaning</value>
  </data>
  <data name="WithInventoryList" xml:space="preserve">
    <value>with Inventory list</value>
  </data>
  <data name="Workers" xml:space="preserve">
    <value>Workers</value>
  </data>
  <data name="Workspace" xml:space="preserve">
    <value>Area</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="YesOn" xml:space="preserve">
    <value>Yes, on:</value>
  </data>
  <data name="YouWantToPurchaseThisOrder" xml:space="preserve">
    <value>Note: Purchasing this order will deduct {0} from your balance.</value>
  </data>
  <data name="AdminDashboardHeader" xml:space="preserve">
    <value>Comprehensive control on the TaskDotNet platform</value>
  </data>
  <data name="AdminDashboardHeader2" xml:space="preserve">
    <value>This is a statistical page that illustrates all  
partners’ interactions and activities on the  
platform.</value>
  </data>
  <data name="CreateUser" xml:space="preserve">
    <value>Create user</value>
  </data>
  <data name="Handyman" xml:space="preserve">
    <value>Handyman</value>
  </data>
  <data name="RequestControl" xml:space="preserve">
    <value>Request Control</value>
  </data>
  <data name="UpdateUser" xml:space="preserve">
    <value>Update user</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="Credits" xml:space="preserve">
    <value>Credits</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="NoCreditsFound" xml:space="preserve">
    <value>No credits found for the selected period.</value>
  </data>
  <data name="Partnername" xml:space="preserve">
    <value>Partner’s name</value>
  </data>
  <data name="PaymentWay" xml:space="preserve">
    <value>Payment way</value>
  </data>
  <data name="Disclaimer" xml:space="preserve">
    <value>Disclaimer</value>
  </data>
  <data name="LegalNotice" xml:space="preserve">
    <value>Legal Notice</value>
  </data>
  <data name="PrivacyPolicy" xml:space="preserve">
    <value>Privacy Policy</value>
  </data>
  <data name="TermsAndConditions" xml:space="preserve">
    <value>T&amp;Cs</value>
  </data>
  <data name="RequestDataNote" xml:space="preserve">
    <value>Icons indicate available personal customer data in the request.</value>
  </data>
</root>