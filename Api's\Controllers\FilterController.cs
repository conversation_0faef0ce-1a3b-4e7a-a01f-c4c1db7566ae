﻿using TaskDotNet.Comman.DataAccess;
using Microsoft.AspNetCore.Mvc;
using TaskDotNet.Models;

namespace TaskDotNet.Api_s.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FilterController : ControllerBase
    {
        #region ctor 
        private readonly ApplicationDbContext context;
        public FilterController(ApplicationDbContext context)
        {
            this.context = context;
            this.context = context;
        }
        #endregion

        #region Filter
        [HttpGet("FilterCity")]
        public IActionResult FilterCity([FromQuery] string postboxName)
        {
            IQueryable<PostBoxCity> cities = context.PostBoxCities;


            if (!string.IsNullOrEmpty(postboxName))
            {
                cities = cities.Where(a => a.PostBox.StartsWith(postboxName));
            }
            return Ok(cities.ToList());
        }

        #endregion
    }
}
