﻿using TaskDotNet.Helper;

namespace TaskDotNet.Api_s.Services
{
    public class OrderNumberService
    {
        public string GenerateOrderNumber(ActivityType type, int serialNumber)
        {


            // Get current date and time
            DateTime now = DateTime.Now;

            // Extract year, month, day, hour, and minute components
            string YY = now.ToString("yy");
            string MM = now.ToString("MM");
            string DD = now.ToString("dd");
            string HH = now.ToString("HH");
            string mm = now.ToString("mm");

            // Format serial number with leading zeros
            string serial = serialNumber.ToString().PadLeft(3, '0');

            // Generate order number string
            string orderNumber = $"{(int)type}-{YY}{MM}{DD}{HH}{mm}{serial}";

            return orderNumber;
        }
    }
}
