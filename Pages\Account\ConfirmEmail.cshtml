﻿@page
@model ConfirmEmailModel
@{
    ViewData["Title"] = "Confirm email";
}

<div class="container-xxl">
    <div class="authentication-wrapper authentication-basic container-p-y d-flex justify-content-center" style=" height: 100vh; ">
        <div class="authentication-inner w-50">
            <div class="card-header text-center">
                <img class="w-75" src="~/dashboard/assets/img/logo.png" />
            </div>
            <div class="card" style="height:650px">
                <div class="card-body d-flex flex-column p-0">
                    <div class="w-100 h-50 bg-primary" style="border-radius: 0.5rem 0.5rem 0 0;">
                    </div>
                    <div class="w-100" style=" position: absolute; align-items: center; text-align: center; top: 50%; left: 50%; transform: translate(-50%, -53%);">
                        <img src="~/dashboard/assets/img/thank-you.png" style="width: 60%;" />
                    </div>
                    <div class="w-100 h-50 d-flex justify-content-center align-items-end">
                        <a asp-controller="Account" asp-action="login" class="btn btn-lg btn-primary fs-4 mb-4" style="padding:10px 80px">@SharedLocalizer["Ok"]</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
