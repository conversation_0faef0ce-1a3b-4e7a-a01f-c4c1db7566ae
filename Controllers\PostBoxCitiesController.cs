﻿using TaskDotNet.Comman.DataAccess;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace TaskDotNet.Controllers
{
    public class PostBoxCitiesController : Controller
    {
        private readonly ApplicationDbContext _context;
        public PostBoxCitiesController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<IActionResult> AllPostBoxCities()
        {
            var postBoxCities = await _context.PostBoxCities.ToListAsync();

            // Grouping in memory after fetching the data
            var result = postBoxCities
                .GroupBy(p => p.Land) // Group by country (Land)
                .ToDictionary(
                    g => g.Key, // Key by Land (Country)
                    g => g.Select(p => new { p.PostBox, p.City }) // Select PostBox-City pairs
                          .Distinct() // Ensure uniqueness
                          .ToList()
                );

            return Json(result);
        }
    }
}
