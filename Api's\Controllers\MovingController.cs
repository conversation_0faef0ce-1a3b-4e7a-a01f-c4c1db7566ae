﻿using Admin.TaskDotNet.Dtos;
using AutoMapper;
using Comman.Services.Interfaces;
using Humanizer;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text;
using TaskDotNet.Api_s.Interfaces;
using TaskDotNet.Api_s.Responses;
using TaskDotNet.Api_s.Services;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Helper;
using TaskDotNet.Models;
using TaskDotNet.Services;
using TaskDotNetal.Api_s.Models;

namespace TaskDotNet.Api_s.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MovingController : ControllerBase
    {
        #region ctor
        private readonly IMovingApiService _movingApiService;
        private readonly ApplicationDbContext _context;
        private readonly IInventoryService _inventoryService;


        public MovingController(IInventoryService inventoryService, IMovingApiService movingApiService, ApplicationDbContext context)
        {
            _inventoryService = inventoryService;
            _movingApiService = movingApiService;
            _context = context;
        }

        #endregion

        #region Create
        [HttpPost("CreateMoving")]
        public async Task<IActionResult> CreateAsync([FromBody] MovingDto dto, string lang)
        {

            if (!ModelState.IsValid)
            {
                return BadRequest(new CustomResponse { Code = "400", Message = "Invalid request data." });
            }
            var kanton = _context.PostBoxCities.Where(m => m.PostBox == dto.PostBox)
                    .Select(m => m.Kanton)
                    .FirstOrDefault();

            dto.Kanton = kanton;
            var activity = await _movingApiService.CreateMovingActivityAsync(dto, lang);

            var ordersCount = await _context.OrdersCount.FirstOrDefaultAsync(m => m.WebsiteName == "TaskDotNet");
            ordersCount.T_Moving += 1;
            

            var notification = new ActivityNotification()
            {
                ActivityType = activity.ActivityType,
                City = activity.City,
                Name = activity.Name,
                PostBox = activity.PostBox,
                Salute = activity.Salute,
                ExcuteDate = activity.CleaningDate,
            };
            _context.ActivityNotifications.Add(notification);


            await _context.SaveChangesAsync();

            return Ok(new ActivityResponse
            {
                Code = "200",
                Status = "Success",
                Message = "Moving activity created successfully!",
                Data = activity
            });

        }

        #endregion

        #region Get AllInventory
        [HttpGet("GetAllInventory")]
        public async Task<IActionResult> GetAllInventory()
        {
            try
            {
                var data = await _inventoryService.Get();
                InventoryResponse response = new()
                {
                    Code = "200",
                    Status = "Success",
                    Message = "Inventory Data Returned successfully !",
                    Data = data
                };
                return Ok(response);

            }
            catch (Exception ex)
            {
                return BadRequest(ex);

            }

        }


        #endregion

        #region Add Inventory To Moving
        [HttpPost(template: "AddInventoryWithMoving")]
        public async Task<IActionResult> AddInventoryWithMoving(AddInventoryWithMovingRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new CustomResponse { Code = "400", Message = "Invalid request data." });
            }
            var kanton = _context.PostBoxCities.Where(m => m.PostBox == request.MovingDto.PostBox)
                    .Select(m => m.Kanton)
                    .FirstOrDefault();

            request.MovingDto.Kanton = kanton;
            var activity = await _movingApiService.AddInventoryToMovingAsync(request);

            var ordersCount = await _context.OrdersCount.FirstOrDefaultAsync(m => m.WebsiteName == "TaskDotNet");
            ordersCount.T_Moving += 1;
            

            var notification = new ActivityNotification()
            {
                ActivityType = activity.ActivityType,
                City = activity.City,
                Name = activity.Name,
                PostBox = activity.PostBox,
                Salute = activity.Salute,
                ExcuteDate = activity.MovingDate,
            };
            _context.ActivityNotifications.Add(notification);

            await _context.SaveChangesAsync();

            return Ok(new ActivityResponse
            {
                Code = "200",
                Status = "Success",
                Message = "Moving activity with inventory added successfully!",
                Data = activity
            });
        }

        #endregion

    }

    public class AddInventoryWithMovingRequest
    {
        public MovingDto MovingDto { get; set; }
        public IEnumerable<InventoryItemApiDto> InventoryItems { get; set; }
        public string Lang { get; set; }
    }
}