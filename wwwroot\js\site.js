﻿
$(function ($) {
    refreshCurrentTime();

});

function formatNumberInput(el, decimals) {
    if (!isNaN(el.val())) {
        el.val(parseFloat(el.val()).toFixed(decimals));
    } else {
        el.val(parseFloat("0").toFixed(decimals));
    }
}
$('.formate3decimal').on('focusout', function (event) {
    formatNumberInput($(this), 3);
});
$('.formate2decimal').on('focusout', function (event) {
    formatNumberInput($(this), 2);
});
$('.formate1decimal').on('focusout', function (event) {
    formatNumberInput($(this), 1);
});


function refreshCurrentTime() {
    var refresh = 1000; // Refresh rate in milli seconds
    mytime = setTimeout('displayCurrentTime()', refresh)
}

function displayCurrentTime() {
    var options = { timeZone: 'Europe/Berlin', hour12: false };

    document.getElementById('date').innerHTML = new Date().toLocaleDateString('de-DE', options);
    document.getElementById('clock').innerHTML = new Date().toLocaleTimeString('de-DE', options);

    refreshCurrentTime();
}

$(".flat-picker-date").flatpickr({
    altInput: true,
    allowInput: true,
    altFormat: 'd.m.Y',
    dateFormat: "Y-m-d",
});
$(".flat-picker-datetime").flatpickr({
    enableTime: true,
    altInput: true,
    allowInput: true,
    altFormat: 'd.m.Y',
    dateFormat: "Y-m-d",
});

$(".flat-picker-time").flatpickr({
    noCalendar: true,
    enableTime: true,
    time_24hr: true,
});