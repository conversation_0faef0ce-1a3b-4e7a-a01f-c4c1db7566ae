﻿@model Dictionary<string, Dictionary<string, int>>

@{
        ViewData["Title"] = "Statistics";

    var currentMonthandYear = DateTime.UtcNow.ToString("yyyy");

    var eachActivityCountAndSum = ViewBag.EachActivityCountAndSum as Dictionary<string, (int Count, decimal Sum)>;
}


<div class="content-wrapper">
    <!-- Content -->

    <div class="container-fluid flex-grow-1 ">
        <h4 class="fw-boldtext-body " style="color:aliceblue">
            General statistics
        </h4>
        <h4 class="fw-bold mb-4 " style="color:white">
            @SharedLocalizer["StatisticsPageTitlePartner"]
        </h4>

        <!-- Lass uns probieren, 30.06.2024 16:18Uhr -->
        <!-- Basic Bootstrap Table -->
        <div class="card">
            <div class="  container my-5">
                <div class="row">
                    <div class="col">
                        <div class="table-responsive">

                            <table class="table table-striped table-bordered" style="width:100%">
                                <thead class="bg-label-primary">
                                    <tr class="bg-primary">
                                        <th style="color:white">Month</th>
                                        @foreach (var month in Model.Keys)
                                        {
                                            <th class="text-center text-nowrap" style="color:white">@month</th>
                                        }
                                    </tr>


                                </thead>
                                <tbody class="table-border-bottom-0">
                                    @foreach (var category in Model.First().Value.Keys)
                                    {
                                        <tr>
                                            <td>@category</td>
                                            @foreach (var month in Model.Keys)
                                            {
                                                <td class="text-center">@Model[month][category]</td>
                                            }
                                        </tr>
                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <hr class="bg-primary my-5" />

                <div>
                    <h4 class="fw-bold mb-4 text-primary text-center">
                        @SharedLocalizer["StatisticsPageTitlePartner2"] @currentMonthandYear
                    </h4>
                </div>

                <div class="row">
                    <div class="col">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered" style="width:100%">
                                <thead class="bg-label-primary">
                                    <tr class="bg-primary">
                                        <th></th>
                                        @foreach (var item in eachActivityCountAndSum)
                                        {
                                            <th class="text-center text-white text-nowrap">@item.Key</th>
                                        }

                                    </tr>
                                </thead>
                                <tbody class="table-border-bottom-0">

                                    <tr class="bg-primary">

                                        <td>@SharedLocalizer["Purchased orders"]</td>
                                         @foreach (var item in eachActivityCountAndSum)
                                        {
                                            <td class="text-center">@item.Value.Count</td>
                                        }
                                    </tr>
                                    <tr>

                                        <td>@SharedLocalizer["Paid costs CHF"]</td>
                                         @foreach (var item in eachActivityCountAndSum)
                                        {
                                            <td class="text-center">@item.Value.Sum</td>
                                        }
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--/ Basic Bootstrap Table -->

    </div>
</div>





