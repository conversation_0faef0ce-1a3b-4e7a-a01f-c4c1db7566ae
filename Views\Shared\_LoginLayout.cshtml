﻿<!DOCTYPE html>

<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - TaskDotNet</title>


    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="~/Dashboard/assets/img/favicon/favicon.ico" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap"
          rel="stylesheet" />

    <!-- Icons. Uncomment required icon fonts -->
    <link rel="stylesheet" href="~/Dashboard/assets/vendor/fonts/boxicons.css" />

    <!-- Core CSS -->
    <link rel="stylesheet" href="~/Dashboard/assets/vendor/css/core.css" class="template-customizer-core-css" />
    <link rel="stylesheet" href="~/Dashboard/assets/vendor/css/theme-default.css" class="template-customizer-theme-css" />
    <link rel="stylesheet" href="~/Dashboard/assets/css/demo.css" />

    <!-- Page CSS -->
    <script src="~/Dashboard/assets/vendor/js/helpers.js"></script>
    <link href="~/dashboard/assets/vendor/css/page-auth.css" rel="stylesheet" />

    <!--! Template customizer & Theme config files MUST be included after core stylesheets and helpers.js in the <head> section -->
    <!--? Config:  Mandatory theme config file contain global vars & default theme options, Set your preferred theme option in this file.  -->
    <script src="~/Dashboard/assets/js/config.js"></script>
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/TaskDotNet.styles.css" asp-append-version="true" />
</head>

<body>

    <!-- Content -->
    @RenderBody()
    <!-- / Content -->

    <script src="~/Dashboard/assets/vendor/jquery/jquery.js"></script>
    <script src="~/Dashboard/assets/vendor/popper/popper.js"></script>
    <script src="~/Dashboard/assets/vendor/js/bootstrap.js"></script>

    <script src="~/Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.js"></script>

    <script src="~/Dashboard/assets/vendor/js/menu.js"></script>

    <!-- Main JS -->
    <script src="~/Dashboard/assets/js/main.js"></script>


    <!-- Place this tag in your head or just before your close body tag. -->

    <script src="~/js/site.js" asp-append-version="true"></script>
    <!-- Another JS -->
    <script async defer src="https://buttons.github.io/buttons.js"></script>

    <partial name="_ValidationScriptsPartial" />
    @RenderSection("Scripts", required: false)
</body>
</html>
