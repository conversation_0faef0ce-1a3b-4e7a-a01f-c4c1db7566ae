{"format": 1, "restore": {"C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\TaskDotNet\\TaskDotNet.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\Comman\\Comman.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\Comman\\Comman.csproj", "projectName": "<PERSON><PERSON><PERSON>", "projectPath": "C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\Comman\\Comman.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\Comman\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 21.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\TaskDotNet.Localization\\TaskDotNet.Localization.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\TaskDotNet.Localization\\TaskDotNet.Localization.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"MailKit": {"target": "Package", "version": "[4.7.1.1, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[6.0.33, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.33, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[6.0.33, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.33, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\TaskDotNet.Localization\\TaskDotNet.Localization.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\TaskDotNet.Localization\\TaskDotNet.Localization.csproj", "projectName": "TaskDotNet.Localization", "projectPath": "C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\TaskDotNet.Localization\\TaskDotNet.Localization.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\TaskDotNet.Localization\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 21.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\TaskDotNet\\TaskDotNet.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\TaskDotNet\\TaskDotNet.csproj", "projectName": "TaskDotNet", "projectPath": "C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\TaskDotNet\\TaskDotNet.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\TaskDotNet\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 21.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\Comman\\Comman.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\Comman\\Comman.csproj"}, "C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\TaskDotNet.Localization\\TaskDotNet.Localization.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\TaskDotNet_14_08_2025\\TaskDotNet_07_08_2025\\TaskDotNet.Localization\\TaskDotNet.Localization.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[6.0.33, )"}, "Microsoft.AspNetCore.Identity.UI": {"target": "Package", "version": "[6.0.33, )"}, "Microsoft.AspNetCore.SpaProxy": {"target": "Package", "version": "[6.*-*, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.33, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.33, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[6.0.33, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.33, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[6.0.17, )"}, "PostFinanceCheckout": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}}}