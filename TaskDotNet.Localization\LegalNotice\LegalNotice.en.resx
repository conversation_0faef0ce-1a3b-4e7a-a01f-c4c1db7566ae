<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="LegalNoticeTitle" xml:space="preserve">
    <value>Legal Notice</value>
  </data>
  <data name="ContactTitle" xml:space="preserve">
    <value>Contact Address</value>
  </data>
  <data name="ContactCompany" xml:space="preserve">
    <value>Company: Lightsoft GmbH</value>
  </data>
  <data name="ContactName" xml:space="preserve">
    <value>Name: M. Sadeq</value>
  </data>
  <data name="ContactAddress" xml:space="preserve">
    <value>Address: Zürcherstrasse 3</value>
  </data>
  <data name="ContactCity" xml:space="preserve">
    <value>8855 Wangen, Switzerland</value>
  </data>
  <data name="ContactEmail" xml:space="preserve">
    <value>E-Mail</value>
  </data>
  <data name="ContactWebsite" xml:space="preserve">
    <value>Website</value>
  </data>
  <data name="ContactPhone" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="ContactRepresentative" xml:space="preserve">
    <value>Authorized Representative(s)</value>
  </data>
  <data name="ContactPosition" xml:space="preserve">
    <value>Managing Director</value>
  </data>
  <data name="ContactRegister" xml:space="preserve">
    <value>Commercial Register of the Canton of Schwyz</value>
  </data>
  <data name="DisclaimerTitle" xml:space="preserve">
    <value>Disclaimer</value>
  </data>
  <data name="DisclaimerContent1" xml:space="preserve">
    <value>Lightsoft GmbH assumes no liability for the correctness, accuracy, timeliness, reliability, or completeness of the information provided.</value>
  </data>
  <data name="DisclaimerContent2" xml:space="preserve">
    <value>Liability claims against the company for material or immaterial damages resulting from access to, use or non-use of the published information, misuse of the connection, or technical faults are excluded.</value>
  </data>
  <data name="DisclaimerContent3" xml:space="preserve">
    <value>All inquiries are non-binding. Lightsoft GmbH expressly reserves the right to modify, supplement, delete parts of the site or the entire offer without prior notice, or to temporarily or permanently discontinue publication.</value>
  </data>
  <data name="LinksTitle" xml:space="preserve">
    <value>Disclaimer for Links</value>
  </data>
  <data name="LinksContent" xml:space="preserve">
    <value>References and links to third-party websites lie outside our responsibility. We reject any liability for such websites. Access and use of such websites are at the user's own risk.</value>
  </data>
  <data name="CopyrightTitle" xml:space="preserve">
    <value>Copyright</value>
  </data>
  <data name="CopyrightContent" xml:space="preserve">
    <value>The copyright and all other rights to content, images, photos, or other files on this website belong exclusively to Lightsoft GmbH. Written permission from the copyright holder must be obtained in advance for the reproduction of any elements.</value>
  </data>
  <data name="CookiesTitle" xml:space="preserve">
    <value>Use of Cookies</value>
  </data>
  <data name="CookiesContent" xml:space="preserve">
    <value>This website uses cookies. Cookies are small text files that are stored permanently or temporarily on your computer when you visit this site. The purpose of cookies is to analyze the use of the website for statistical evaluation and continuous improvement. You can disable cookies partially or entirely in your browser settings at any time. If cookies are disabled, some functions of this website may no longer be fully available.</value>
  </data>
  <data name="CookiesSource" xml:space="preserve">
    <value>Source</value>
  </data>
</root>
