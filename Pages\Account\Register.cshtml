﻿@page
@model RegisterModel

@{
    ViewData["Title"] = "Register";
}


<!-- Content -->
<section style="height:100vh">
    <div>
        <div>
            <div class="d-flex justify-content-center align-items-center vh-100" style="background-color: #677587;">
                <div class="row g-0" style="width:70%;height: 80%; background-color: white;">
                    <div class="col-md-6 d-flex align-items-center justify-content-center text-center flex-column" style="background-color:#4a5474">
                        <img src="~/dashboard/assets/img/logo.png" class="mb-8" style="width:300px" />
                        <h2 class="mb-2" style="color:#99CC33">@SharedLocalizer["RegisterHeader1"] <br /> @SharedLocalizer["RegisterHeader2"]</h2>
                        <img src="~/dashboard/assets/img/foto_register_form.png" alt="login form" class="" width="300px" />
                        <h3 style="color:#99CC33">@SharedLocalizer["RegisterHeader3"]<br /> @SharedLocalizer["RegisterHeader4"]</h3>
                    </div>

                    <div class="col-md-6 d-flex align-items-center justify-content-center p-lg-10">
                        <div class="card-body p-4 p-lg-5 text-black">
                            <h1 class="mb-2 text-center">@SharedLocalizer["Register now"]</h1>
                            <p class="mb-4 text-center fs-5">@SharedLocalizer["RegisterHeader5"]</p>

                            <form asp-controller="Account" asp-action="Register" class="mb-3" method="post">
                                <div asp-validation-summary="All" class="text-danger"></div>
                                <div class="mb-3">
                                    <label asp-for="Input.CompanyName" class="form-label fs-5">@SharedLocalizer["Company name"]</label>
                                    <input asp-for="Input.CompanyName" class="form-control" placeholder="@SharedLocalizer["EnterCompanyName"] " />
                                    <span asp-validation-for="Input.CompanyName" class="text-danger"></span>
                                </div>
                                <div class="mb-3">
                                    <label for="Email" class="form-label fs-5">@SharedLocalizer["Email"] </label>
                                    <input type="email" asp-for="Input.Email" class="form-control" placeholder="@SharedLocalizer["EnterYourEmail"] " />
                                    <span asp-validation-for="Input.Email" class="text-danger"></span>
                                </div>
                                <div class="mb-3 form-password-toggle">
                                    <label class="form-label fs-5" for="Password">@SharedLocalizer["Password"]</label>
                                    <input asp-for="Input.Password" class="form-control" />
                                    <span asp-validation-for="Input.Password" class="text-danger"></span>
                                </div>
                                <div class="mb-3 form-password-toggle">
                                    <label class="form-label fs-5" for="ConfirmPassword">@SharedLocalizer["Confirm Password"]</label>
                                    <input asp-for="Input.ConfirmPassword" class="form-control" />
                                    <span asp-validation-for="Input.ConfirmPassword" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <input class="btn btn-lg btn-primary d-grid w-100" style="color: #fff; background-color: #4a5474; border-color: #4a5474; box-shadow: 0 0.125rem 0.25rem 0 #4a5474;" value="@SharedLocalizer["Register"]" type="submit" />
                                </div>
                            </form>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>



@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}