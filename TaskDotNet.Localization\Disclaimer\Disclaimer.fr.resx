<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="DisclaimerTitle" xml:space="preserve">
    <value>Clause de non-responsabilité</value>
  </data>
  <data name="OnlineOffersTitle" xml:space="preserve">
    <value>Offres en ligne et contenu</value>
  </data>
  <data name="OnlineOffersContent" xml:space="preserve">
    <value>L'exploitante des plateformes TaskDotNet.ch décline toute responsabilité quant à l'exactitude, l'exhaustivité ou la qualité des informations fournies. Toute réclamation résultant de l'utilisation ou de la non-utilisation des services proposés est exclue, sauf en cas de faute intentionnelle ou de négligence grave avérée. L'exploitante se réserve le droit d'apporter des modifications à certains services ou à l'ensemble de l'offre sans préavis, ou de suspendre temporairement ou définitivement la publication.</value>
  </data>
  <data name="ExternalLinksTitle" xml:space="preserve">
    <value>Liens et références externes</value>
  </data>
  <data name="ExternalLinksContent" xml:space="preserve">
    <value>L'exploitante n'assume aucune responsabilité pour le contenu de sites web externes accessibles via des liens, et qui ne relèvent pas de sa responsabilité. Une responsabilité ne peut être engagée que si l'exploitante avait connaissance de contenus illégaux et qu'il lui était techniquement possible et raisonnable d'en empêcher l'accès. Au moment de la création du lien, les pages liées ont été vérifiées et ne présentaient aucun contenu illégal apparent. L'exploitante se distancie expressément de toute modification ultérieure du contenu des pages liées.</value>
  </data>
  <data name="ValidityJurisdictionTitle" xml:space="preserve">
    <value>Validité et juridiction compétente</value>
  </data>
  <data name="ValidityJurisdictionContent" xml:space="preserve">
    <value>En utilisant les plateformes, l'utilisateur accepte les Conditions Générales (CG) ainsi que cette clause de non-responsabilité comme partie intégrante de l'offre. Si certaines dispositions de ce texte ne sont pas conformes à la législation en vigueur, les autres parties restent valables.</value>
  </data>
  <data name="CourtJurisdiction" xml:space="preserve">
    <value>Le for juridique est Zurich, Suisse.</value>
  </data>
</root>
