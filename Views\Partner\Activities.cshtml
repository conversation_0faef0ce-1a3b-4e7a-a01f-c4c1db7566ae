﻿@using Admin.TaskDotNet.Dtos
@using Comman.Helper.Extensions
@model PaginatedListResponse<Activity>
@{
    ViewData["Title"] = "GetAllActivity";

}


@section Links {
    <style>
        /* Custom animations only */
        @@keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @@keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @@keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @@keyframes shimmer {
            0% {
                background-position: -200px 0;
            }
            100% {
                background-position: calc(200px + 100%) 0;
            }
        }

        @@keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        @@keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Minimal custom classes using Bootstrap variables */
        .animate-fade-up {
            animation: fadeInUp 0.6s ease;
        }

        .animate-slide-left {
            animation: slideInLeft 0.6s ease;
        }

        .animate-pulse:hover {
            animation: pulse 0.6s ease;
        }

        .card-hover {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(var(--bs-primary-rgb), 0.3);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200px 100%;
            animation: shimmer 1.5s infinite;
        }

        .bx-spin {
            animation: spin 1s linear infinite;
        }

        /* Staggered animation delays */
        .stagger-1 { animation-delay: 0.1s; }
        .stagger-2 { animation-delay: 0.2s; }
        .stagger-3 { animation-delay: 0.3s; }
        .stagger-4 { animation-delay: 0.4s; }
        .stagger-5 { animation-delay: 0.5s; }
        .stagger-6 { animation-delay: 0.6s; }
        .stagger-7 { animation-delay: 0.7s; }
        .stagger-8 { animation-delay: 0.8s; }

        table td {
            padding: 0 !important;
            color: #000 !important;
        }
    </style>
}

<!-- Content wrapper -->
<div class="content-wrapper">
    <!-- Content -->
    <div class="container-fluid flex-grow-1">
        <div class="bg-primary bg-gradient rounded-3 p-4 mb-4 animate-fade-up">
            <div class="row align-items-center">
                <div class="col-md-2">
                    <h3 class="fw-bold text-white mb-0">@SharedLocalizer["Activities List"]</h3>
                </div>
                <div class="col-md-7 text-center">
                    <h3 class="fw-bold text-white mb-2">@SharedLocalizer["RequestDataNote"]</h3>
                    <div class="d-flex justify-content-center gap-3 mt-2">
                        <i class='bx bx-map fs-2 text-white-50 animate-pulse'></i>
                        <i class='bx bx-phone fs-2 text-white-50 animate-pulse'></i>
                        <i class='bx bx-at fs-2 text-white-50 animate-pulse'></i>
                    </div>
                </div>
                <div class="col-md-3 text-end">
                    <h4 class="fw-bold text-white mb-0">
                        @SharedLocalizer["Current Balance"]:
                        <span class="badge bg-light text-primary fs-5 ms-2">CHF @ViewBag.Salodo</span>
                    </h4>
                </div>
            </div>
        </div>
        <!-- Enhanced Main Card -->
        <div class="card shadow-sm mb-4 animate-slide-left">
            <div class="card-body bg-light">
                <div class="row d-flex justify-content-between align-items-center">
                    <div class="col-lg-6">
                        <div class="form-group row align-items-center">
                            <label for="categoryDropdown" class="col-md-5 col-form-label fs-5 fw-semibold text-primary">
                                <i class="bx bx-filter-alt me-2"></i>@SharedLocalizer["Select an activity"]:
                            </label>
                            <div class="col-md-7">
                                <select class="form-select shadow-sm" asp-items="ViewBag.PartnerActivities" id="categoryDropdown">
                                    <option value="0">@SharedLocalizer["View All"]</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <nav aria-label="Pagination Navigation">
                            <ul class="pagination justify-content-end" id="pagination-list">
                                @{
                                    int currentPage = Model.PageNumber;
                                    int totalPages = Model.TotalPages;
                                    int pageRange = 3; // Number of pages to show before and after the current page
                                    int startPage = Math.Max(1, currentPage - pageRange);
                                    int endPage = Math.Min(totalPages, currentPage + pageRange);

                                    // Always show the first page
                                    if (startPage > 1)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Activities", "Partner", new { PageNumber = 1, SearchTerm = Model.SearchTerm })">1</a>
                                        </li>
                                        if (startPage > 2)
                                        {
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        }
                                    }

                                    // Show the range of pages around the current page
                                    for (int i = startPage; i <= endPage; i++)
                                    {
                                        <li class="page-item @(i == currentPage ? "active" : string.Empty)">
                                            <a class="page-link" href="@Url.Action("Activities", "Partner", new { PageNumber = i, SearchTerm = Model.SearchTerm })">@i</a>
                                        </li>
                                    }

                                    // Always show the last page
                                    if (endPage < totalPages)
                                    {
                                        if (endPage < totalPages - 1)
                                        {
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        }
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Activities", "Partner", new { PageNumber = totalPages, SearchTerm = Model.SearchTerm })">@totalPages</a>
                                        </li>
                                    }
                                }
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <div class="p-4">
                @if (!Model.Data.Any())
                {
                    <div class="text-center py-5 animate-fade-up">
                        <div class="row justify-content-center">
                            <div class="col-lg-6 col-md-8 col-sm-10">
                                <img src="~/dashboard/assets/img/nathing.png"
                                     alt="No Activities Found"
                                     class="img-fluid mb-4 animate-pulse"
                                     style="width: 160px; height: auto;" />
                                <h3 class="text-muted fw-light mb-3">
                                    <i class="bx bx-search-alt me-2"></i>@SharedLocalizer["EmptyOrdersTitle"]
                                </h3>
                                <p class="text-secondary fs-6 mb-4">
                                    @SharedLocalizer["EmptyOrdersMessage"]
                                </p>
                                
                            </div>
                        </div>
                    </div>
                }
                <!-- Enhanced Card Grid -->
                <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 row-cols-xl-4 g-4">
                    @{int cardIndex = 0;}
                    @foreach (var item in Model.Data)
                    {
                        cardIndex++;
                        <div class="col">
                            <div class="card h-100 shadow-sm card-hover animate-fade-up stagger-@(cardIndex % 8 + 1)">
                                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                    <div class="fw-bold fs-6">
                                        <i class="bx bx-briefcase me-2"></i>@item.ActivityType.GetDisplayName()
                                    </div>
                                    <div class="fw-bold fs-6">
                                        <i class="bx bx-calendar me-2"></i>
                                        @if (item.ActivityType == ActivityType.Cleaning)
                                        {
                                            @item.CleaningDate.ToString("dd.MM.yyyy")
                                        }
                                        else
                                        {
                                            @item.MovingDate.ToString("dd.MM.yyyy")
                                        }
                                    </div>
                                </div>
                                <div class="card-body p-4 bg-light bg-gradient">
                                    <!-- Activity Details -->
                                    @if (item.ActivityType == ActivityType.Moving || item.ActivityType == ActivityType.MovingAndCleaning)
                                    {
                                        <table class="table table-borderless">
                                            <tbody>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["From"]:</strong></td>
                                                    <td>@(item.PostBox) @(item.City)</td>
                                                </tr>


                                                <tr>
                                                    <td><strong>@SharedLocalizer["To"]:</strong></td>
                                                    <td>@(item.TPostBox) @(item.TCity)</td>
                                                </tr>

                                                <tr style="height: 20px;"></tr>

                                                <tr>
                                                    <td style=" vertical-align: top; "><strong>@SharedLocalizer["Auszug"]:</strong></td>
                                                    <td>
                                                        @(item.Room) @SharedLocalizer["Room"]-@(SharedLocalizer[item.Object])<br />
                                                        @(SharedLocalizer[item.Floor]), @SharedLocalizer["Lift"]: @(item.FLift ?? false ? @SharedLocalizer["Yes"] : @SharedLocalizer["No"])
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Einzug"]:</strong></td>
                                                    <td>
                                                        @(SharedLocalizer[item.TFloor]), @SharedLocalizer["Lift"]: @(item.TLift ?? false ? @SharedLocalizer["Yes"] : @SharedLocalizer["No"])
                                                    </td>
                                                </tr>
                                                <tr style="height: 10px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Flexibility"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.Flexible])</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        @if (item.ActivityType == ActivityType.MovingAndCleaning)
                                        {
                                            <hr class="m-2" />
                                            <table class="table table-borderless">
                                                <tbody>
                                                    <tr>
                                                        <td><strong>@SharedLocalizer["CleaningDate"]:</strong></td>
                                                        <td>@item.CleaningDate.ToString("dd.MM.yyyy")</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>@SharedLocalizer["HandOverDate"]:</strong></td>
                                                        <td>@item.HandOverDate.ToString("dd.MM.yyyy")</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        }

                                    }
                                    else if (item.ActivityType == ActivityType.Cleaning)
                                    {

                                        <table class="table table-borderless">
                                            <tbody>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Type"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.CleaningType])</td>
                                                </tr>
                                                <tr style="height: 10px;"></tr>

                                                <tr>
                                                    <td style=" vertical-align: top; "><strong>@SharedLocalizer["Ort"]:</strong></td>
                                                    <td>
                                                        @(item.PostBox) @(item.City) <br />
                                                        @(item.Room) @SharedLocalizer["Room"]-@(SharedLocalizer[item.Object])
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Area"]:</strong></td>
                                                    <td>
                                                        @(item.Area), @(SharedLocalizer[item.Floor])
                                                    </td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Flexibility"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.Flexible])</td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["HandOverDate"]:</strong></td>
                                                    <td>@item.HandOverDate.ToString("dd.MM.yyyy")</td>
                                                </tr>
                                            </tbody>
                                        </table>

                                    }
                                    else if (item.ActivityType == ActivityType.PaintingAndGisper)
                                    {
                                        <table class="table table-borderless">
                                            <tbody>

                                                <tr>
                                                    <td style=" vertical-align: top; "><strong>@SharedLocalizer["Ort"]:</strong></td>
                                                    <td>
                                                        @(item.PostBox) @(item.City) <br />
                                                        @(item.Room) @SharedLocalizer["Room"]-@(SharedLocalizer[item.Object]) <br />
                                                        @(SharedLocalizer[item.Floor])
                                                    </td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Area"]:</strong></td>
                                                    <td>
                                                        @(item.Area)
                                                    </td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Workspace"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.Workspace])</td>
                                                </tr>

                                                <tr style="height: 15px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Heater"]:</strong></td>
                                                    <td>@(item.Heater)</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Walls"]:</strong></td>
                                                    <td>@(item.Walls)</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Doors"]:</strong></td>
                                                    <td>@(item.Doors)</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Windows"]:</strong></td>
                                                    <td>@(item.Windows)</td>
                                                </tr>
                                            </tbody>
                                        </table>

                                    }
                                    else
                                    {
                                        <table class="table table-borderless">
                                            <tbody>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Ort"]:</strong></td>
                                                    <td>@(item.PostBox) @(item.City)</td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Object"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.Object])</td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["MoreWork"]:</strong></td>
                                                    <td>@(item.MoreWork)</td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Focus"]:</strong></td>
                                                    <td>
                                                        @(SharedLocalizer[item.Focus])
                                                    </td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Flexibility"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.Flexible])</td>
                                                </tr>
                                                <tr style="height: 15px;"></tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Notes"]:</strong></td>
                                                </tr>
                                                <tr>
                                                    <td class="text-wrap" colspan="2">@(item.Notes)</td>
                                                </tr>
                                            </tbody>
                                        </table>

                                    }
                                </div>
                                <div class="card-footer bg-transparent p-0">
                                    @if (item.ActivityType == ActivityType.Moving || item.ActivityType == ActivityType.MovingAndCleaning)
                                    {
                                        <div class="form-check form-switch m-0 m-3 d-flex align-items-center justify-content-end">
                                            <label class="form-check-label">@SharedLocalizer["HasInventory"]? </label>
                                            <input class="form-check-input m-0 ms-3" type="checkbox" onclick="return false;" @(item.Inventar ?? false ? "checked" : "")
                                                   style="height:25px; width:60px;" />
                                        </div>
                                    }

                                    <div class="bg-primary text-white text-center py-3 fs-5 fw-bold">
                                        <i class="bx bx-money me-2"></i>CHF @(item.Preis.ToString("0.00"))
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center p-3">
                                        <div class="d-flex gap-2">
                                            @if (!string.IsNullOrWhiteSpace(item.Street))
                                            {
                                                <i class='bx bx-map fs-4 text-white bg-primary bg-opacity-10 p-2 rounded-circle' title="Address Available"></i>
                                            }
                                            @if (!string.IsNullOrWhiteSpace(item.Phone))
                                            {
                                                <i class='bx bx-phone fs-4 text-white bg-primary bg-opacity-10 p-2 rounded-circle' title="Phone Available"></i>
                                            }
                                            @if (!string.IsNullOrWhiteSpace(item.Email))
                                            {
                                                <i class='bx bx-at fs-4 text-white bg-primary bg-opacity-10 p-2 rounded-circle' title="Email Available"></i>
                                            }
                                        </div>
                                        <div class="flex-grow-1 text-end">
                                            @if (item.PaymentCountCompleted ?? false)
                                            {
                                                <span class="btn btn-outline-danger disabled">
                                                    <i class="bx bx-check-circle me-2"></i>@SharedLocalizer["Sold"]
                                                </span>
                                            }
                                            else
                                            {
                                                <button class="btn btn-outline-primary shadow-sm"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#checkoutModal"
                                                        onclick="setActivityDetails('@item.Id', '@item.Preis.ToString("0.00")')">
                                                    <i class="bx bx-cart me-2"></i>@SharedLocalizer["Click to buy"]
                                                </button>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
        <!--/ Enhanced Activities Grid -->
    </div>


</div>
 

<div class="modal fade" id="checkoutModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content shadow-lg">
            <div class="modal-header bg-primary text-white">
                <h3 class="modal-title fw-bold text-white" id="exampleModalLabel">
                    <i class="bx bx-cart me-2"></i>@SharedLocalizer["Click to buy"]
                </h3>
                <input type="hidden" id="activityId" />
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-4">
                <div class="mb-4">
                    <i class="bx bx-money fs-1 text-primary mb-3"></i>
                    <p class="fs-4 text-dark mb-4">
                        @Html.Raw(string.Format(
                        SharedLocalizer["YouWantToPurchaseThisOrder"],
                                                "<strong class='fs-3 text-primary'>CHF <span id='checkoutPrice'></span></strong>"
                                                ))
                    </p>
                </div>
                <div class="d-flex justify-content-center gap-3 mt-4">
                    <button class="btn btn-lg btn-outline-secondary px-4" type="button" data-bs-dismiss="modal" aria-label="Close">
                        <i class="bx bx-x me-2"></i>@SharedLocalizer["Cancel"]
                    </button>
                    <button id="checkoutBtn" class="btn btn-lg btn-primary px-4">
                        <i class="bx bx-check me-2"></i>@SharedLocalizer["Ok"]
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Insufficient Balance Modal -->
<div class="modal fade" id="insufficientBalanceModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content shadow-lg">
            <div class="modal-header bg-warning text-dark">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-4">
                <div class="mb-4">
                    <i class="bx bx-error-circle fs-1 text-warning mb-3"></i>
                    <p class="fs-5 text-dark mb-3" id="insufficientBalanceMessage">
                        <!-- Message will be set dynamically -->
                    </p>
                    <div class="alert alert-info d-flex align-items-center">
                        <i class="bx bx-info-circle me-2"></i>
                        <span>@SharedLocalizer["Price"]: <strong>CHF <span id="activityPrice"></span></strong></span>
                    </div>
                </div>
                <div class="d-flex justify-content-center gap-3 mt-4">
                    <button class="btn btn-lg btn-outline-secondary px-4" type="button" data-bs-dismiss="modal" aria-label="Close">
                        <i class="bx bx-x me-2"></i>@SharedLocalizer["Cancel"]
                    </button>
                    <button id="proceedToPaymentBtn" class="btn btn-lg btn-primary px-4">
                        <i class="bx bx-credit-card me-2"></i>@SharedLocalizer["Checkout"]
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>


@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


    <script>

         function setActivityDetails(activityId, price) {
             document.getElementById("activityId").value = activityId;
             document.getElementById('checkoutPrice').textContent = price;
         }
        document.getElementById("checkoutBtn").addEventListener("click", async function () {
             // Show the loader
             $('#loader').show();

             try {
                 // Get the activity ID
                 const activityId = document.getElementById("activityId").value;

                 // Validate activity ID
                 if (!activityId) {
                     throw new Error("Activity ID is missing.");
                 }

                 // Send the AJAX request
                 const response = await $.ajax({
                     type: "POST",
                     url: '@Url.Action("Checkout", "Partner")',
                     contentType: "application/json", // Specify JSON content type
                     data: JSON.stringify({ activityId: activityId }), // Serialize the data as JSON
                     headers: {
                         "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() // Add CSRF token
                     }
                 });
                 $('#checkoutModal').modal('hide');

                 if(response.success == true){
                     Swal.fire({
                         icon: "success",
                         title: '@SharedLocalizer["Payment"]',
                         text: response.message
                     }).then(() => {
                         window.location.href = response.redirectUrl;
                     });
                 }else{
                      if(response.amount){
                           // Show insufficient balance modal with two options
                           showInsufficientBalanceModal(response.message, response.amount);
                       }else{
                            Swal.fire({
                              icon: "error",
                              text: response.message
                          })
                       }

                 }
                 // Hide the modal and show success message

             } catch (error) {
                 // Show error message
                 $('#checkoutModal').modal('hide');

                 Swal.fire({
                     icon: "error",
                     text: error.responseJSON?.message || "An error occurred while processing your request."
                 });
             } finally {
                 // Hide the loader
                 $('#loader').hide();
             }
         });

         // Function to show insufficient balance modal
         function showInsufficientBalanceModal(message, amount) {
             document.getElementById('insufficientBalanceMessage').textContent = message;
             document.getElementById('activityPrice').textContent = amount;
             $('#insufficientBalanceModal').modal('show');
         }

         // Handle proceed to payment button click
         document.getElementById("proceedToPaymentBtn").addEventListener("click", async function () {
             // Show the loader
             $('#loader').show();
             $('#insufficientBalanceModal').modal('hide');

             try {
                 // Get the activity ID and price
                 const activityId = document.getElementById("activityId").value;
                 const activityPrice = document.getElementById('activityPrice').textContent;

                 // Validate activity ID
                 if (!activityId) {
                     throw new Error("Activity ID is missing.");
                 }

                 // Send the AJAX request to create payment transaction
                 const response = await $.ajax({
                     type: "POST",
                     url: '@Url.Action("CreateActivityPayment", "Partner")',
                     contentType: "application/json",
                     data: JSON.stringify({ activityId: activityId }),
                     headers: {
                         "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                     }
                 });

                 if(response.success == true){
                     // Redirect to payment page
                     window.location.href = response.paymentUrl;
                 } else {
                     Swal.fire({
                         icon: "error",
                         text: response.message
                     });
                 }

             } catch (error) {
                 // Show error message
                 Swal.fire({
                     icon: "error",
                     text: error.responseJSON?.message || "An error occurred while processing your request."
                 });
             } finally {
                 // Hide the loader
                 //$('#loader').hide();
             }
         });

         $(document).ready(function () {
             const searchTerm = "@Model.SearchTerm";

             if (searchTerm) {
                 $("#categoryDropdown").val(decodeURIComponent(searchTerm));
             }

             $("#categoryDropdown").on("change", function () {
                 const selectedValue = $(this).val();

                 // Add loading effect
                 $(this).addClass('loading-shimmer');

                 // Construct the URL with proper encoding for the query parameter
                 const redirectUrl = `/Partner/Activities?SearchTerm=${encodeURIComponent(selectedValue)}`;
                 window.location.href = redirectUrl;
             });

             // Add ripple effect to cards
             $('.card-hover').on('click', function(e) {
                 const ripple = $('<div class="ripple"></div>');
                 $(this).append(ripple);

                 const rect = this.getBoundingClientRect();
                 const size = Math.max(rect.width, rect.height);
                 ripple.css({
                     width: size + 'px',
                     height: size + 'px',
                     left: (e.clientX - rect.left - size / 2) + 'px',
                     top: (e.clientY - rect.top - size / 2) + 'px'
                 });

                 setTimeout(() => {
                     ripple.remove();
                 }, 600);
             });
         });
    </script>
}
