<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Update="Disclaimer\Disclaimer.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Disclaimer.resx</DependentUpon>
    </Compile>
    <Compile Update="LegalNotice\LegalNotice.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>LegalNotice.resx</DependentUpon>
    </Compile>
    <Compile Update="PrivacyPolicy\PrivacyPolicy.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>PrivacyPolicy.resx</DependentUpon>
    </Compile>
    <Compile Update="SharedResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>SharedResource.resx</DependentUpon>
    </Compile>
    <Compile Update="TermsAndConditions\TermsAndConditions.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>TermsAndConditions.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Disclaimer\Disclaimer.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Disclaimer.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="LegalNotice\LegalNotice.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>LegalNotice.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="PrivacyPolicy\PrivacyPolicy.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>PrivacyPolicy.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="SharedResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>SharedResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="TermsAndConditions\TermsAndConditions.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>TermsAndConditions.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
