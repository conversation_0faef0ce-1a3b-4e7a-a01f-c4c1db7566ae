﻿using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Api_s.Interfaces;
using TaskDotNet.Models;
using Admin.TaskDotNet.Dtos;
using TaskDotNet.Helper;
using AutoMapper;
using Comman.Services.Interfaces;
using System.Text;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using TaskDotNet.Localization;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.Globalization;
using Comman.Helper.Extensions;

namespace TaskDotNet.Api_s.Services
{
    public class MovingAndCleaningApiService : IMovingAndCleaningApiService
    {
        private readonly ApplicationDbContext context;
        private readonly IMapper _mapper;
        private readonly IMailService _mailService;
        private readonly OrderNumberService _orderNumberService;
        private readonly IEmailHtmlTemplateService _emailHtmlTemplateService;

        public MovingAndCleaningApiService(IMapper mapper, IMailService mailService, ApplicationDbContext context, IEmailHtmlTemplateService emailHtmlTemplateService)
        {
            _mapper = mapper;
            _mailService = mailService;
            _orderNumberService = new OrderNumberService();
            this.context = context;
            _emailHtmlTemplateService = emailHtmlTemplateService;
        }

        public async Task<Activity> AddInventoryToMovingAndCleaningAsync(AddInventoryWithMovingAndCleaningRequest request)
        {
            var companyData = context.Company.Select(m=>new { m.Offers_MovClean,m.Price_MovClean }).FirstOrDefault(x => true);

            var activity = _mapper.Map<Activity>(request.MovingDto);

            var inventoryItems = _mapper.Map<List<ActivityInventoryItem>>(request.InventoryItems);

            activity.ActivityType = ActivityType.MovingAndCleaning;
            activity.InventoryItems = inventoryItems;
            activity.Preis = companyData.Price_MovClean;
            activity.PaymentCount = companyData.Offers_MovClean;
            activity.Source = "TaskDotNet";

            activity = await Create(activity);

            activity.OrderNr = _orderNumberService.GenerateOrderNumber(activity.ActivityType, activity.Id);
            await Update(activity);

            request.MovingDto.OrderNr = activity.OrderNr;
            await SendThankYouEmailAsync(activity, request.Lang);



            return activity;
        }

        public async Task<Activity> Create(Activity activity)
        {
            await context.Activities.AddAsync(activity);
            context.SaveChanges();
            return activity;
        }

        public async Task<Activity> CreateMovingAndCleaningActivityAsync(MovingCleaningDto dto, string lang)
        {
            var companyData = context.Company.Select(m => new { m.Offers_MovClean, m.Price_MovClean }).FirstOrDefault(x => true);

            var data = _mapper.Map<Activity>(dto);
            data.ActivityType = ActivityType.MovingAndCleaning;
            data.Preis = companyData.Price_MovClean;
            data.PaymentCount = companyData.Offers_MovClean;
            data.Source = "TaskDotNet";

            data = await Create(data);
            data.OrderNr = _orderNumberService.GenerateOrderNumber(data.ActivityType, data.Id);
            await Update(data);

            dto.OrderNr = data.OrderNr;

            await SendThankYouEmailAsync(data, lang);

            return data;
        }

        public async Task Update(Activity activity)
        {
            context.Activities.Update(activity);
            await context.SaveChangesAsync();
        }


        private async Task SendThankYouEmailAsync(Activity data, string lang)
        {
            string body = _emailHtmlTemplateService.GetCustomerThankYouTemplate(data.Salute.GetDisplayName(), data.Name, lang);
            MailRequest mailRequest = new()
            {
                ToEmail = data.Email,
                Subject = "TaskDotNet",
                Body = body
            };
            await _mailService.SendEmailAsync(mailRequest, default);
        }
    }
}
