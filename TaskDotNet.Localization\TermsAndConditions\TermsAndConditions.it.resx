<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="TermsAndConditionsTitle" xml:space="preserve">
    <value>Condizioni generali (CG)</value>
  </data>
  <data name="TermsAndConditionsIntro" xml:space="preserve">
    <value>Le presenti condizioni generali regolano il rapporto giuridico tra Lightsoft GmbH e i suoi clienti. Per semplicità, in queste condizioni si utilizza il termine "contratto/", indipendentemente dal tipo di servizio fornito.</value>
  </data>
  <data name="ContractDefinitionTitle" xml:space="preserve">
    <value>Definizione del contratto</value>
  </data>
  <data name="ContractDefinitionText" xml:space="preserve">
    <value>Con il termine « contratto » si intende il consenso del cliente all'utilizzo della piattaforma TaskDotNet per pubblicare la propria esigenza di un determinato servizio e ricevere offerte da potenziali fornitori di servizi. Analogamente, i partner registrano le proprie aziende su questa piattaforma per interagire con le richieste dei clienti e mettersi in contatto con loro. Questa collaborazione, sia tramite la registrazione che la pubblicazione di una richiesta, costituisce un contratto vincolante tra l'utente e la piattaforma.</value>
  </data>
  <data name="ApplicableConditionsTitle" xml:space="preserve">
    <value>Condizioni applicabili</value>
  </data>
  <data name="ApplicableConditionsText" xml:space="preserve">
    <value>Le presenti condizioni generali della Lightsoft GmbH si applicano a tutte le persone che si registrano come partner per gestire le richieste dei clienti tramite questa piattaforma. La registrazione è considerata un contratto tra l'azienda e il partner, che entra in vigore con l'approvazione della registrazione da parte di Lightsoft sulla piattaforma.</value>
  </data>
  <data name="ContractValidityTitle" xml:space="preserve">
    <value>Validità del contratto</value>
  </data>
  <data name="ContractValidityText" xml:space="preserve">
    <value>Qualora una disposizione delle presenti condizioni generali sia, in tutto o in parte, invalida o nulla, la validità del contratto e delle restanti disposizioni non sarà compromessa. La disposizione invalida sarà sostituita da una norma che si avvicini il più possibile, in modo legalmente ammissibile, allo scopo giuridico ed economico della disposizione originaria.</value>
  </data>
  <data name="JurisdictionTitle" xml:space="preserve">
    <value>Foro competente</value>
  </data>
  <data name="JurisdictionText" xml:space="preserve">
    <value>Per tutte le controversie derivanti dal presente contratto, è competente il tribunale svizzero, salvo ove vigano fori giurisdizionali obbligatori previsti dalla legge. Si applica esclusivamente il diritto svizzero a tutte le disposizioni contrattuali, inclusa la presentazione delle richieste di servizio da parte dei clienti tramite la piattaforma e la loro accettazione da parte dei partner mediante acquisto tramite TaskDotNet.</value>
  </data>
  <data name="ConditionsValidityTitle" xml:space="preserve">
    <value>Validità delle condizioni</value>
  </data>
  <data name="ConditionsValidityText" xml:space="preserve">
    <value>Le presenti condizioni generali sono valide a partire dal 1° giugno 2024 e cessano di essere valide con la pubblicazione di una nuova versione sulla piattaforma. Le conferme, dichiarazioni o reclami scritti sono legalmente vincolanti se inviati per posta, e-mail, SMS o WhatsApp.</value>
  </data>
  <data name="ContractingPartiesTitle" xml:space="preserve">
    <value>Parti contraenti</value>
  </data>
  <data name="ContractingPartiesText" xml:space="preserve">
    <value>Le parti contraenti ai sensi delle presenti condizioni generali sono sia l'ospite (cliente o partner) che la Lightsoft GmbH.</value>
  </data>
  <data name="ScopeOfServicesTitle" xml:space="preserve">
    <value>Ambito delle prestazioni</value>
  </data>
  <data name="ScopeOfServicesText" xml:space="preserve">
    <value>L'ambito delle prestazioni concordato è definito dall'incarico pubblicato individualmente dal cliente. Gli incarichi nei settori del trasporto, della pulizia, della pittura, dell'intonacatura e di altri lavori artigianali comuni devono essere registrati esclusivamente tramite la piattaforma TaskDotNet.com.</value>
  </data>
  <data name="OrderRulesTitle" xml:space="preserve">
    <value>Regole per la registrazione e la prenotazione degli incarichi</value>
  </data>
  <data name="OrderRulesText" xml:space="preserve">
    <value>Il cliente che pubblica un incarico sulla piattaforma e il partner che prenota e acquista tale incarico sono considerati ciascuno come parte contrattuale responsabile ai sensi delle presenti condizioni.</value>
  </data>
  <data name="PersonalDataEntryTitle" xml:space="preserve">
    <value>Inserimento dei dati personali del cliente (committente)</value>
  </data>
  <data name="PersonalDataEntryText" xml:space="preserve">
    <value>Il committente è tenuto a fornire tutte le informazioni rilevanti per la richiesta, nonché i propri dati di contatto di base, in modo completo e veritiero. I dati di contatto di base includono almeno il nome completo, il codice postale, il luogo di residenza e un indirizzo e-mail valido. Ulteriori dati personali, come il numero di telefono e il nome della via, sono generalmente utili per garantire una comunicazione fluida e una corretta gestione della richiesta. Tuttavia, Lightsoft GmbH comprende che alcuni committenti possano preferire non fornire tali dati per motivi personali. La piattaforma informa in modo trasparente il partner interessato, per ogni richiesta, dei dati personali forniti dal committente. Il partner decide autonomamente se accettare o rifiutare la richiesta sulla base delle informazioni disponibili. La piattaforma verifica almeno la validità dell'indirizzo e-mail fornito. Se tale requisito non è soddisfatto, la richiesta sarà considerata incompleta e non verrà elaborata. La verifica dei dati di contatto viene effettuata nell'interesse dei partner, che forniscono servizi a fronte della ricezione di richieste, e ha lo scopo di evitare richieste non valide o inutilizzabili.</value>
  </data>
  <data name="PartnerRightsDisclaimerTitle" xml:space="preserve">
    <value>Esclusione di responsabilità e diritti del partner</value>
  </data>
  <data name="PartnerRightsDisclaimerText" xml:space="preserve">
    <value>Lightsoft GmbH non si assume alcuna responsabilità in merito all'ottenimento, da parte del partner, del consenso del cliente per l'esecuzione del servizio richiesto, né riguardo all'eventuale instaurarsi di una comunicazione tra le parti. La decisione finale sull'accettazione dell'offerta di prezzo e sulla scelta del fornitore del servizio spetta esclusivamente al cliente. Il partner non ha diritto al rimborso del prezzo di acquisto di una richiesta se non ha ottenuto il consenso del cliente o non è stato possibile stabilire un contatto con quest'ultimo. Inoltre, non è consentito al partner mettere in dubbio l'accuratezza dei dati forniti dal cliente. La piattaforma attribuisce grande importanza alla verifica della validità dell'indirizzo e-mail del cliente prima di rendere disponibile la richiesta ai partner.</value>
  </data>
  <data name="OrderConfirmationTitle" xml:space="preserve">
    <value>Conferma dell'incarico e responsabilità del cliente</value>
  </data>
  <data name="OrderConfirmationText" xml:space="preserve">
    <value>Per confermare un incarico, il cliente deve rispondere all'e-mail inviata dalla piattaforma durante la registrazione dell'incarico. In assenza di risposta, l'incarico è considerato incompleto. In tal caso, il cliente non ha diritto ad alcun servizio e non può in alcun modo esprimersi sulla piattaforma in modo da danneggiare la reputazione o gli interessi dell'azienda.</value>
  </data>
  <data name="OrderEntryTimesTitle" xml:space="preserve">
    <value>Orari per la registrazione degli incarichi</value>
  </data>
  <data name="OrderEntryTimesText" xml:space="preserve">
    <value>La piattaforma TaskDotNet è disponibile 24 ore su 24, 7 giorni su 7 per la registrazione degli incarichi. La validità dell'indirizzo e-mail viene verificata per ogni incarico al fine di garantire una comunicazione affidabile.</value>
  </data>
  <data name="OrderForwardingTitle" xml:space="preserve">
    <value>Trasmissione delle richieste ai partner</value>
  </data>
  <data name="OrderForwardingText" xml:space="preserve">
    <value>Dopo l'invio corretto di una richiesta, questa viene immediatamente inoltrata ai partner registrati. I primi quattro partner hanno il diritto di prenotare e acquistare la richiesta a pagamento. Successivamente, la richiesta viene chiusa e non più visualizzata. Se il cliente desidera ritirare o annullare la richiesta dopo la pubblicazione e l'acquisto da parte dei partner, si assume tutte le obbligazioni finanziarie derivanti — in particolare eventuali richieste di rimborso da parte dei partner per i pagamenti effettuati in relazione alla richiesta. Lightsoft GmbH non si assume alcuna responsabilità o obbligo nei confronti dei partner in merito a ciò.</value>
  </data>
  <data name="PartnerRightsTitle" xml:space="preserve">
    <value>Diritti e obblighi dei partner</value>
  </data>
  <data name="PartnerRightsText" xml:space="preserve">
    <value>Al momento dell'acquisto di una richiesta, il partner riceve tutti i dettagli dell'ordine, inclusi i dati di contatto del cliente – almeno l'indirizzo e-mail verificato. Dopo l'acquisto, il partner non ha diritto ad annullare la richiesta né a richiedere un rimborso dell'importo pagato.</value>
  </data>
  <data name="PartnerCustomerCoordinationTitle" xml:space="preserve">
    <value>Coordinamento tra partner e cliente</value>
  </data>
  <data name="PartnerCustomerCoordinationText" xml:space="preserve">
    <value>L'accordo relativo all'esecuzione del servizio, comprese eventuali visite in loco, avviene esclusivamente e direttamente tra il partner e il cliente, al di fuori della piattaforma TaskDotNet. Tali accordi sono vincolanti per entrambe le parti. Il ruolo della piattaforma si limita esclusivamente alla ricezione e al inoltro della richiesta.</value>
  </data>
  <data name="PricesCurrencyTitle" xml:space="preserve">
    <value>Prezzi e valuta</value>
  </data>
  <data name="PricesCurrencyText" xml:space="preserve">
    <value>I prezzi dei contratti sono mostrati ai partner in modo chiaro e trasparente. Le transazioni vengono effettuate esclusivamente in franchi svizzeri (CHF).</value>
  </data>
  <data name="LiabilityTitle" xml:space="preserve">
    <value>Responsabilità e risarcimento</value>
  </data>
  <data name="LiabilityText" xml:space="preserve">
    <value>Il cliente si assume la piena responsabilità per la correttezza e completezza dei dati inseriti durante la registrazione dell'ordine. Il partner è responsabile della presentazione di un'offerta di prezzo adeguata e dell'esecuzione corretta e puntuale del servizio concordato senza causare danni al cliente. Il partner esecutore è l'unico responsabile nei confronti del cliente per tutti i danni o le perdite derivanti dal proprio comportamento, da quello dei propri dipendenti o di terzi incaricati. Né queste persone né il partner stesso possono ritenere Lightsoft GmbH responsabile in alcun modo—direttamente o indirettamente—per danni o difetti di esecuzione. Lightsoft GmbH declina espressamente qualsiasi responsabilità legale o dichiarazione in caso di furto o danneggiamento di materiali o proprietà legati al servizio. La protezione e l'assicurazione di tali oggetti, così come eventuali risarcimenti, sono esclusivamente a carico del partner esecutore.</value>
  </data>
</root>
