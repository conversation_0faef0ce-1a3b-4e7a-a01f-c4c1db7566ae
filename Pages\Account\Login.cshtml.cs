﻿// Licensed to the .NET Foundation under one or more agreements.
// The .NET Foundation licenses this file to you under the MIT license.
#nullable disable

using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using TaskDotNet.Helper;
using TaskDotNet.Models;

namespace TaskDotNet.Areas.Identity.Pages.Account
{
    public class LoginModel : PageModel
    {
        private readonly SignInManager<Partner> _signInManager;
        private readonly UserManager<Partner> _userManager;
        private readonly ILogger<LoginModel> _logger;

        public LoginModel(SignInManager<Partner> signInManager, ILogger<LoginModel> logger, UserManager<Partner> userManager)
        {
            _signInManager = signInManager;

            _logger = logger;
            _userManager = userManager;
        }

        [BindProperty]
        public InputModel Input { get; set; }

        public string ReturnUrl { get; set; }

        [TempData]
        public string ErrorMessage { get; set; }

        public class InputModel
        {

            [Required]
            [EmailAddress]
            public string Email { get; set; }

            [Required]
            [DataType(DataType.Password)]
            public string Password { get; set; }

            [Display(Name = "Remember me?")]
            public bool RememberMe { get; set; }
        }

        public async Task OnGetAsync(string returnUrl = null)
        {
            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                ModelState.AddModelError(string.Empty, ErrorMessage);
            }

            returnUrl ??= Url.Content("~/Home/Index");

            await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);

            ReturnUrl = returnUrl;
        }

        public async Task<IActionResult> OnPostAsync(string returnUrl = null)
        {
            // Set default returnUrl if not provided
            returnUrl ??= Url.Content("~/Home/Index");

            // Validate the model state before attempting login
            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Attempt to sign in the user
            var result = await _signInManager.PasswordSignInAsync(Input.Email, Input.Password, Input.RememberMe, lockoutOnFailure: true);

            if (result.Succeeded)
            {
                var user = await _userManager.FindByEmailAsync(Input.Email);
                HttpContext.Session.SetString("IsCompletedData", user.IsCompletedData.ToString());
                 
                SetBlockedStatusIfUIDPreviouslyBlocked(user);

                return LocalRedirect(returnUrl);
            }

            if (result.IsLockedOut)
            {
                
                return RedirectToPage("./Lockout");
            }

            if (result.IsNotAllowed)
            {
                
                // Redirect to confirmation page if email is not confirmed
                return RedirectToPage("SendEmailConfirmation", new { email = Input?.Email });
            }

            // Handle invalid login attempt (wrong password, etc.)
            AddLocalizedModelError(ModelState, "InvalidLogin");

            return Page();
        }

        private void SetBlockedStatusIfUIDPreviouslyBlocked(Partner user)
        {
            if (!user.IsCompletedData) return;

            var userWithSameUID = _userManager.Users.AsNoTracking().FirstOrDefault(u => u.UID == user.UID);
            if (userWithSameUID != null && userWithSameUID.Status == PartnerStatus.Blocked)
            {
                user.Status = PartnerStatus.Blocked;
                user.Evaluation = PartnerEvaluation.Useless;
                _userManager.UpdateAsync(user).GetAwaiter().GetResult();
            }
        }

        private void AddLocalizedModelError(ModelStateDictionary modelState, string messageKey)
        {
            var culture = CultureInfo.CurrentUICulture.Name;
            var localizedMessage = GetLocalizedMessage(culture, messageKey);
            modelState.AddModelError(string.Empty, localizedMessage);
        }

        private string GetLocalizedMessage(string culture, string messageKey)
        {
            var messages = new Dictionary<string, Dictionary<string, string>>
            {
                { "en-US", new Dictionary<string, string>
                    {
                        { "InvalidLogin", "Invalid login attempt." }
                    }
                },
                { "de-DE", new Dictionary<string, string>
                    {
                        { "InvalidLogin", "Ungültiger Anmeldeversuch." }
                    }
                },
                { "it-IT", new Dictionary<string, string>
                    {
                        { "InvalidLogin", "Tentativo di accesso non valido." }
                    }
                },
                { "fr-FR", new Dictionary<string, string>
                    {
                        { "InvalidLogin", "Tentative de connexion invalide." }
                    }
                }
            };

            // Fallback to English if culture not found
            if (!messages.ContainsKey(culture))
                culture = "en-US";

            return messages[culture].TryGetValue(messageKey, out var message)
                ? message
                : "Message not found.";
        }


    }
}
