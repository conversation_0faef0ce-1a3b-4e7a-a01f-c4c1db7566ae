﻿// Licensed to the .NET Foundation under one or more agreements.
// The .NET Foundation licenses this file to you under the MIT license.
#nullable disable

using Comman.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using TaskDotNet.Helper;
using TaskDotNet.Models;

namespace TaskDotNet.Areas.Identity.Pages.Account
{
    [AllowAnonymous]
    public class SendEmailConfirmationModel : PageModel
    {
        private readonly UserManager<Partner> _userManager;
        private readonly IMailService _emailSender;
        private readonly IEmailHtmlTemplateService _emailHtmlTemplateService;
        private readonly IOTPVerificationService _OTPVerificationService;
        public SendEmailConfirmationModel(UserManager<Partner> userManager, IMailService emailSender, IEmailHtmlTemplateService emailHtmlTemplateService, IOTPVerificationService oTPVerificationService)
        {
            _userManager = userManager;
            _emailSender = emailSender;
            _emailHtmlTemplateService = emailHtmlTemplateService;
            _OTPVerificationService = oTPVerificationService;
        }


        [BindProperty]
        public string Email { get; set; }
        public string ReturnUrl { get; set; }

        public async Task<IActionResult> OnGetAsync(string email)
        {
            if (email == null)
            {
                return RedirectToPage("/Index");
            }

            // Check if this is a temporary registration (no user created yet)
            var tempEmail = HttpContext.Session.GetString("TempRegistration_Email");
            if (tempEmail == email)
            {
                Email = email;
                return Page();
            }

            // Check for existing user (for other flows like password reset)
            var user = await _userManager.FindByEmailAsync(email);
            if (user == null)
            {
                return NotFound($"Unable to load user with email '{email}'.");
            }

            Email = email;
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Check if this is a temporary registration
            var tempEmail = HttpContext.Session.GetString("TempRegistration_Email");
            if (tempEmail == Email)
            {
                // This is a new registration - proceed with OTP for temporary registration
                var currentLangeCode = System.Globalization.CultureInfo.CurrentCulture.TwoLetterISOLanguageName;

                var otp = await _OTPVerificationService.CreateAsync(Email);

                string body = await _emailHtmlTemplateService.GetOTPTemplateAsync(Email, otp.OTP, currentLangeCode);
                MailRequest mailRequest = new MailRequest
                {
                    ToEmail = Email,
                    Subject = "TaskDotNet",
                    Body = body,
                };
                await _emailSender.SendEmailAsync(mailRequest);

                return RedirectToPage("RegisterConfirmation", new { email = Email });
            }

            // Check for existing user (for other flows)
            var user = await _userManager.FindByEmailAsync(Email);
            if (user == null)
            {
                ModelState.AddModelError(string.Empty, $"Unable to load user with email '{Email}'.");
                return Page();
            }

            var currentLangeCode2 = System.Globalization.CultureInfo.CurrentCulture.TwoLetterISOLanguageName;

            var otp2 = await _OTPVerificationService.CreateAsync(Email);

            string body2 = await _emailHtmlTemplateService.GetOTPTemplateAsync(Email, otp2.OTP, currentLangeCode2);
            MailRequest mailRequest2 = new MailRequest
            {
                ToEmail = user.Email,
                Subject = "TaskDotNet",
                Body = body2,
            };
            await _emailSender.SendEmailAsync(mailRequest2);

            return RedirectToPage("RegisterConfirmation", new { email = Email });
        }
    }
}
