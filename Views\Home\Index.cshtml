﻿@using TaskDotNet.Helper.Extensions
@model HomeViewModel
@{
    ViewData["Title"] = "Home Page";
}


<div class="content-wrapper">
    <!-- Content -->

    <div class="container-fluid flex-grow-1 ">
        <div class="row">
            <div class="col-lg-12 mb-4 order-0 p-0">
                <div class="card" style="">
                    <div class="d-flex align-items-center row">
                        <div class="col-sm-6">
                            <div class="card-body">
                                <h2 class="card-title text-primary" style="font-weight: 800;">
                                    @Html.Raw(SharedLocalizer["Process an order"])
                                </h2>

                            </div>
                        </div>
                        <div class="col-sm-6 text-center text-sm-end">
                            <div class="card-body pb-0 px-0 pt-0">
                                <img src="~/Dashboard/assets/img/Dashboard1.png"
                                     height="140" class="w-100"
                                     alt="View Badge User"
                                     data-app-dark-img="illustrations/man-with-laptop-dark.png"
                                     data-app-light-img="illustrations/man-with-laptop-light.png" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div class="align-content-center d-flex row" style="background-image:url('/Dashboard/assets/img/Dashboard2.jpeg');background-position: left;background-size: cover; min-height:700px">
            <div class="px-5 row justify-content-center col-12">
                <p class="col-md-10" style="font-size: 35px; font-weight: 700; color: #816106; line-height: 1.5;text-align:center;">
                    @SharedLocalizer["PartnerDashboardMessage"]
                </p>
            </div>
            <div class="row col-md-8 ms-3 align-items-center">
                <img src="~/dashboard/assets/img/dashboard_01.png" style="width:80px;"/>
                <p class="col-md-10 h4">
                    @SharedLocalizer["Partner_Dashboard01"]
                </p>
            </div>

            <div class="row col-md-8 ms-3 align-items-center">
                <img src="~/dashboard/assets/img/dashboard_02.png" style="width:80px;"/>
                <p class="col-md-10 h4">
                    @SharedLocalizer["Partner_Dashboard02"]
                </p>
            </div>

            <div class="row col-md-8 ms-3 align-items-center">
                <img src="~/dashboard/assets/img/dashboard_03.png" style="width:80px;"/>
                <p class="col-md-10 h4">
                    @SharedLocalizer["Partner_Dashboard03"]
                </p>
            </div>
        </div>
    </div>
</div>


@if (TempData["ModelMessage"] != null)
{
    <div class="modal fade" id="StopModal" tabindex="-1" role="dialog" aria-hidden="true" style="display: block;">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body d-flex justify-content-center align-items-center flex-column">
                    <img src="/Dashboard/assets/img/Stop.png" alt="Stop" />
                    <h3 class="text-center my-10">@TempData["ModelMessage"]</h3>
                    <button type="button" class="btn btn-lg btn-primary px-11" style="border-radius: 0;" data-bs-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Automatically show the modal when the page loads
        document.addEventListener('DOMContentLoaded', function () {
            var modal = new bootstrap.Modal(document.getElementById('StopModal'));
            modal.show();
        });
    </script>
}

