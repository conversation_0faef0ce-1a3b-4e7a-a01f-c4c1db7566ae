﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Account" xml:space="preserve">
    <value>Compte</value>
  </data>
  <data name="Account Data" xml:space="preserve">
    <value>Détails du compte</value>
  </data>
  <data name="Account Settings" xml:space="preserve">
    <value>Paramètres du compte</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actes</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Actif</value>
  </data>
  <data name="Activites setup" xml:space="preserve">
    <value>Ajuster les paramètres d’activité</value>
  </data>
  <data name="Activities List" xml:space="preserve">
    <value>Liste des activités</value>
  </data>
  <data name="New Activities" xml:space="preserve">
    <value>Nouvelles activités</value>
  </data>
  <data name="Mark as Checked" xml:space="preserve">
    <value>Marquer comme vérifié</value>
  </data>
  <data name="Are you sure you want to mark this activity as checked?" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir marquer cette activité comme vérifiée?</value>
  </data>
  <data name="Delete Activity" xml:space="preserve">
    <value>Supprimer l'activité</value>
  </data>
  <data name="Are you sure you want to delete this activity? This action cannot be undone!" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer cette activité? Cette action ne peut pas être annulée!</value>
  </data>
  <data name="No New Activities" xml:space="preserve">
    <value>Aucune nouvelle activité</value>
  </data>
  <data name="All activities have been checked." xml:space="preserve">
    <value>Toutes les activités ont été vérifiées.</value>
  </data>
  <data name="ActivityType" xml:space="preserve">
    <value>Type d'activité</value>
  </data>
  <data name="MovingDate" xml:space="preserve">
    <value>Date de déménagement</value>
  </data>
  <data name="Kanton" xml:space="preserve">
    <value>Canton</value>
  </data>
  <data name="Activity" xml:space="preserve">
    <value>Enquête</value>
  </data>
  <data name="ActivityDetails" xml:space="preserve">
    <value>Détails</value>
  </data>
  <data name="ActivityDetailsTitle" xml:space="preserve">
    <value>La demande sélectionnée</value>
  </data>
  <data name="Add to archive" xml:space="preserve">
    <value>Ajouter aux archives</value>
  </data>
  <data name="AddAmount" xml:space="preserve">
    <value>Ajouter un montant</value>
  </data>
  <data name="AdditionalServices" xml:space="preserve">
    <value>Prestations supplémentaires</value>
  </data>
  <data name="Additive" xml:space="preserve">
    <value>Ajout</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>adresse</value>
  </data>
  <data name="Administrator" xml:space="preserve">
    <value>Administrateur</value>
  </data>
  <data name="AdvertisingList" xml:space="preserve">
    <value>Annuaire des secteurs à des fins publicitaires</value>
  </data>
  <data name="Albanian" xml:space="preserve">
    <value>albanais</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Tout</value>
  </data>
  <data name="All Activity" xml:space="preserve">
    <value>Toutes les activités</value>
  </data>
  <data name="All Paertners" xml:space="preserve">
    <value>Liste des partenaires</value>
  </data>
  <data name="AlreadyPurchased" xml:space="preserve">
    <value>Vous avez déjà acheté cette commande. Un nouvel achat n'est pas possible.</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Montant</value>
  </data>
  <data name="AmountMustBeGreaterThanZero" xml:space="preserve">
    <value>Le montant doit être supérieur à zéro.</value>
  </data>
  <data name="Apartment" xml:space="preserve">
    <value>Appartement</value>
  </data>
  <data name="Approx." xml:space="preserve">
    <value>Env.</value>
  </data>
  <data name="Arabic" xml:space="preserve">
    <value>arabe</value>
  </data>
  <data name="Archive" xml:space="preserve">
    <value>Archive</value>
  </data>
  <data name="Archive Content" xml:space="preserve">
    <value>Archiver le contenu</value>
  </data>
  <data name="Archive entry" xml:space="preserve">
    <value>Entrée d'archive :</value>
  </data>
  <data name="Are you sure you want to delete the selected entry?" xml:space="preserve">
    <value>Voulez-vous vraiment supprimer l'enregistrement sélectionné ?</value>
  </data>
  <data name="Area" xml:space="preserve">
    <value>zone</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>Voulez-vous procéder à la réinitialisation ?</value>
  </data>
  <data name="AssembleFurniture" xml:space="preserve">
    <value>Assembler des meubles</value>
  </data>
  <data name="AT_Austria" xml:space="preserve">
    <value>AT–Autriche</value>
  </data>
  <data name="Auszug" xml:space="preserve">
    <value>De</value>
  </data>
  <data name="Background" xml:space="preserve">
    <value>arrière-plan</value>
  </data>
  <data name="BackToList" xml:space="preserve">
    <value>Retour à la liste</value>
  </data>
  <data name="BadRequestMessage" xml:space="preserve">
    <value>Quelque chose s'est mal passé. Veuillez réessayer plus tard.</value>
  </data>
  <data name="BadRequestTitle" xml:space="preserve">
    <value>Demande incorrecte ⚠️</value>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>équilibre</value>
  </data>
  <data name="BalanceRechargeHistory" xml:space="preserve">
    <value>Historique de recharge du solde</value>
  </data>
  <data name="Balcony" xml:space="preserve">
    <value>balcon</value>
  </data>
  <data name="Bank" xml:space="preserve">
    <value>banque</value>
  </data>
  <data name="Bank Name" xml:space="preserve">
    <value>Nom de la banque</value>
  </data>
  <data name="Basement, Cellar" xml:space="preserve">
    <value>cave</value>
  </data>
  <data name="Basic Cleaning" xml:space="preserve">
    <value>Nettoyage de chantier</value>
  </data>
  <data name="Blocked" xml:space="preserve">
    <value>Bloqué</value>
  </data>
  <data name="Both" xml:space="preserve">
    <value>Les deux ensemble.</value>
  </data>
  <data name="BoxCity" xml:space="preserve">
    <value>Code postal / Ville</value>
  </data>
  <data name="Boxes" xml:space="preserve">
    <value>Boîtes en carton</value>
  </data>
  <data name="Branch" xml:space="preserve">
    <value>Secteur</value>
  </data>
  <data name="Building Cleaning" xml:space="preserve">
    <value>Nettoyage de bâtiment</value>
  </data>
  <data name="Business" xml:space="preserve">
    <value>Entreprise</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="Card number" xml:space="preserve">
    <value>Numéro de carte</value>
  </data>
  <data name="Cardboard boxes" xml:space="preserve">
    <value>Boîtes en carton</value>
  </data>
  <data name="CarpetCleaning" xml:space="preserve">
    <value>Nettoyage de tapis</value>
  </data>
  <data name="Carpets" xml:space="preserve">
    <value>Tapis</value>
  </data>
  <data name="Cartons" xml:space="preserve">
    <value>Cartons de déménagement</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>catégorie</value>
  </data>
  <data name="Category (room)" xml:space="preserve">
    <value>Catégorie (chambre)</value>
  </data>
  <data name="Cellar" xml:space="preserve">
    <value>cave</value>
  </data>
  <data name="Change password" xml:space="preserve">
    <value>changer le mot de passe</value>
  </data>
  <data name="ChangePasswordHint" xml:space="preserve">
    <value>Laissez ce champ vide si aucun changement n’est souhaité.</value>
  </data>
  <data name="Checkout" xml:space="preserve">
    <value>Caisse</value>
  </data>
  <data name="CheckoutRedirectMessage" xml:space="preserve">
    <value>Vous êtes sur le point de recharger votre crédit. Voulez-vous continuer ?</value>
  </data>
  <data name="Choose email content" xml:space="preserve">
    <value>Sélectionner un message</value>
  </data>
  <data name="ChooseActivity" xml:space="preserve">
    <value>Choisissez l'activité appropriée</value>
  </data>
  <data name="CH_Switzerland" xml:space="preserve">
    <value>CH-Suisse</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Ville</value>
  </data>
  <data name="Cleaning" xml:space="preserve">
    <value>nettoyage</value>
  </data>
  <data name="CleaningAndHandover" xml:space="preserve">
    <value>Nettoyage et remise d’un(e)</value>
  </data>
  <data name="CleaningDate" xml:space="preserve">
    <value>Date de nettoyage</value>
  </data>
  <data name="CleaningType" xml:space="preserve">
    <value />
  </data>
  <data name="Click buttons to filter by activity" xml:space="preserve">
    <value>Type de nettoyage</value>
  </data>
  <data name="Click to buy" xml:space="preserve">
    <value>Cliquez sur les boutons pour filtrer par activité</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Acheter</value>
  </data>
  <data name="Company Data" xml:space="preserve">
    <value>Données de l'entreprise</value>
  </data>
  <data name="Company Move" xml:space="preserve">
    <value>Déménagement d'entreprise</value>
  </data>
  <data name="Company Name" xml:space="preserve">
    <value>Nom de l'entreprise</value>
  </data>
  <data name="CompanyDetails" xml:space="preserve">
    <value>Détails de l’entreprise</value>
  </data>
  <data name="CompareAttribute_MustMatch" xml:space="preserve">
    <value>Les champs {0} et {1} doivent correspondre.</value>
  </data>
  <data name="CompleteProfileMessage" xml:space="preserve">
    <value>Veuillez compléter les informations de votre profil afin de finaliser correctement votre inscription !</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>confirmer</value>
  </data>
  <data name="Confirm New Password" xml:space="preserve">
    <value>Confirmez le nouveau mot de passe</value>
  </data>
  <data name="Confirm Password" xml:space="preserve">
    <value>Confirmez le mot de passe</value>
  </data>
  <data name="ConfirmationCode" xml:space="preserve">
    <value>Le code de vérification</value>
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value>Confirmé</value>
  </data>
  <data name="ConfirmMessage" xml:space="preserve">
    <value>Nous vous avons envoyé un code de confirmation par e-mail. Veuillez le saisir ici, s’il vous plaît.</value>
  </data>
  <data name="Construction Cleaning" xml:space="preserve">
    <value>Nettoyage de chantier</value>
  </data>
  <data name="Contact person" xml:space="preserve">
    <value>Personne de contact</value>
  </data>
  <data name="ContactDetails" xml:space="preserve">
    <value>Coordonnées</value>
  </data>
  <data name="ContactSubTitle" xml:space="preserve">
    <value>Informations détaillées sur la demande de contact</value>
  </data>
  <data name="ContactUs" xml:space="preserve">
    <value>Contactez-nous</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>Créer</value>
  </data>
  <data name="Create a room" xml:space="preserve">
    <value>Ajouter une pièce</value>
  </data>
  <data name="Create entry" xml:space="preserve">
    <value>Nouvelle entrée</value>
  </data>
  <data name="Create room content" xml:space="preserve">
    <value>Ajouter le contenu de la pièce</value>
  </data>
  <data name="Credit card" xml:space="preserve">
    <value>Carte de crédit</value>
  </data>
  <data name="Croatian" xml:space="preserve">
    <value>Croate</value>
  </data>
  <data name="Crook" xml:space="preserve">
    <value>Escroc</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>devise</value>
  </data>
  <data name="Current Balance" xml:space="preserve">
    <value>Solde actuel</value>
  </data>
  <data name="Current balance: CHF" xml:space="preserve">
    <value>Solde actuel : CHF</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>client</value>
  </data>
  <data name="CustomerFocus" xml:space="preserve">
    <value>Focus client</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Fiche d'information</value>
  </data>
  <data name="Data imported successfully." xml:space="preserve">
    <value>Les données ont été importées avec succès.</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="DateFrom" xml:space="preserve">
    <value>Supprimer des mouvements : de</value>
  </data>
  <data name="Daten reinigen" xml:space="preserve">
    <value>Nettoyer les données archivées</value>
  </data>
  <data name="DateTo" xml:space="preserve">
    <value>à</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="Delete record" xml:space="preserve">
    <value>Supprimer l'entrée</value>
  </data>
  <data name="Delete Selected" xml:space="preserve">
    <value>Supprimer les entrées sélectionnées</value>
  </data>
  <data name="Depending on the card type, you will find these in the marked position on the back of the card" xml:space="preserve">
    <value>Vous les trouverez selon le type de carte à l’endroit indiqué au verso de la carte.</value>
  </data>
  <data name="DE_Germany" xml:space="preserve">
    <value>DE-Allemagne</value>
  </data>
  <data name="Different" xml:space="preserve">
    <value>Différent</value>
  </data>
  <data name="DismantleFurniture" xml:space="preserve">
    <value>Démonter les meubles</value>
  </data>
  <data name="DismantleLamp" xml:space="preserve">
    <value>Démonter la lampe</value>
  </data>
  <data name="Disposal" xml:space="preserve">
    <value>Élimination</value>
  </data>
  <data name="Distance" xml:space="preserve">
    <value>distance</value>
  </data>
  <data name="Doors" xml:space="preserve">
    <value>Portes</value>
  </data>
  <data name="DoReset" xml:space="preserve">
    <value>Êtes-vous sûr ?</value>
  </data>
  <data name="Download" xml:space="preserve">
    <value>Télécharger</value>
  </data>
  <data name="ExportExcel" xml:space="preserve">
    <value>Exporter Excel</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Modifier</value>
  </data>
  <data name="Edit archive data" xml:space="preserve">
    <value>Modifier les données archivées</value>
  </data>
  <data name="Edit category (rooms)" xml:space="preserve">
    <value>Modifier la catégorie (pièces).</value>
  </data>
  <data name="Edit entry" xml:space="preserve">
    <value>Modifier l’adresse professionnelle</value>
  </data>
  <data name="Edit messages" xml:space="preserve">
    <value>Modifier les messages</value>
  </data>
  <data name="Edit objects" xml:space="preserve">
    <value>Modifier les objets</value>
  </data>
  <data name="Edit partner profile" xml:space="preserve">
    <value>Modifier le profil partenaire</value>
  </data>
  <data name="Edit Profile Data" xml:space="preserve">
    <value>Modifier les données du profil</value>
  </data>
  <data name="Edit room content" xml:space="preserve">
    <value>Modifier le contenu de la pièce</value>
  </data>
  <data name="efh" xml:space="preserve">
    <value>MI</value>
  </data>
  <data name="EightFloor" xml:space="preserve">
    <value>8e étage</value>
  </data>
  <data name="Einzug" xml:space="preserve">
    <value>Emménagement</value>
  </data>
  <data name="Electrician" xml:space="preserve">
    <value>Electrical work</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="Email confirm" xml:space="preserve">
    <value>E-mail confirmée</value>
  </data>
  <data name="Email senden" xml:space="preserve">
    <value>Courriels publicitaires</value>
  </data>
  <data name="Email texts" xml:space="preserve">
    <value>Préparer les textes de correspondance</value>
  </data>
  <data name="EmailAddressAttribute_ValidationError" xml:space="preserve">
    <value>Le champ {0} ne contient pas une adresse e-mail valide.</value>
  </data>
  <data name="EmailAlreadyExistsError" xml:space="preserve">
    <value>L’adresse e-mail existe déjà.</value>
  </data>
  <data name="EmailConfirmationMessage1" xml:space="preserve">
    <value>Tu as presque réussi !</value>
  </data>
  <data name="EmailConfirmationMessage2" xml:space="preserve">
    <value>Plus qu’une étape pour commencer !</value>
  </data>
  <data name="EmailConfirmationMessage3" xml:space="preserve">
    <value>Vérification de l’e-mail</value>
  </data>
  <data name="EmailConfirmationMessage4" xml:space="preserve">
    <value>Un e-mail contenant un code de confirmation va vous être envoyé.</value>
  </data>
  <data name="EmailConfirmationMessage5" xml:space="preserve">
    <value>Après l’envoi, veuillez saisir le code de confirmation pour vérifier votre identité.</value>
  </data>
  <data name="EmailSenden" xml:space="preserve">
    <value>Envoyer e-mail</value>
  </data>
  <data name="EmailSentSuccessfully" xml:space="preserve">
    <value>L’e-mail a été envoyé avec succès.</value>
  </data>
  <data name="EmailToCustomer" xml:space="preserve">
    <value>Email au client</value>
  </data>
  <data name="EmptyOrdersMessage" xml:space="preserve">
    <value>Veuillez réessayer plus tard, s’il vous plaît.</value>
  </data>
  <data name="EmptyOrdersTitle" xml:space="preserve">
    <value>Aucune commande n’est en cours pour le moment.</value>
  </data>
  <data name="EmptyPurchasedOrders" xml:space="preserve">
    <value>Vous n’avez encore acheté aucune demande.</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>Anglais</value>
  </data>
  <data name="Enter the ID number. a" xml:space="preserve">
    <value>Entrez le numéro d'identification. un</value>
  </data>
  <data name="EnterCompanyName" xml:space="preserve">
    <value>Entrez le nom de l'entreprise</value>
  </data>
  <data name="EnterYourEmail" xml:space="preserve">
    <value>Entrez votre adresse e-mail</value>
  </data>
  <data name="Evaluation" xml:space="preserve">
    <value>Évaluation</value>
  </data>
  <data name="Event Cleaning" xml:space="preserve">
    <value>Nettoyage événementiel</value>
  </data>
  <data name="Excellent" xml:space="preserve">
    <value>Excellent</value>
  </data>
  <data name="Excerpt from" xml:space="preserve">
    <value>Extrait de</value>
  </data>
  <data name="Exec. Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="ExecDate" xml:space="preserve">
    <value>Date d'exécution</value>
  </data>
  <data name="ExecutionDate" xml:space="preserve">
    <value>Date d'exécution</value>
  </data>
  <data name="Expiry Date" xml:space="preserve">
    <value>Date d'expiration</value>
  </data>
  <data name="External" xml:space="preserve">
    <value>Externe</value>
  </data>
  <data name="Final Cleaning" xml:space="preserve">
    <value>Nettoyage final</value>
  </data>
  <data name="FiveFloor" xml:space="preserve">
    <value>5e étage</value>
  </data>
  <data name="Flexibility" xml:space="preserve">
    <value>Flexibilité</value>
  </data>
  <data name="Flexible" xml:space="preserve">
    <value>flexibilité</value>
  </data>
  <data name="Floor" xml:space="preserve">
    <value>Étage</value>
  </data>
  <data name="Floor Cleaning" xml:space="preserve">
    <value>Nettoyage des sols</value>
  </data>
  <data name="FloorAndPanels" xml:space="preserve">
    <value>Sols et panneaux</value>
  </data>
  <data name="Focus" xml:space="preserve">
    <value>Orientation client</value>
  </data>
  <data name="ForbiddenMessage" xml:space="preserve">
    <value>L'accès à cette ressource est interdit.</value>
  </data>
  <data name="ForbiddenTitle" xml:space="preserve">
    <value>Accès refusé 🚫</value>
  </data>
  <data name="ForgotPassword" xml:space="preserve">
    <value>Mot de passe oublié</value>
  </data>
  <data name="ForgotPasswordMessage" xml:space="preserve">
    <value>Veuillez saisir votre adresse e-mail. Nous vous enverrons ensuite les instructions pour réinitialiser votre mot de passe.</value>
  </data>
  <data name="FourFloor" xml:space="preserve">
    <value>4e étage</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>Français</value>
  </data>
  <data name="From" xml:space="preserve">
    <value>De</value>
  </data>
  <data name="Furniture" xml:space="preserve">
    <value>Meubles</value>
  </data>
  <data name="FurnitureAssembly" xml:space="preserve">
    <value>Montage de meubles</value>
  </data>
  <data name="FurnitureLift" xml:space="preserve">
    <value>Monte-meubles</value>
  </data>
  <data name="Garage" xml:space="preserve">
    <value>Garage</value>
  </data>
  <data name="Garden" xml:space="preserve">
    <value>Jardin</value>
  </data>
  <data name="Gastronomy" xml:space="preserve">
    <value>Gastronomie</value>
  </data>
  <data name="General statistics" xml:space="preserve">
    <value>Statistiques générales</value>
  </data>
  <data name="German" xml:space="preserve">
    <value>Allemand</value>
  </data>
  <data name="Gisper" xml:space="preserve">
    <value>Plâtrier</value>
  </data>
  <data name="Good" xml:space="preserve">
    <value>Bien</value>
  </data>
  <data name="GreaterTenFloor" xml:space="preserve">
    <value>&gt; 10e étage</value>
  </data>
  <data name="HandOverDate" xml:space="preserve">
    <value>Date de remise</value>
  </data>
  <data name="HasInventory" xml:space="preserve">
    <value>Inventaire</value>
  </data>
  <data name="HaveAccount" xml:space="preserve">
    <value>Pas encore de compte ?</value>
  </data>
  <data name="Healthcare" xml:space="preserve">
    <value>Santé</value>
  </data>
  <data name="HeatingAndEnergy" xml:space="preserve">
    <value>Chauffage et énergie</value>
  </data>
  <data name="HeavyLoad" xml:space="preserve">
    <value>Charge lourde</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>Bonne journée</value>
  </data>
  <data name="HighPressure" xml:space="preserve">
    <value>Haute pression</value>
  </data>
  <data name="Hotel Cleaning" xml:space="preserve">
    <value>Nettoyage d'hôtel</value>
  </data>
  <data name="Hotel industry" xml:space="preserve">
    <value>Hôtellerie</value>
  </data>
  <data name="House Keeping" xml:space="preserve">
    <value>Conciergerie</value>
  </data>
  <data name="House maintenance" xml:space="preserve">
    <value>Conciergerie</value>
  </data>
  <data name="I have read and agree to the general terms and conditions for purchasing inquiries online via TaskDotNets" xml:space="preserve">
    <value>Je confirme avoir lu et accepté les conditions générales de vente (CGV) pour l’achat en ligne de demandes sur TaskDotNets.com.</value>
  </data>
  <data name="IBAN" xml:space="preserve">
    <value>N° IBAN.</value>
  </data>
  <data name="ID Number" xml:space="preserve">
    <value>Numéro d'identification</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="IndividualActivity" xml:space="preserve">
    <value>Activité personnalisée</value>
  </data>
  <data name="Industrial Cleaning" xml:space="preserve">
    <value>Nettoyage industriel</value>
  </data>
  <data name="Information Sheet" xml:space="preserve">
    <value>Tableau de bord</value>
  </data>
  <data name="Inspection" xml:space="preserve">
    <value>Visite</value>
  </data>
  <data name="InstallLamp" xml:space="preserve">
    <value>Installer des lampes</value>
  </data>
  <data name="Internal" xml:space="preserve">
    <value>Interne</value>
  </data>
  <data name="Internal/External" xml:space="preserve">
    <value>Interne / Externe</value>
  </data>
  <data name="InternalServerErrorMessage" xml:space="preserve">
    <value>Erreur interne du serveur. Veuillez contacter le support si le problème persiste.</value>
  </data>
  <data name="InternalServerErrorTitle" xml:space="preserve">
    <value>Erreur interne du serveur 💥</value>
  </data>
  <data name="International Move" xml:space="preserve">
    <value>Déménagement international</value>
  </data>
  <data name="InvalidAmount" xml:space="preserve">
    <value>Montant invalide</value>
  </data>
  <data name="InvalidInput" xml:space="preserve">
    <value>Entrée invalide</value>
  </data>
  <data name="Inventory" xml:space="preserve">
    <value>Inventaire (pièces)</value>
  </data>
  <data name="Inventory Items" xml:space="preserve">
    <value>Inventaire (contenu)</value>
  </data>
  <data name="Inventory list" xml:space="preserve">
    <value>Liste d'inventaire</value>
  </data>
  <data name="InventoryItem" xml:space="preserve">
    <value>Article d'inventaire</value>
  </data>
  <data name="Italian" xml:space="preserve">
    <value>italien</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>Objets</value>
  </data>
  <data name="ItemsList" xml:space="preserve">
    <value>Ajouter et modifier le contenu des pièces</value>
  </data>
  <data name="KantonWorkingIn" xml:space="preserve">
    <value>Veuillez sélectionner les cantons dans lesquels vous souhaitez travailler.</value>
  </data>
  <data name="KitchenConstruction" xml:space="preserve">
    <value>Construction de cuisine</value>
  </data>
  <data name="Kurdish" xml:space="preserve">
    <value>kurde</value>
  </data>
  <data name="Laminate" xml:space="preserve">
    <value>Stratifié</value>
  </data>
  <data name="Lamps" xml:space="preserve">
    <value>Lampes</value>
  </data>
  <data name="Land" xml:space="preserve">
    <value>pays</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Langue</value>
  </data>
  <data name="Lift" xml:space="preserve">
    <value>Ascenseur</value>
  </data>
  <data name="Linoleum" xml:space="preserve">
    <value>Linoléum</value>
  </data>
  <data name="Locksmith" xml:space="preserve">
    <value>Locksmith services</value>
  </data>
  <data name="Log Out" xml:space="preserve">
    <value>Se déconnecter</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Se connecter</value>
  </data>
  <data name="loginDesc" xml:space="preserve">
    <value>Tout sous contrôle - plus intelligent, plus rapide, mieux organisé</value>
  </data>
  <data name="LoginHeader" xml:space="preserve">
    <value>Ici, vous avez un accès complet à toutes les demandes et informations.</value>
  </data>
  <data name="Maintenance Cleaning" xml:space="preserve">
    <value>Nettoyage d'entretien</value>
  </data>
  <data name="Manage Users" xml:space="preserve">
    <value>Gérer les utilisateurs</value>
  </data>
  <data name="Maximum offers" xml:space="preserve">
    <value>Nombre de ventes autorisées</value>
  </data>
  <data name="Mechanic" xml:space="preserve">
    <value>Travaux mécaniques</value>
  </data>
  <data name="Message" xml:space="preserve">
    <value>Message</value>
  </data>
  <data name="Miss" xml:space="preserve">
    <value>Mademoiselle</value>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="MoreWork" xml:space="preserve">
    <value>Plus de travail</value>
  </data>
  <data name="Move And Clean" xml:space="preserve">
    <value>Déménagement et nettoyage</value>
  </data>
  <data name="MoveFrom" xml:space="preserve">
    <value>Extrait de</value>
  </data>
  <data name="MoveTo" xml:space="preserve">
    <value>à</value>
  </data>
  <data name="Moving" xml:space="preserve">
    <value>Déménagement</value>
  </data>
  <data name="Moving &amp; Cleaning" xml:space="preserve">
    <value>Déménagement &amp; nettoyage</value>
  </data>
  <data name="Moving and Cleaning" xml:space="preserve">
    <value>Déménagement et nettoyage</value>
  </data>
  <data name="Moving in" xml:space="preserve">
    <value>Déménagement vers</value>
  </data>
  <data name="MovingAndCleaning" xml:space="preserve">
    <value>Déménagement &amp; nettoyage</value>
  </data>
  <data name="MovingBoxes" xml:space="preserve">
    <value>Cartons de déménagement</value>
  </data>
  <data name="Mr" xml:space="preserve">
    <value>Monsieur</value>
  </data>
  <data name="Mrs" xml:space="preserve">
    <value>Madame</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="Name in English" xml:space="preserve">
    <value>Objet en anglais</value>
  </data>
  <data name="Name in French" xml:space="preserve">
    <value>Objet en français</value>
  </data>
  <data name="Name in German" xml:space="preserve">
    <value>Objet en allemand</value>
  </data>
  <data name="Name in Italian" xml:space="preserve">
    <value>Objet en italien</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Nouveau</value>
  </data>
  <data name="New entry" xml:space="preserve">
    <value>Nouvelle entrée</value>
  </data>
  <data name="New message" xml:space="preserve">
    <value>Nouveau message</value>
  </data>
  <data name="New Password" xml:space="preserve">
    <value>Nouveau mot de passe</value>
  </data>
  <data name="NewPasswordMessage" xml:space="preserve">
    <value>Saisir et confirmer le nouveau mot de passe.</value>
  </data>
  <data name="NewRequest" xml:space="preserve">
    <value>Nouvelle demande</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>Suivant</value>
  </data>
  <data name="NineFloor" xml:space="preserve">
    <value>9ème étage</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="NotEnoughBalance" xml:space="preserve">
    <value>Votre solde actuel est insuffisant. Voulez-vous continuer ?</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Remarques</value>
  </data>
  <data name="NotFoundMessage" xml:space="preserve">
    <value>Nous n'avons pas pu trouver la page demandée.</value>
  </data>
  <data name="NotFoundTitle" xml:space="preserve">
    <value>Page non trouvée ⚠️</value>
  </data>
  <data name="Nothing" xml:space="preserve">
    <value>Aucun</value>
  </data>
  <data name="NotRequired" xml:space="preserve">
    <value>Non requis</value>
  </data>
  <data name="Number" xml:space="preserve">
    <value>Quantité</value>
  </data>
  <data name="Object" xml:space="preserve">
    <value>Objet</value>
  </data>
  <data name="Office Cleaning" xml:space="preserve">
    <value>Nettoyage de bureaux</value>
  </data>
  <data name="Office Rooms" xml:space="preserve">
    <value>Bureaux</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Old Paassword" xml:space="preserve">
    <value>Ancien mot de passe</value>
  </data>
  <data name="OneDay" xml:space="preserve">
    <value>1 jour</value>
  </data>
  <data name="OneFloor" xml:space="preserve">
    <value>1er étage</value>
  </data>
  <data name="OneMonth" xml:space="preserve">
    <value>1 mois</value>
  </data>
  <data name="OneWeek" xml:space="preserve">
    <value>1 semaine</value>
  </data>
  <data name="Order-Nr" xml:space="preserve">
    <value>N° de demande</value>
  </data>
  <data name="OrderCountTitle" xml:space="preserve">
    <value>Formulaire de contrôle des demandes</value>
  </data>
  <data name="OrderNumber" xml:space="preserve">
    <value>Numéro de commande</value>
  </data>
  <data name="OrdersList" xml:space="preserve">
    <value>Liste des commandes</value>
  </data>
  <data name="OrderType" xml:space="preserve">
    <value>Type de commande</value>
  </data>
  <data name="OurPartner" xml:space="preserve">
    <value>Notre partenaire</value>
  </data>
  <data name="Owner" xml:space="preserve">
    <value>Propriétaire</value>
  </data>
  <data name="Packing" xml:space="preserve">
    <value>Emballage</value>
  </data>
  <data name="Pages" xml:space="preserve">
    <value>Pages</value>
  </data>
  <data name="Paid costs CHF" xml:space="preserve">
    <value>Coûts payés CHF</value>
  </data>
  <data name="Paid costs CHF1" xml:space="preserve">
    <value>Coûts payés CHF</value>
  </data>
  <data name="Painting" xml:space="preserve">
    <value>Peindre</value>
  </data>
  <data name="Painting &amp; Gipser" xml:space="preserve">
    <value>Peintre &amp; Plâtrier</value>
  </data>
  <data name="PaintingAndGisper" xml:space="preserve">
    <value>Peintre &amp; Plâtrier</value>
  </data>
  <data name="Parquet" xml:space="preserve">
    <value>Parquet</value>
  </data>
  <data name="Partner Data Updated Successfully" xml:space="preserve">
    <value>Données du partenaire mises à jour avec succès</value>
  </data>
  <data name="Partner Profile" xml:space="preserve">
    <value>Profil du partenaire</value>
  </data>
  <data name="Partner Status" xml:space="preserve">
    <value>Statut du partenaire</value>
  </data>
  <data name="Partner-No" xml:space="preserve">
    <value>N° du partenaire</value>
  </data>
  <data name="PartnerBalanceHistory" xml:space="preserve">
    <value>Sessions de recharge</value>
  </data>
  <data name="PartnerDataUpdatedSuccessfully" xml:space="preserve">
    <value>Données du partenaire mises à jour avec succès.</value>
  </data>
  <data name="PartnerList" xml:space="preserve">
    <value>Liste des partenaires</value>
  </data>
  <data name="PartnerOrders" xml:space="preserve">
    <value>Commandes partenaires</value>
  </data>
  <data name="PartnerOrdersReport" xml:space="preserve">
    <value>Rapport des commandes partenaires</value>
  </data>
  <data name="Partner_Block_UID" xml:space="preserve">
    <value>Accès refusé : Votre accès à la plateforme a été définitivement désactivé en raison de violations précédentes. Une nouvelle inscription n’est pas possible.
            Pour toute question, veuillez nous contacter à l’adresse suivante : <EMAIL>.</value>
  </data>
  <data name="Partner_Dashboard01" xml:space="preserve">
    <value>Sélectionnez les demandes adaptées en fonction du type d’activité et de la région.  Seules celles qui vous correspondent s’afficheront. </value>
  </data>
  <data name="Partner_Dashboard02" xml:space="preserve">
    <value>Réservez rapidement et contactez directement le client pour fixer un rendez-vous ou envoyer une offre.</value>
  </data>
  <data name="Partner_Dashboard03" xml:space="preserve">
    <value>Le client attend une offre adaptée, de la fiabilité et le respect des délais.</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Mot de passe</value>
  </data>
  <data name="Pay CHF" xml:space="preserve">
    <value>Payer en CHF</value>
  </data>
  <data name="Pay now" xml:space="preserve">
    <value>Payez maintenant</value>
  </data>
  <data name="Payment" xml:space="preserve">
    <value>Paiement</value>
  </data>
  <data name="PaymentFehler" xml:space="preserve">
    <value>Une erreur s’est produite lors du paiement.</value>
  </data>
  <data name="PaymentMethod" xml:space="preserve">
    <value>Méthode de paiement</value>
  </data>
  <data name="PaymentSuccesfully" xml:space="preserve">
    <value>Le paiement a été effectué avec succès.</value>
  </data>
  <data name="Period" xml:space="preserve">
    <value>Période</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Téléphone</value>
  </data>
  <data name="Piano" xml:space="preserve">
    <value>Piano</value>
  </data>
  <data name="Plates" xml:space="preserve">
    <value>Carrelage</value>
  </data>
  <data name="Please choose a payment method!" xml:space="preserve">
    <value>Veuillez choisir une méthode de paiement !</value>
  </data>
  <data name="Please pay for the selected order" xml:space="preserve">
    <value>Payez maintenant la commande sélectionnée.</value>
  </data>
  <data name="PleaseAcceptTerms" xml:space="preserve">
    <value>Veuillez accepter les conditions générales.</value>
  </data>
  <data name="Plumbing" xml:space="preserve">
    <value>Travaux de plomberie</value>
  </data>
  <data name="PName" xml:space="preserve">
    <value>Personne de contact</value>
  </data>
  <data name="Portuguese" xml:space="preserve">
    <value>Portugais</value>
  </data>
  <data name="PostBox" xml:space="preserve">
    <value>BP</value>
  </data>
  <data name="Preisfuer" xml:space="preserve">
    <value>Prix pour</value>
  </data>
  <data name="Press key to show the list of desired activity" xml:space="preserve">
    <value>Veuillez appuyer sur le bouton pour voir la liste des activités souhaitées.</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Prix</value>
  </data>
  <data name="Price of the cleaning request" xml:space="preserve">
    <value>Prix de la demande de nettoyage</value>
  </data>
  <data name="Price of the combined request" xml:space="preserve">
    <value>Prix de la demande combinée</value>
  </data>
  <data name="Price of the moving request" xml:space="preserve">
    <value>Prix de la demande de déménagement</value>
  </data>
  <data name="Price of the painting request" xml:space="preserve">
    <value>Prix de la demande de peinture</value>
  </data>
  <data name="Price of the plastering request" xml:space="preserve">
    <value>Preis der Gipseranfrage – Prix de la demande de plâtrerie</value>
  </data>
  <data name="Price/Quality" xml:space="preserve">
    <value>Prix / Qualité</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Imprimer</value>
  </data>
  <data name="PrintReport" xml:space="preserve">
    <value>Imprimer le rapport</value>
  </data>
  <data name="Privacy Policy" xml:space="preserve">
    <value>Déclaration de confidentialité</value>
  </data>
  <data name="Private Move" xml:space="preserve">
    <value>Déménagement privé</value>
  </data>
  <data name="Process an order" xml:space="preserve">
    <value>Plateforme TaskDotNet : &lt;br /&gt;                                 La solution pour un travail productif</value>
  </data>
  <data name="Purchased Activites" xml:space="preserve">
    <value>Activités achetées</value>
  </data>
  <data name="Purchased orders" xml:space="preserve">
    <value>Commandes achetées</value>
  </data>
  <data name="PurchaseDate" xml:space="preserve">
    <value>Date d'achat</value>
  </data>
  <data name="Quality" xml:space="preserve">
    <value>Qualité</value>
  </data>
  <data name="RangeAttribute_ValidationError" xml:space="preserve">
    <value>Le champ {0} doit être compris entre {1} et {2}.</value>
  </data>
  <data name="ReadTermsAndCondotions" xml:space="preserve">
    <value>J'ai lu les conditions générales (CG) et je les accepte.</value>
  </data>
  <data name="Recharge credit" xml:space="preserve">
    <value>Recharger le crédit</value>
  </data>
  <data name="RechargeCreditHead1" xml:space="preserve">
    <value>Vous avez ici la possibilité de recharger votre crédit afin de ne pas devoir payer à chaque achat.</value>
  </data>
  <data name="RechargeCreditHead2" xml:space="preserve">
    <value>Vos données personnelles sont utilisées pour traiter votre paiement, améliorer votre expérience utilisateur sur ce site web et vous fournir des informations transparentes sur l'utilisation de vos données.</value>
  </data>
  <data name="RechargeCreditHead3" xml:space="preserve">
    <value>Veuillez lire à ce sujet notre</value>
  </data>
  <data name="RechargeFailedMessage" xml:space="preserve">
    <value>Échec de la recharge. Veuillez réessayer plus tard.</value>
  </data>
  <data name="RechargeMessage" xml:space="preserve">
    <value>Votre crédit a été ajouté avec succès. Merci beaucoup !</value>
  </data>
  <data name="RechargeSuccessfullyMessage" xml:space="preserve">
    <value>Le crédit a été rechargé avec succès.</value>
  </data>
  <data name="Recurring Cleaning" xml:space="preserve">
    <value>Nettoyage récurrent</value>
  </data>
  <data name="RefrigerationTechnician" xml:space="preserve">
    <value>Services de réfrigération</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>S'inscrire</value>
  </data>
  <data name="Register here" xml:space="preserve">
    <value>S'inscrire</value>
  </data>
  <data name="Register now" xml:space="preserve">
    <value>Inscrivez-vous maintenant</value>
  </data>
  <data name="RegisterHeader1" xml:space="preserve">
    <value>La plateforme pour </value>
  </data>
  <data name="RegisterHeader2" xml:space="preserve">
    <value>une productivité maximale.</value>
  </data>
  <data name="RegisterHeader3" xml:space="preserve">
    <value>Connectée efficacement. </value>
  </data>
  <data name="RegisterHeader4" xml:space="preserve">
    <value>Réalisée avec succès.</value>
  </data>
  <data name="RegisterHeader5" xml:space="preserve">
    <value>Veuillez saisir vos identifiants pour vous inscrire sur TaskDotNet.</value>
  </data>
  <data name="Regular Cleaning" xml:space="preserve">
    <value>Nettoyage régulier</value>
  </data>
  <data name="Remember Me" xml:space="preserve">
    <value>Rester connecté</value>
  </data>
  <data name="Request List" xml:space="preserve">
    <value>Liste des demandes</value>
  </data>
  <data name="RequiredAttribute_ValidationError" xml:space="preserve">
    <value>Le champ {0} est requis.</value>
  </data>
  <data name="ResetDashboard" xml:space="preserve">
    <value>Réinitialiser le tableau de bord</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Réinitialiser le mot de passe</value>
  </data>
  <data name="ResetPasswordMessage" xml:space="preserve">
    <value>Le lien de réinitialisation du mot de passe a été envoyé. Veuillez vérifier votre boîte de réception.</value>
  </data>
  <data name="ResetTitle" xml:space="preserve">
    <value>Nettoyer les données du formulaire </value>
  </data>
  <data name="Role" xml:space="preserve">
    <value>Droit d’accès</value>
  </data>
  <data name="Roofer" xml:space="preserve">
    <value>Travaux de couverture</value>
  </data>
  <data name="Room" xml:space="preserve">
    <value>Pièce</value>
  </data>
  <data name="RoomList" xml:space="preserve">
    <value>Liste des pièces principales</value>
  </data>
  <data name="Rooms" xml:space="preserve">
    <value>Pièce</value>
  </data>
  <data name="Russian" xml:space="preserve">
    <value>Russe</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Enregistrer</value>
  </data>
  <data name="Save changes" xml:space="preserve">
    <value>Enregistrer les modifications</value>
  </data>
  <data name="Select an activity" xml:space="preserve">
    <value>Sélectionnez une activité</value>
  </data>
  <data name="Select Branch" xml:space="preserve">
    <value>Sélectionner le secteur</value>
  </data>
  <data name="Select Template" xml:space="preserve">
    <value>Sélectionner un modèle</value>
  </data>
  <data name="Select the Activity you want" xml:space="preserve">
    <value>Sélectionnez les activités souhaitées.</value>
  </data>
  <data name="Select the cantons in which you would like to work here" xml:space="preserve">
    <value>Sélectionnez ici les cantons dans lesquels vous souhaitez travailler.</value>
  </data>
  <data name="Send" xml:space="preserve">
    <value>Envoyer</value>
  </data>
  <data name="SendEmail" xml:space="preserve">
    <value>Envoyer un e-mail</value>
  </data>
  <data name="SendEmailTo" xml:space="preserve">
    <value>Envoyez un e-mail au partenaire</value>
  </data>
  <data name="SendMessageToTheCustomer" xml:space="preserve">
    <value>Envoyer un message au client</value>
  </data>
  <data name="SendResetLink" xml:space="preserve">
    <value>Envoyer le lien de réinitialisation</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Paramètres</value>
  </data>
  <data name="SevenFloor" xml:space="preserve">
    <value>7e étage</value>
  </data>
  <data name="Show all Activity" xml:space="preserve">
    <value>Afficher toutes les activités</value>
  </data>
  <data name="Show all Archive data" xml:space="preserve">
    <value>Afficher toutes les données archivées</value>
  </data>
  <data name="Show all Inventory" xml:space="preserve">
    <value>Modifier les pièces</value>
  </data>
  <data name="Show all Statistics" xml:space="preserve">
    <value>Afficher toutes les statistiques</value>
  </data>
  <data name="Sightseeing" xml:space="preserve">
    <value>Visite</value>
  </data>
  <data name="SixFloor" xml:space="preserve">
    <value>6e étage</value>
  </data>
  <data name="SmallTransport" xml:space="preserve">
    <value>Petit transport</value>
  </data>
  <data name="SoilType" xml:space="preserve">
    <value>Type de sol</value>
  </data>
  <data name="Sold" xml:space="preserve">
    <value>Vendu</value>
  </data>
  <data name="SomeThingWentWrong" xml:space="preserve">
    <value>Une erreur s'est produite. Veuillez réessayer plus tard.</value>
  </data>
  <data name="Space" xml:space="preserve">
    <value>Pièce</value>
  </data>
  <data name="Spanish" xml:space="preserve">
    <value>Espagnol</value>
  </data>
  <data name="Start Date" xml:space="preserve">
    <value>Date de début</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Date de début </value>
  </data>
  <data name="Statistics" xml:space="preserve">
    <value>Statistiques</value>
  </data>
  <data name="StatisticsPageTitle" xml:space="preserve">
    <value>Ce tableau montre les mouvements de vente de tous les produits et activités annoncés au cours de l’année en cours.</value>
  </data>
  <data name="StatisticsPageTitlePartner" xml:space="preserve">
    <value>Ce tableau montre les mouvements d’achat de tous les produits (activités) acquis au cours de l’année en cours.</value>
  </data>
  <data name="StatisticsPageTitlePartner2" xml:space="preserve">
    <value>Les achats du partenaire pour l’année en cours :</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Statut</value>
  </data>
  <data name="Storage" xml:space="preserve">
    <value>Stockage</value>
  </data>
  <data name="Street" xml:space="preserve">
    <value>Rue</value>
  </data>
  <data name="StringLengthAttribute_ValidationError" xml:space="preserve">
    <value>Le champ {0} doit être une chaîne de caractères d’une longueur maximale de {1}.</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Sujet</value>
  </data>
  <data name="System setup" xml:space="preserve">
    <value>Paramètres système</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Votre équipe TaskDotNet</value>
  </data>
  <data name="TenFloor" xml:space="preserve">
    <value>10e étage</value>
  </data>
  <data name="TermsNotAccepted" xml:space="preserve">
    <value>CG non acceptées </value>
  </data>
  <data name="Thank you for your payment..." xml:space="preserve">
    <value>Merci beaucoup pour votre paiement.</value>
  </data>
  <data name="The achievements of partnere for the current month" xml:space="preserve">
    <value>Prestations fournies par les partenaires au cours de l’année en cours.</value>
  </data>
  <data name="The total: CHF" xml:space="preserve">
    <value>Le montant total : CHF</value>
  </data>
  <data name="PartnerDashboardMessage" xml:space="preserve">
    <value>Ce projet vise à collaborer avec "TaskDotNet.com" et aide les partenaires à suivre les commandes, analyser les statistiques et prendre des décisions éclairées basées sur des données actuelles.</value>
  </data>
  <data name="This request will be deleted after archiving!" xml:space="preserve">
    <value>Cette demande sera supprimée après archivage !</value>
  </data>
  <data name="This table displays the work activity statistics for the current year" xml:space="preserve">
    <value>Ce tableau affiche les statistiques des activités professionnelles pour l’année en cours.</value>
  </data>
  <data name="ThisIsRequiredRequest" xml:space="preserve">
    <value>Il s’agit de la demande concrète.</value>
  </data>
  <data name="ThreeDays" xml:space="preserve">
    <value>3 jours</value>
  </data>
  <data name="ThreeFloor" xml:space="preserve">
    <value>3e étage</value>
  </data>
  <data name="ThreeWeeks" xml:space="preserve">
    <value>3 semaines</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Titre</value>
  </data>
  <data name="To" xml:space="preserve">
    <value>À</value>
  </data>
  <data name="To which branch" xml:space="preserve">
    <value>Sélectionner un secteur</value>
  </data>
  <data name="Top Up Balance" xml:space="preserve">
    <value>Recharger le crédit </value>
  </data>
  <data name="Total volume" xml:space="preserve">
    <value>Volume total</value>
  </data>
  <data name="toTheDrainEdge" xml:space="preserve">
    <value>vers la zone d’évacuation</value>
  </data>
  <data name="toTheLoadingEdge" xml:space="preserve">
    <value>vers la zone de chargement</value>
  </data>
  <data name="Turkish" xml:space="preserve">
    <value>Turc</value>
  </data>
  <data name="TwoDays" xml:space="preserve">
    <value>2 jours</value>
  </data>
  <data name="TwoFloor" xml:space="preserve">
    <value>2e étage</value>
  </data>
  <data name="TwoWeeks" xml:space="preserve">
    <value>2 semaines</value>
  </data>
  <data name="UID" xml:space="preserve">
    <value>N° IDE </value>
  </data>
  <data name="UnauthorizedMessage" xml:space="preserve">
    <value>Vous n’avez malheureusement pas l’autorisation d’accéder à cette page. Veuillez retourner à la page d’accueil.</value>
  </data>
  <data name="UnauthorizedTitle" xml:space="preserve">
    <value>Vous n’êtes pas autorisé ! 🔐</value>
  </data>
  <data name="Unit" xml:space="preserve">
    <value>Unité</value>
  </data>
  <data name="Unpacking" xml:space="preserve">
    <value>Déballer</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Mettre à jour</value>
  </data>
  <data name="Upload File" xml:space="preserve">
    <value>Télécharger un fichier</value>
  </data>
  <data name="Useless" xml:space="preserve">
    <value>Inutile</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Utilisateur</value>
  </data>
  <data name="UsersList" xml:space="preserve">
    <value>Liste des utilisateurs</value>
  </data>
  <data name="VerifyAccount" xml:space="preserve">
    <value>Veuillez vérifier votre compte.</value>
  </data>
  <data name="View All" xml:space="preserve">
    <value>Tout afficher</value>
  </data>
  <data name="View order" xml:space="preserve">
    <value>Aperçu de la demande</value>
  </data>
  <data name="Viewing" xml:space="preserve">
    <value>Visite </value>
  </data>
  <data name="ViewingDate" xml:space="preserve">
    <value>Date de la visite</value>
  </data>
  <data name="Volume (m3)" xml:space="preserve">
    <value>Volume (m³)</value>
  </data>
  <data name="Volume in m3" xml:space="preserve">
    <value>Volume en m³</value>
  </data>
  <data name="Walls" xml:space="preserve">
    <value>Murs</value>
  </data>
  <data name="WallsAndCeilings" xml:space="preserve">
    <value>Murs et plafonds</value>
  </data>
  <data name="Washroom" xml:space="preserve">
    <value>Buanderie</value>
  </data>
  <data name="Weak" xml:space="preserve">
    <value>Faible</value>
  </data>
  <data name="Website" xml:space="preserve">
    <value>Site web</value>
  </data>
  <data name="WelcomeBack" xml:space="preserve">
    <value>Bienvenue, partenaire !</value>
  </data>
  <data name="Welder" xml:space="preserve">
    <value>Travaux de soudure</value>
  </data>
  <data name="Window Cleaning" xml:space="preserve">
    <value>Nettoyage des fenêtres</value>
  </data>
  <data name="Windows" xml:space="preserve">
    <value>Fenêtre(s)</value>
  </data>
  <data name="Windows Cleaning" xml:space="preserve">
    <value>Nettoyer les fenêtres</value>
  </data>
  <data name="WithInventoryList" xml:space="preserve">
    <value>avec la liste d’inventaire </value>
  </data>
  <data name="Workers" xml:space="preserve">
    <value>Travailleur</value>
  </data>
  <data name="Workspace" xml:space="preserve">
    <value>Secteur</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="YesOn" xml:space="preserve">
    <value>Oui, le :</value>
  </data>
  <data name="YouWantToPurchaseThisOrder" xml:space="preserve">
    <value>Remarque : Lors de l’achat de cette commande, {0} seront déduits de votre crédit.</value>
  </data>
  <data name="AdminDashboardHeader" xml:space="preserve">
    <value>Contrôle complet sur la plateforme TaskDotNet</value>
  </data>
  <data name="AdminDashboardHeader2" xml:space="preserve">
    <value>Il s'agit d'une page statistique qui illustre les  
interactions et les activités de tous les  
partenaires sur la plateforme.</value>
  </data>
  <data name="CreateUser" xml:space="preserve">
    <value>Créer un utilisateur</value>
  </data>
  <data name="Handyman" xml:space="preserve">
    <value>Bricoleur</value>
  </data>
  <data name="RequestControl" xml:space="preserve">
    <value>Contrôle des demandes</value>
  </data>
  <data name="UpdateUser" xml:space="preserve">
    <value>Mettre à jour l’utilisateur</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Retour</value>
  </data>
  <data name="Credits" xml:space="preserve">
    <value>Avoirs</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filtre</value>
  </data>
  <data name="NoCreditsFound" xml:space="preserve">
    <value>Aucun crédit trouvé pour la période sélectionnée.</value>
  </data>
  <data name="Partnername" xml:space="preserve">
    <value>Nom du partenaire</value>
  </data>
  <data name="PaymentWay" xml:space="preserve">
    <value>Mode de paiemen</value>
  </data>
  <data name="Disclaimer" xml:space="preserve">
    <value>Avertissement</value>
  </data>
  <data name="LegalNotice" xml:space="preserve">
    <value>Mentions légales</value>
  </data>
  <data name="PrivacyPolicy" xml:space="preserve">
    <value>Protection des données</value>
  </data>
  <data name="TermsAndConditions" xml:space="preserve">
    <value>CG</value>
  </data>
  <data name="RequestDataNote" xml:space="preserve">
    <value>Les icônes indiquent les données personnelles du client disponibles dans la demande.</value>
  </data>
</root>