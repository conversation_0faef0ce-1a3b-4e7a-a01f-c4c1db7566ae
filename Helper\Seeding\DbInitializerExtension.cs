﻿using TaskDotNet.Comman.DataAccess;
using Microsoft.AspNetCore.Identity;
using TaskDotNet.Models;

namespace TaskDotNet.Helper.Seeding
{
    public static class DbInitializerExtension
    {
        public static async Task<IApplicationBuilder> UseItToSeedSqlServer(this IApplicationBuilder app, IConfiguration configuration)
        {
            ArgumentNullException.ThrowIfNull(app, nameof(app));

            using var scope = app.ApplicationServices.CreateScope();
            var services = scope.ServiceProvider;
            try
            {
                var context = services.GetRequiredService<ApplicationDbContext>();
                var userManager = services.GetRequiredService<UserManager<Partner>>();
                var roleManager = services.GetRequiredService<RoleManager<IdentityRole>>();

                await DbInitializer.Initialize(context, userManager, roleManager, configuration);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
            }

            return app;
        }
    }
}
