<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="TermsAndConditionsTitle" xml:space="preserve">
    <value>General Terms and Conditions (GTC)</value>
  </data>
  <data name="TermsAndConditionsIntro" xml:space="preserve">
    <value>These general terms and conditions govern the legal relationship between Lightsoft GmbH and its customers. For the sake of simplicity, the term "contract/" is used in these conditions regardless of the type of service provided.</value>
  </data>
  <data name="ContractDefinitionTitle" xml:space="preserve">
    <value>Definition of the Contract</value>
  </data>
  <data name="ContractDefinitionText" xml:space="preserve">
    <value>The term "contract" here refers to the customer's consent to use the TaskDotNet platform in order to publish their need for a specific service and receive offers from potential service providers. Likewise, partners register their businesses on this platform to interact with customer inquiries and get in contact with them. This collaboration, whether through registration or publishing a request, constitutes a binding contract between the user and the platform.</value>
  </data>
  <data name="ApplicableConditionsTitle" xml:space="preserve">
    <value>Applicable Conditions</value>
  </data>
  <data name="ApplicableConditionsText" xml:space="preserve">
    <value>These general terms and conditions of Lightsoft GmbH apply to all persons who register as partners to process customer inquiries via this platform. The registration is considered a contract between the company and the partner, which comes into effect upon Lightsoft's approval of the partner's registration on the platform.</value>
  </data>
  <data name="ContractValidityTitle" xml:space="preserve">
    <value>Validity of the Contract</value>
  </data>
  <data name="ContractValidityText" xml:space="preserve">
    <value>If any provision of these general terms and conditions is wholly or partially invalid or void, the validity of the contract and the remaining provisions shall remain unaffected. The invalid provision shall be replaced by a regulation that comes as close as legally permissible to the legal and economic intent of the original provision.</value>
  </data>
  <data name="JurisdictionTitle" xml:space="preserve">
    <value>Place of Jurisdiction</value>
  </data>
  <data name="JurisdictionText" xml:space="preserve">
    <value>For all disputes arising from this contract, the Swiss courts shall have jurisdiction, unless mandatory legal jurisdictions apply. Swiss law shall exclusively apply to all contractual provisions, including the submission of service requests by customers via the platform and the acceptance of such requests by partners through purchase via TaskDotNet.</value>
  </data>
  <data name="ConditionsValidityTitle" xml:space="preserve">
    <value>Validity of the Terms</value>
  </data>
  <data name="ConditionsValidityText" xml:space="preserve">
    <value>These general terms and conditions are valid from June 1, 2024, and cease to be valid upon the publication of a new version on the platform. Written confirmations, statements, or complaints are legally binding if submitted by post, email, SMS, or WhatsApp.</value>
  </data>
  <data name="ContractingPartiesTitle" xml:space="preserve">
    <value>Contracting Parties</value>
  </data>
  <data name="ContractingPartiesText" xml:space="preserve">
    <value>The contracting parties within the meaning of these general terms and conditions are both the guest (customer or partner) and Lightsoft GmbH.</value>
  </data>
  <data name="ScopeOfServicesTitle" xml:space="preserve">
    <value>Scope of Services</value>
  </data>
  <data name="ScopeOfServicesText" xml:space="preserve">
    <value>The agreed scope of services is determined by the individual order published by the customer. Orders in the areas of transport, cleaning, painting, plastering, and other common manual trades may only be submitted via the TaskDotNet.com platform.</value>
  </data>
  <data name="OrderRulesTitle" xml:space="preserve">
    <value>Rules for Order Submission and Booking</value>
  </data>
  <data name="OrderRulesText" xml:space="preserve">
    <value>The customer who publishes an order on the platform and the partner who books and purchases this order are each considered responsible contractual parties within the meaning of these terms.</value>
  </data>
  <data name="PersonalDataEntryTitle" xml:space="preserve">
    <value>Entry of Personal Data of the Customer (the Client)</value>
  </data>
  <data name="PersonalDataEntryText" xml:space="preserve">
    <value>The client is obliged to provide all information relevant to the request as well as their basic contact details completely and truthfully. Basic contact details include at least the full name, postal code, city of residence, and a valid email address. Additional personal information such as telephone number and street name is generally helpful for smooth communication and processing. However, Lightsoft GmbH understands that clients may choose not to provide this data for personal reasons. The platform transparently informs the respective partner, for each request, of the personal data provided by the client. The partner independently decides whether to accept or decline the request based on the available information. The platform verifies at least the validity of the provided email address. If this requirement is not met, the request is considered incomplete and will not be processed further. The verification of contact data is carried out in the interest of the partners, who provide services in exchange for receiving requests, and is intended to prevent invalid or unusable requests.</value>
  </data>
  <data name="PartnerRightsDisclaimerTitle" xml:space="preserve">
    <value>Disclaimer and Partner Rights</value>
  </data>
  <data name="PartnerRightsDisclaimerText" xml:space="preserve">
    <value>Lightsoft GmbH assumes no liability regarding whether the partner obtains the customer's consent to carry out the requested service or whether communication between the parties takes place. The final decision concerning the acceptance of a price offer and the selection of the service provider lies solely with the customer. The partner is not entitled to a refund of the purchase price of a request if the customer's consent was not obtained or if contact with the customer could not be established. Furthermore, the partner is not permitted to question the accuracy of the data submitted by the customer. The platform places great importance on verifying the validity of the customer's email address before making the request available to partners.</value>
  </data>
  <data name="OrderConfirmationTitle" xml:space="preserve">
    <value>Order Confirmation and Customer Responsibility</value>
  </data>
  <data name="OrderConfirmationText" xml:space="preserve">
    <value>To confirm an order, the customer must reply to the email sent by the platform during the order submission process. If no response is received, the order is considered incomplete. In this case, the customer is not entitled to any services and may not make any statements about the platform that could harm the reputation or interests of the company.</value>
  </data>
  <data name="OrderEntryTimesTitle" xml:space="preserve">
    <value>Order Submission Times</value>
  </data>
  <data name="OrderEntryTimesText" xml:space="preserve">
    <value>The TaskDotNet platform is available 24/7 on all days of the week for order submission. The validity of the email address is verified with each order to ensure reliable communication.</value>
  </data>
  <data name="OrderForwardingTitle" xml:space="preserve">
    <value>Forwarding of Requests to Partners</value>
  </data>
  <data name="OrderForwardingText" xml:space="preserve">
    <value>Once a request has been successfully submitted, it is immediately forwarded to registered partners. The first four partners have the right to reserve and purchase the request for a fee. After that, the request is closed and no longer displayed. If the customer wishes to withdraw or cancel the request after it has been published and purchased by partners, they shall bear all resulting financial obligations — in particular, any refund claims by partners for payments made in connection with the request. Lightsoft GmbH assumes no responsibility or liability in this regard toward the partners.</value>
  </data>
  <data name="PartnerRightsTitle" xml:space="preserve">
    <value>Rights and Obligations of the Partners</value>
  </data>
  <data name="PartnerRightsText" xml:space="preserve">
    <value>Upon purchasing a request, the partner receives all order details, including the customer's contact information – at minimum, the verified email address. After the purchase, the partner is not entitled to cancel the request or to claim a refund for the amount paid.</value>
  </data>
  <data name="PartnerCustomerCoordinationTitle" xml:space="preserve">
    <value>Coordination Between Partner and Customer</value>
  </data>
  <data name="PartnerCustomerCoordinationText" xml:space="preserve">
    <value>The agreement regarding the execution of the service, including any site visits, is made exclusively and directly between the partner and the customer, outside the TaskDotNet platform. These agreements are binding for both parties. The platform's role is limited solely to receiving and forwarding the request.</value>
  </data>
  <data name="PricesCurrencyTitle" xml:space="preserve">
    <value>Prices and Currency</value>
  </data>
  <data name="PricesCurrencyText" xml:space="preserve">
    <value>The contract prices are displayed to partners clearly and transparently. Transactions are conducted exclusively in Swiss Francs (CHF).</value>
  </data>
  <data name="LiabilityTitle" xml:space="preserve">
    <value>Liability and Compensation</value>
  </data>
  <data name="LiabilityText" xml:space="preserve">
    <value>The customer bears full responsibility for the accuracy and completeness of the data entered during the order placement. The partner is responsible for submitting an appropriate price offer and for the proper and timely execution of the agreed service without causing any damage to the customer. The executing partner is solely liable to the customer for all damages or losses arising from their conduct, that of their employees, or other commissioned third parties. Neither these persons nor the partner themselves may hold Lightsoft GmbH liable in any way—directly or indirectly—for damages or defects in execution. Lightsoft GmbH expressly disclaims any legal liability or statements in cases of theft or damage to materials or property related to the service. The protection and insurance of such items as well as any possible compensation lie solely with the executing partner.</value>
  </data>
</root>
