﻿using Humanizer;
using TaskDotNet.Api_s.Interfaces;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Models;

namespace TaskDotNet.Api_s.Services
{
    public class CleaningApiService : ICleaningApiService
    {
        private readonly ApplicationDbContext context;
        public CleaningApiService(ApplicationDbContext context)
        {
            this.context = context;
        }

        public async Task<Activity> Create(Activity activity)
        {
            activity.ActivityType = Helper.ActivityType.Cleaning;
            await context.Activities.AddAsync(activity);

            context.SaveChanges();
            return activity;
        }
        public async Task Update(Activity activity)
        {
            context.Activities.Update(activity);
            await context.SaveChangesAsync();
        }


    }
}
