<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="TermsAndConditionsTitle" xml:space="preserve">
    <value>Allgemeine Geschäftsbedingungen (AGB)</value>
  </data>
  <data name="TermsAndConditionsIntro" xml:space="preserve">
    <value>Diese allgemeinen Geschäftsbedingungen regeln das Rechtsverhältnis zwischen der Lightsoft GmbH und ihren Gästen. Zur Vereinfachung wird in diesen Bedingungen unabhängig von der Art der erbrachten Leistung von einem „Vertrag/" gesprochen.</value>
  </data>
  <data name="ContractDefinitionTitle" xml:space="preserve">
    <value>Definition des Vertrags</value>
  </data>
  <data name="ContractDefinitionText" xml:space="preserve">
    <value>Mit dem Vertrag ist hier die Zustimmung des Kunden zur Nutzung der Plattform TaskDotNet gemeint, um seinen Bedarf an einer bestimmten Dienstleistung zu veröffentlichen und Angebote von potenziellen Dienstleistern zu erhalten. Ebenso registrieren die Partner ihre Unternehmen auf dieser Plattform, um mit Kundenanfragen zu interagieren und mit ihnen in Kontakt zu treten. Diese Zusammenarbeit durch Registrierung oder Veröffentlichung gilt als Vertrag zwischen dem Nutzer und der Plattform.</value>
  </data>
  <data name="ApplicableConditionsTitle" xml:space="preserve">
    <value>Anwendbare Bedingungen</value>
  </data>
  <data name="ApplicableConditionsText" xml:space="preserve">
    <value>Diese allgemeinen Geschäftsbedingungen der Lightsoft GmbH gelten für alle Personen, die sich als Partner registrieren, um Kundenanfragen über diese Plattform zu bearbeiten. Die Registrierung gilt als Vertrag zwischen dem Unternehmen und dem Partner, der mit der Zustimmung von Lightsoft zur Registrierung des Partners auf der Plattform in Kraft tritt.</value>
  </data>
  <data name="ContractValidityTitle" xml:space="preserve">
    <value>Gültigkeit des Vertrags</value>
  </data>
  <data name="ContractValidityText" xml:space="preserve">
    <value>Sollte eine Bestimmung dieser allgemeinen Geschäftsbedingungen ganz oder teilweise unwirksam oder nichtig sein, bleibt die Gültigkeit des Vertrags und der übrigen Bestimmungen unberührt. Die unwirksame Bestimmung wird durch eine Regelung ersetzt, die dem rechtlichen und wirtschaftlichen Zweck der ursprünglichen Bestimmung in zulässiger Weise möglichst nahekommt.</value>
  </data>
  <data name="JurisdictionTitle" xml:space="preserve">
    <value>Gerichtsstand</value>
  </data>
  <data name="JurisdictionText" xml:space="preserve">
    <value>Für sämtliche Streitigkeiten aus diesem Vertrag ist das schweizerische Gericht zuständig, sofern nicht zwingende gesetzliche Gerichtsstände bestehen. Es findet ausschliesslich schweizerisches Recht Anwendung auf alle vertraglichen Bestimmungen, einschliesslich der Eingabe von Dienstleistungsanfragen durch Kunden über die Plattform sowie der Annahme dieser Anfragen durch Partner mittels Kauf über TaskDotNet.</value>
  </data>
  <data name="ConditionsValidityTitle" xml:space="preserve">
    <value>Gültigkeit der Bedingungen</value>
  </data>
  <data name="ConditionsValidityText" xml:space="preserve">
    <value>Diese allgemeinen Geschäftsbedingungen gelten ab dem 1. Juni 2024 und verlieren ihre Gültigkeit mit der Veröffentlichung einer neuen Version auf der Plattform. Schriftliche Bestätigungen, Stellungnahmen oder Beschwerden sind rechtlich verbindlich, wenn sie per Post, E-Mail, SMS oder WhatsApp übermittelt werden.</value>
  </data>
  <data name="ContractingPartiesTitle" xml:space="preserve">
    <value>Vertragsparteien</value>
  </data>
  <data name="ContractingPartiesText" xml:space="preserve">
    <value>Vertragsparteien im Sinne dieser AGB sind sowohl der Gast (Kunde oder Partner) als auch die Lightsoft GmbH.</value>
  </data>
  <data name="ScopeOfServicesTitle" xml:space="preserve">
    <value>Leistungsumfang</value>
  </data>
  <data name="ScopeOfServicesText" xml:space="preserve">
    <value>Der vereinbarte Leistungsumfang ergibt sich aus dem jeweiligen, vom Kunden individuell veröffentlichten Auftrag. Aufträge in den Bereichen Transport, Reinigung, Malerarbeiten, Verputzen und andere gängige handwerkliche Tätigkeiten dürfen ausschliesslich über die Plattform TaskDotNet.com erfasst werden.</value>
  </data>
  <data name="OrderRulesTitle" xml:space="preserve">
    <value>Regeln zur Auftragserfassung und -buchung</value>
  </data>
  <data name="OrderRulesText" xml:space="preserve">
    <value>Der Kunde, der einen Auftrag über die Plattform veröffentlicht, sowie der Partner, der diesen Auftrag bucht und kauft, sind die jeweils verantwortlichen Vertragspartner im Sinne dieser Bedingungen.</value>
  </data>
  <data name="PersonalDataEntryTitle" xml:space="preserve">
    <value>Eingabe personenbezogener Daten des Kunden (des Auftraggebers)</value>
  </data>
  <data name="PersonalDataEntryText" xml:space="preserve">
    <value>Der Auftraggeber ist verpflichtet, alle für die Anfrage relevanten Angaben sowie seine grundlegenden Kontaktdaten vollständig und wahrheitsgemäss anzugeben. Zu den grundlegenden Kontaktdaten zählen mindestens der vollständige Name, die Postleitzahl, der Wohnort sowie eine gültige E-Mail-Adresse. Weitere personenbezogene Angaben wie Telefonnummer und Strassenname sind für eine reibungslose Kommunikation und Abwicklung grundsätzlich hilfreich. Die Lightsoft GmbH zeigt jedoch Verständnis dafür, dass Auftraggeber diese Daten aus persönlichen Gründen nicht angeben möchten. Die Plattform informiert den jeweiligen Partner bei jeder Anfrage transparent über die vom Auftraggeber bereitgestellten personenbezogenen Daten. Der Partner entscheidet eigenverantwortlich, ob er die Anfrage auf Grundlage der verfügbaren Informationen annimmt oder ablehnt. Die Plattform überprüft mindestens die Gültigkeit der angegebenen E-Mail-Adresse. Sofern diese Voraussetzung nicht erfüllt ist, gilt die Anfrage als unvollständig und wird nicht weiterverarbeitet. Die Überprüfung der Kontaktdaten erfolgt im Interesse der Partner, die für den Erhalt von Anfragen entgeltlich Leistungen erbringen, und dient der Vermeidung ungültiger oder nicht verwertbarer Anfragen.</value>
  </data>
  <data name="PartnerRightsDisclaimerTitle" xml:space="preserve">
    <value>Haftungsausschluss und Rechte des Partners</value>
  </data>
  <data name="PartnerRightsDisclaimerText" xml:space="preserve">
    <value>Die Lightsoft GmbH übernimmt keine Haftung dafür, ob der Partner die Zustimmung des Kunden zur Ausführung der gewünschten Dienstleistung erhält oder ob eine Kommunikation zwischen den Parteien zustande kommt. Die endgültige Entscheidung über die Annahme eines Preisangebots sowie die Auswahl des Dienstleisters obliegt ausschliesslich dem Kunden. Dem Partner steht kein Anspruch auf Rückerstattung des Kaufpreises einer Anfrage zu, wenn er keine Zustimmung des Kunden erhalten hat oder eine Kontaktaufnahme mit dem Kunden nicht möglich war. Es ist dem Partner zudem nicht gestattet, die Richtigkeit der vom Kunden übermittelten Daten in Zweifel zu ziehen. Die Plattform misst der Überprüfung der Gültigkeit der E-Mail-Adresse des Kunden vor Freigabe der Anfrage für Partner einen hohen Stellenwert bei.</value>
  </data>
  <data name="OrderConfirmationTitle" xml:space="preserve">
    <value>Auftragsbestätigung und Kundenverantwortung</value>
  </data>
  <data name="OrderConfirmationText" xml:space="preserve">
    <value>Zur Bestätigung eines Auftrags muss der Kunde auf die von der Plattform versendete E-Mail während der Auftragseingabe antworten. Erfolgt keine Rückmeldung, gilt der Auftrag als unvollständig. In diesem Fall hat der Kunde keinen Anspruch auf Leistungen und darf sich in keiner Weise über die Plattform äussern, die den Ruf oder das Interesse des Unternehmens beeinträchtigen könnte.</value>
  </data>
  <data name="OrderEntryTimesTitle" xml:space="preserve">
    <value>Zeiten zur Auftragserfassung</value>
  </data>
  <data name="OrderEntryTimesText" xml:space="preserve">
    <value>Die Plattform TaskDotNet steht rund um die Uhr und an allen Wochentagen zur Erfassung von Aufträgen zur Verfügung. Die Gültigkeit der E-Mail-Adresse wird bei jedem Auftrag überprüft, um die Zuverlässigkeit der Kommunikation zu gewährleisten.</value>
  </data>
  <data name="OrderForwardingTitle" xml:space="preserve">
    <value>Auftragsweiterleitung an Partner</value>
  </data>
  <data name="OrderForwardingText" xml:space="preserve">
    <value>Nach erfolgreicher Eingabe der Anfrage wird diese umgehend an die registrierten Partner übermittelt. Den ersten vier Partnern steht das Recht zu, die Anfrage zu reservieren und kostenpflichtig zu erwerben. Anschliessend wird die Anfrage geschlossen und nicht mehr angezeigt. Möchte der Kunde die Anfrage nach ihrer Veröffentlichung und dem Erwerb durch Partner zurückziehen oder stornieren, trägt er sämtliche daraus resultierenden finanziellen Verpflichtungen – insbesondere etwaige Rückerstattungsansprüche der Partner in Bezug auf die für die Anfrage geleisteten Zahlungen. Die Lightsoft GmbH übernimmt hierfür keinerlei Verantwortung oder Haftung gegenüber den Partnern.</value>
  </data>
  <data name="PartnerRightsTitle" xml:space="preserve">
    <value>Rechte und Pflichten der Partner</value>
  </data>
  <data name="PartnerRightsText" xml:space="preserve">
    <value>Beim Kauf einer Anfrage erhält der Partner alle Auftragsdetails, einschliesslich der Kontaktdaten des Kunden – mindestens jedoch die verifizierte E-Mail-Adresse. Nach dem Kauf ist der Partner nicht berechtigt, die Anfrage zu stornieren oder eine Rückerstattung des dafür gezahlten Betrags zu verlangen.</value>
  </data>
  <data name="PartnerCustomerCoordinationTitle" xml:space="preserve">
    <value>Abstimmung zwischen Partner und Kunde</value>
  </data>
  <data name="PartnerCustomerCoordinationText" xml:space="preserve">
    <value>Die Vereinbarung über die Durchführung der Dienstleistung, einschliesslich etwaiger Besichtigungstermine, erfolgt ausschliesslich direkt zwischen Partner und Kunde ausserhalb der Plattform TaskDotNet. Diese Vereinbarungen sind für beide Parteien verbindlich. Die Rolle der Plattform beschränkt sich ausschliesslich auf die Entgegennahme und Weiterleitung der Anfrage.</value>
  </data>
  <data name="PricesCurrencyTitle" xml:space="preserve">
    <value>Preise und Währung</value>
  </data>
  <data name="PricesCurrencyText" xml:space="preserve">
    <value>Die Auftragspreise werden den Partnern klar und transparent angezeigt. Die Abwicklung erfolgt ausschliesslich in Schweizer Franken (CHF).</value>
  </data>
  <data name="LiabilityTitle" xml:space="preserve">
    <value>Haftung und Schadensersatz</value>
  </data>
  <data name="LiabilityText" xml:space="preserve">
    <value>Der Kunde trägt die volle Verantwortung für die Richtigkeit und Vollständigkeit der von ihm eingegebenen Daten bei der Auftragserfassung. Der Partner ist verantwortlich für die Abgabe eines geeigneten Preisangebots sowie für die fachgerechte und termingerechte Ausführung der vereinbarten Dienstleistung ohne Verursachung von Schäden beim Kunden. Der ausführende Partner haftet allein gegenüber dem Kunden für sämtliche Schäden oder Verluste, die durch sein Verhalten, das seiner Mitarbeitenden oder sonstiger beauftragter Dritter entstehen. Weder diese Personen noch der Partner selbst können die Lightsoft GmbH in irgendeiner Weise – weder direkt noch indirekt – für Schäden oder Ausführungsmängel haftbar machen. Die Lightsoft GmbH lehnt jegliche rechtliche Haftung oder Stellungnahme bei Diebstahl oder Beschädigung von Materialien oder Eigentum im Zusammenhang mit der Dienstleistung kategorisch ab. Der Schutz und die Versicherung solcher Gegenstände sowie ein etwaiger Ersatz liegen ausschliesslich in der Verantwortung des ausführenden Partners.</value>
  </data>
</root>
