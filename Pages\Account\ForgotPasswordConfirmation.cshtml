﻿@page
@model ForgotPasswordConfirmation
@{
    ViewData["Title"] = "Forgot password confirmation";
}

<div class="container-xxl">
    <div class="authentication-wrapper authentication-basic container-p-y">
        <div class="authentication-inner">
            <!-- Register -->
            <div class="card">
                <div class="card-body">
                    <!-- Logo -->
                    <div class="app-brand justify-content-center">
                        <a href="index.html" class="app-brand-link gap-2">

                            <div class="img"> <img src="~/Dashboard/assets/img/logo.png" style="width: 200px;" alt=""></div>
                        </a>
                    </div>
                    <!-- /Logo -->
                    <h4 class="mb-2">@SharedLocalizer["ResetPassword"]</h4>
                    <p class="mb-4">@SharedLocalizer["ResetPasswordMessage"]</p>

                    <div class="text-center">
                        <a asp-controller="Account" asp-action="Login" class="d-flex align-items-center justify-content-center">
                            <i class="bx bx-chevron-left scaleX-n1-rtl bx-sm"></i>
                            @SharedLocalizer["Cancel"]
                        </a>
                    </div>
                </div>
            </div>
            <!-- /Register -->
        </div>
    </div>
</div>
