{"profiles": {"TaskDotNet": {"commandName": "Project", "launchBrowser": true, "launchUrl": "https://localhost:7220/", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7220;http://localhost:5032"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "https://localhost:7220", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}, "$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:27392", "sslPort": 44381}}}