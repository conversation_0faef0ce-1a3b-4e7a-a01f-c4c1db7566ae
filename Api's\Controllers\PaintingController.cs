﻿using Admin.TaskDotNet.Dtos;
using AutoMapper;
using Comman.Helper.Extensions;
using Comman.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Api_s.Interfaces;
using TaskDotNet.Api_s.Responses;
using TaskDotNet.Api_s.Services;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Helper;
using TaskDotNet.Models;
using TaskDotNet.Services;
using TaskDotNetal.Api_s.Models;

namespace TaskDotNet.Api_s.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PaintingController : ControllerBase
    {
        #region ctor
        private readonly IMapper mapper;
        private readonly IMailService mailService;
        private readonly IPaintingApiService paintingService;
        private readonly IEmailHtmlTemplateService emailHtmlTemplateService;
        private readonly OrderNumberService _orderNumberService;
        private readonly ApplicationDbContext _context;


        public PaintingController(IMapper mapper, IMailService mailService, IPaintingApiService paintingService, IEmailHtmlTemplateService emailHtmlTemplateService, ApplicationDbContext context)
        {
            this.mapper = mapper;
            this.mailService = mailService;
            this.paintingService = paintingService;
            this.emailHtmlTemplateService = emailHtmlTemplateService;
            _orderNumberService = new OrderNumberService();
            _context = context;
        }

        #endregion

        #region Create
        [HttpPost("CreatePainting")]
        public async Task<IActionResult> CreateAsync([FromBody] PaintingDto dto,string lang)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new CustomResponse { Code = "400", Message = "Invalid request data." });
            }
            var companyData = _context.Company.Select(m => new { m.Offers_PaintGips, m.Price_PaintGips }).FirstOrDefault(x => true);
            var kanton = _context.PostBoxCities.Where(m => m.PostBox == dto.PostBox)
                    .Select(m => m.Kanton)
                    .FirstOrDefault();

            dto.Kanton = kanton;
            var data = mapper.Map<Activity>(dto);
            data.ActivityType = ActivityType.PaintingAndGisper;
            data.Preis = companyData.Price_PaintGips;
            data.PaymentCount = companyData.Offers_PaintGips;
            data.Source = "TaskDotNet";

            data = await paintingService.Create(data);

            data.OrderNr = _orderNumberService.GenerateOrderNumber(data.ActivityType, data.Id);
            dto.OrderNr = data.OrderNr;

            await paintingService.Update(data);

            var ordersCount = await _context.OrdersCount.FirstOrDefaultAsync(m => m.WebsiteName == "TaskDotNet");
            ordersCount.T_Paint_Gips += 1;
            

            var notification = new ActivityNotification()
            {
                ActivityType = data.ActivityType,
                City = data.City,
                Name = data.Name,
                PostBox = data.PostBox,
                Salute = data.Salute,
                ExcuteDate = data.MovingDate,
            };
            _context.ActivityNotifications.Add(notification);

            await _context.SaveChangesAsync();

            // Prepare the response object
            var response = new ActivityResponse
            {
                Code = "200",
                Status = "Success",
                Message = "Panting And Gisper activity created successfully!",
                Data = data
            };

            string body = emailHtmlTemplateService.GetCustomerThankYouTemplate(data.Salute.GetDisplayName(), data.Name, lang);
            var mailRequest = new MailRequest
            {
                ToEmail = dto.Email,
                Subject = "TaskDotNet",
                Body = body
            };
            await mailService.SendEmailAsync(mailRequest, default);

            return Ok(response);
        }

        #endregion
    }
}
