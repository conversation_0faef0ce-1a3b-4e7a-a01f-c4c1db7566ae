﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.SpaProxy">
			<Version>6.*-*</Version>
		</PackageReference>
		<PackageReference Include="AutoMapper" Version="13.0.1" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="6.0.33" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="6.0.33" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.33" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.33">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.33" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.33">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="6.0.17" />
		<PackageReference Include="PostFinanceCheckout" Version="6.0.0" />
	</ItemGroup>


	<ItemGroup>
		<ProjectReference Include="..\Comman\Comman.csproj" />
		<ProjectReference Include="..\TaskDotNet.Localization\TaskDotNet.Localization.csproj" />
	</ItemGroup>
	
</Project>
