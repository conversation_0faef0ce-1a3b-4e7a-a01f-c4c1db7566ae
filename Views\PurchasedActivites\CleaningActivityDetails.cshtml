﻿@model MovementActivity
@using Comman.Helper.Extensions
@{
    ViewData["Title"] = SharedLocalizer["ActivityDetails"];
}

@section Links {
    <style>
        @@media print {
            body * {
                visibility: hidden;
            }

            body {
                background-color: white;
            }

            #ActivityContent, #ActivityContent * {
                visibility: visible;
            }

            #ActivityContent {
                position: absolute;
                left: 0px;
                top: -170px;
                width: 100%;
            }

            #cleaningSection {
                margin-top: 100px !important;
            }
            /* Force background colors to print */
            * {
                -webkit-print-color-adjust: exact; /* For Chrome/Safari */
                color-adjust: exact; /* Standard property */
            }
        }
    </style>
}

<div class="content-wrapper">
    <!-- Content -->
    <div class="container-fluid flex-grow-1 ">
        <h3 class="fw-bold text-white py-3 mb-4"> <a title="back" class="text-white" asp-action="Index"><i class='bx bxs-left-arrow-circle' style="font-size:2.1rem"></i></a> <span>@SharedLocalizer["Activity"] /</span> @SharedLocalizer["ActivityDetails"]</h3>

        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="d-flex justify-content-end card-header gap-5">
                        <button id="printActivity" class="btn btn-primary">@SharedLocalizer["Print"]</button>
                        <button id="downloadPDF" class="btn btn-primary">@SharedLocalizer["Download"]</button>
                        <a asp-action="EmailToCustomer" asp-route-id="@Model.Id" class="btn btn-primary">@SharedLocalizer["EmailToCustomer"]</a>
                    </div>
                    <!-- Account -->
                    <div class="card-body">
                        <div id="ActivityContent" style="max-width: 700px; margin: auto;font-size: 18px;">
                            <!-- header  -->
                            <h3 style="color:#008284;font-family: arial; margin: 0; text-align: center; border: 1px solid #008284">
                                @SharedLocalizer["NewRequest"]: #@Model.OrderNr
                            </h3>
                            <div style="padding-top: 10px">
                                <p style="padding-left: 20px">@SharedLocalizer["Hello"],</p>
                                <p style="padding-left: 20px">
                                    @SharedLocalizer["ThisIsRequiredRequest"]
                                </p>
                                <!-- separator  -->
                                <div style="background-color: #008284; height: 3px"></div>

                                <table style="margin: 20px 0;width: 100%">
                                    <tr>
                                        <td style="width: 40%; font-weight: bold">@SharedLocalizer["Category"]:</td>
                                        <td style="color: black;font-weight: bold;">@SharedLocalizer["Cleaning"]</td>
                                    </tr>
                                    <tr>
                                        <td style="font-weight: bold">@SharedLocalizer["CleaningDate"]:</td>
                                        <td style="color: black;font-weight: bold;">@Model.CleaningDate.ToString("dd.MM.yyyy")</td>
                                    </tr>
                                    <tr>
                                        <td style="font-weight: bold">@SharedLocalizer["HandOverDate"]:</td>
                                        <td style="color: black;font-weight: bold;">@Model.HandOverDate.ToString("dd.MM.yyyy")</td>
                                    </tr>
                                </table>

                                <table style="margin-bottom: 20px">
                                    <tbody>
                                        <tr>
                                            <td style="font-weight: bold; width: 30%; vertical-align: top">@SharedLocalizer["Customer"]:</td>
                                            <td style="vertical-align: top">
                                                <p style="margin: 0">
                                                    <span style="color: black;font-weight: bold;">@Model.Name</span><br />
                                                    @Model.Street, <br />
                                                    @Model.PostBox @Model.City <br />
                                                    <span style="color: black;font-weight: bold;">@Model.Phone </span><br />
                                                    <span style="color: black;font-weight: bold;">@Model.Email</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr style="height: 10px"></tr>
                                        <tr>
                                            <td style="font-weight: bold; vertical-align: top">@SharedLocalizer["Object"]:</td>
                                            <td>
                                                @Model.Room @SharedLocalizer["Room"]-@SharedLocalizer[Model.Object]<br />
                                                @Model.Area m²<br />
                                                @SharedLocalizer[Model.Floor]
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <!-- separator  -->
                                <div style="background-color: #008284; height: 4px; width: 60%; margin: 5px auto 0;"></div>

                                <table style="width: 100%; margin: 20px 0">
                                    <tbody>
                                        <tr>
                                            <td style="font-weight: bold;text-align: center;padding-bottom: 25px;" colspan="3">@SharedLocalizer["AdditionalServices"]:</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["CleaningType"]:</td>
                                            <td style="padding-left: 10px">@SharedLocalizer[Model.CleaningType]</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["SoilType"]:</td>
                                            <td style="padding-left: 10px">@SharedLocalizer[Model.SoilType]</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["CarpetCleaning"]:</td>
                                            <td style="padding-left: 10px">@(@Model.CarpetCleaning ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["HighPressure"]:</td>
                                            <td style="padding-left: 10px">@(@Model.HighPressure ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["Balcony"]:</td>
                                            <td style="padding-left: 10px">@(@Model.Balcony ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["CustomerFocus"]:</td>
                                            <td style="padding-left: 10px">@SharedLocalizer[Model.Focus]</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <p><span style="font-weight:bold">@SharedLocalizer["Notes"]:</span> @Model.Notes</p>

                                <!-- separator  -->
                                <div style="background-color: #008284; height: 4px; width: 60%; margin: 5px auto 0;"></div>

                                <p style="padding-top: 10px;font-weight: 800">@SharedLocalizer["Team"]</p>
                            </div>
                            <!-- /Account -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- / Content -->
        </div>
    </div>
</div>


@section Scripts {
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dompurify/3.0.5/purify.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script>
        // Use jQuery to handle the button click event
        $(document).ready(function () {
            $('#printActivity').on('click', function () {
                window.print();
            });

            $('#downloadPDF').on('click', function () {
                // Get the content of the div
                const content = document.getElementById('ActivityContent');
                $('#loader').show();
                // Initialize jsPDF with A4 dimensions (210mm x 297mm)
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF({
                    orientation: 'portrait', // Can also use 'landscape'
                    unit: 'mm', // Use millimeters for A4 dimensions
                    format: 'a4' // Set the format to A4
                });

                // Add the HTML content to the PDF
                doc.html(content, {
                    callback: function (doc) {
                        // Save the PDF
                        doc.save('@(Html.Raw(Model.ActivityType.GetDisplayName())).pdf');
                        $('#loader').hide();
                    },
                    x: 10, // Left margin (10mm)
                    y: 10, // Top margin (10mm)
                    width: 190, // Width of content (210mm - 20mm margins)
                    windowWidth: 700, // Match the div's width for scaling
                    autoPaging: 'text', // Enable automatic text-based pagination
                });
            });

        });

    </script>
}