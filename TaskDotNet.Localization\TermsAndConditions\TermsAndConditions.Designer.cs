﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace TaskDotNet.Localization.TermsAndConditions {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class TermsAndConditions {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal TermsAndConditions() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("TaskDotNet.Localization.TermsAndConditions.TermsAndConditions", typeof(TermsAndConditions).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ApplicableConditionsText {
            get {
                return ResourceManager.GetString("ApplicableConditionsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ApplicableConditionsTitle {
            get {
                return ResourceManager.GetString("ApplicableConditionsTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ConditionsValidityText {
            get {
                return ResourceManager.GetString("ConditionsValidityText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ConditionsValidityTitle {
            get {
                return ResourceManager.GetString("ConditionsValidityTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ContractDefinitionText {
            get {
                return ResourceManager.GetString("ContractDefinitionText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ContractDefinitionTitle {
            get {
                return ResourceManager.GetString("ContractDefinitionTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ContractingPartiesText {
            get {
                return ResourceManager.GetString("ContractingPartiesText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ContractingPartiesTitle {
            get {
                return ResourceManager.GetString("ContractingPartiesTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ContractValidityText {
            get {
                return ResourceManager.GetString("ContractValidityText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ContractValidityTitle {
            get {
                return ResourceManager.GetString("ContractValidityTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string JurisdictionText {
            get {
                return ResourceManager.GetString("JurisdictionText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string JurisdictionTitle {
            get {
                return ResourceManager.GetString("JurisdictionTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string LiabilityText {
            get {
                return ResourceManager.GetString("LiabilityText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string LiabilityTitle {
            get {
                return ResourceManager.GetString("LiabilityTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrderConfirmationText {
            get {
                return ResourceManager.GetString("OrderConfirmationText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrderConfirmationTitle {
            get {
                return ResourceManager.GetString("OrderConfirmationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrderEntryTimesText {
            get {
                return ResourceManager.GetString("OrderEntryTimesText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrderEntryTimesTitle {
            get {
                return ResourceManager.GetString("OrderEntryTimesTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrderForwardingText {
            get {
                return ResourceManager.GetString("OrderForwardingText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrderForwardingTitle {
            get {
                return ResourceManager.GetString("OrderForwardingTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrderRulesItem1 {
            get {
                return ResourceManager.GetString("OrderRulesItem1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrderRulesItem2 {
            get {
                return ResourceManager.GetString("OrderRulesItem2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrderRulesItem3 {
            get {
                return ResourceManager.GetString("OrderRulesItem3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrderRulesItem4 {
            get {
                return ResourceManager.GetString("OrderRulesItem4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrderRulesItem5 {
            get {
                return ResourceManager.GetString("OrderRulesItem5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrderRulesItem6 {
            get {
                return ResourceManager.GetString("OrderRulesItem6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrderRulesTitle {
            get {
                return ResourceManager.GetString("OrderRulesTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PartnerCustomerCoordinationText {
            get {
                return ResourceManager.GetString("PartnerCustomerCoordinationText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PartnerCustomerCoordinationTitle {
            get {
                return ResourceManager.GetString("PartnerCustomerCoordinationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PartnerRightsText {
            get {
                return ResourceManager.GetString("PartnerRightsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PartnerRightsTitle {
            get {
                return ResourceManager.GetString("PartnerRightsTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PricesCurrencyText {
            get {
                return ResourceManager.GetString("PricesCurrencyText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PricesCurrencyTitle {
            get {
                return ResourceManager.GetString("PricesCurrencyTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ScopeOfServicesText {
            get {
                return ResourceManager.GetString("ScopeOfServicesText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ScopeOfServicesTitle {
            get {
                return ResourceManager.GetString("ScopeOfServicesTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string TermsAndConditionsIntro {
            get {
                return ResourceManager.GetString("TermsAndConditionsIntro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string TermsAndConditionsTitle {
            get {
                return ResourceManager.GetString("TermsAndConditionsTitle", resourceCulture);
            }
        }
    }
}
