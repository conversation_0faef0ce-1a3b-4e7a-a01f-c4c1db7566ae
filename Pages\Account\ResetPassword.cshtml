﻿@page
@model ResetPasswordModel
@{
    ViewData["Title"] = "Reset password";
}


<div class="container-xxl">
    <div class="authentication-wrapper authentication-basic container-p-y">
        <div class="authentication-inner">

            <div class="card">
                <div class="card-body">
                    <!-- Logo -->
                    <div class="app-brand justify-content-center">
                        <a href="index.html" class="app-brand-link gap-2">

                            <div class="img"> <img src="~/Dashboard/assets/img/logo.png" style="width: 200px;" alt=""></div>
                        </a>
                    </div>
                    <!-- /Logo -->
                    <h4 class="mb-2">@SharedLocalizer["ResetPassword"]? 🔒</h4>
                    <p class="mb-4">@SharedLocalizer["NewPasswordMessage"]... </p>
                    <form asp-controller="Account" asp-action="ResetPassword" class="mb-3" method="POST">

                        <input type="hidden" asp-for="Input.Email" />
                        <input type="hidden" asp-for="Input.Code" />


                        <div class="mb-3 form-password-toggle">
                            <div class="d-flex justify-content-between">
                                <label class="form-label" style="font-size:20px" for="password">@SharedLocalizer["New Password"]</label>

                            </div>
                            <div class="input-group input-group-merge">
                                <input asp-for="Input.Password" class="form-control"
                                       placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;" />
                                <span class="input-group-text cursor-pointer"><i class="bx bx-hide"></i></span>
                            </div>
                        </div>

                        <div class="mb-3 form-password-toggle">
                            <div class="d-flex justify-content-between">
                                <label class="form-label" style="font-size:20px" for="password">@SharedLocalizer["Confirm New Password"]</label>
                            </div>
                            <div class="input-group input-group-merge">
                                <input asp-for="Input.ConfirmPassword" class="form-control"
                                       placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"/>
                                <span class="input-group-text cursor-pointer"><i class="bx bx-hide"></i></span>
                            </div>
                        </div>

                        <input type="submit" value="@SharedLocalizer["ResetPassword"]" class="btn btn-primary d-grid w-100" />
                    </form>

                </div>
            </div>
        </div>
    </div>
</div>

