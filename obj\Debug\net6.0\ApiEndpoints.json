[{"ContainingType": "TaskDotNet.Api_s.CleaningController", "Method": "CreateCleaning", "RelativePath": "api/Cleaning/CreateCleaning", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "TaskDotNet.DTOs.CleaningDto", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TaskDotNet.Api_s.Controllers.CompanyApiController", "Method": "GetCompanyData", "RelativePath": "api/CompanyApi/GetCompanyData", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "TaskDotNet.Api_s.Controllers.ContactUsApiController", "Method": "CreateAsync", "RelativePath": "api/ContactUsApi/ContactUs", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Admin.TaskDotNet.Dtos.ContactUsDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TaskDotNet.Api_s.Controllers.FilterController", "Method": "FilterCity", "RelativePath": "api/Filter/FilterCity", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "postboxName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TaskDotNet.Api_s.Controllers.MovingController", "Method": "AddInventoryWithMoving", "RelativePath": "api/Moving/AddInventoryWithMoving", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TaskDotNet.Api_s.Controllers.AddInventoryWithMovingRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TaskDotNet.Api_s.Controllers.MovingController", "Method": "CreateAsync", "RelativePath": "api/Moving/CreateMoving", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Admin.TaskDotNet.Dtos.MovingDto", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TaskDotNet.Api_s.Controllers.MovingController", "Method": "GetAllInventory", "RelativePath": "api/Moving/GetAllInventory", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "TaskDotNet.Api_s.MovingAndCleaningController", "Method": "AddInventoryWithMoving", "RelativePath": "api/MovingAndCleaning/AddInventoryWithMovingAndCleaning", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TaskDotNet.Api_s.AddInventoryWithMovingAndCleaningRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TaskDotNet.Api_s.MovingAndCleaningController", "Method": "CreateAsync", "RelativePath": "api/MovingAndCleaning/CreateMovingCleaning", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Admin.TaskDotNet.Dtos.MovingCleaningDto", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TaskDotNet.Api_s.Controllers.OtpController", "Method": "SendOtp", "RelativePath": "api/Otp/SendOtp", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TaskDotNet.Api_s.Controllers.OtpController", "Method": "VerifyOTP", "RelativePath": "api/Otp/VerifyOTP", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Admin.TaskDotNet.Dtos.OTPVerificationDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TaskDotNet.Api_s.Controllers.PaintingController", "Method": "CreateAsync", "RelativePath": "api/Painting/CreatePainting", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Admin.TaskDotNet.Dtos.PaintingDto", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TaskDotNet.Api_s.WorkersController", "Method": "CreateAsync", "RelativePath": "api/Workers/CreateWorkers", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Admin.TaskDotNet.Dtos.WorkersDto", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}]