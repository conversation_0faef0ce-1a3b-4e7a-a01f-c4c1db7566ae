﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Account" xml:space="preserve">
    <value>Konto</value>
  </data>
  <data name="Account Data" xml:space="preserve">
    <value>Kontodaten</value>
  </data>
  <data name="Account Settings" xml:space="preserve">
    <value>Profil Einstellungen</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Aktionen</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Aktiv</value>
  </data>
  <data name="Activites setup" xml:space="preserve">
    <value>Aktivitätseinstellungen anpassen</value>
  </data>
  <data name="Activities List" xml:space="preserve">
    <value>Anfragen</value>
  </data>
  <data name="New Activities" xml:space="preserve">
    <value>Neue Aktivitäten</value>
  </data>
  <data name="Mark as Checked" xml:space="preserve">
    <value>Als geprüft markieren</value>
  </data>
  <data name="Are you sure you want to mark this activity as checked?" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie diese Aktivität als geprüft markieren möchten?</value>
  </data>
  <data name="Delete Activity" xml:space="preserve">
    <value>Aktivität löschen</value>
  </data>
  <data name="Are you sure you want to delete this activity? This action cannot be undone!" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie diese Aktivität löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden!</value>
  </data>
  <data name="No New Activities" xml:space="preserve">
    <value>Keine neuen Aktivitäten</value>
  </data>
  <data name="All activities have been checked." xml:space="preserve">
    <value>Alle Aktivitäten wurden geprüft.</value>
  </data>
  <data name="ActivityType" xml:space="preserve">
    <value>Aktivitätstyp</value>
  </data>
  <data name="MovingDate" xml:space="preserve">
    <value>Umzugsdatum</value>
  </data>
  <data name="Kanton" xml:space="preserve">
    <value>Kanton</value>
  </data>
  <data name="Activity" xml:space="preserve">
    <value>Anfragen</value>
  </data>
  <data name="ActivityDetails" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="ActivityDetailsTitle" xml:space="preserve">
    <value>Die ausgewählte Anfrage</value>
  </data>
  <data name="Add to archive" xml:space="preserve">
    <value>Im Archiv ablegen</value>
  </data>
  <data name="AddAmount" xml:space="preserve">
    <value>Betrag hinzufügen</value>
  </data>
  <data name="AdditionalServices" xml:space="preserve">
    <value>Zusätzliche Dienstleistungen</value>
  </data>
  <data name="Additive" xml:space="preserve">
    <value>Zusatz</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Adresse</value>
  </data>
  <data name="Administrator" xml:space="preserve">
    <value>Administrator</value>
  </data>
  <data name="AdvertisingList" xml:space="preserve">
    <value>Branchenverzeichnis für Werbezwecke</value>
  </data>
  <data name="Albanian" xml:space="preserve">
    <value>Albanisch</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Alle</value>
  </data>
  <data name="All Activity" xml:space="preserve">
    <value>Alle Aktivitäten</value>
  </data>
  <data name="All Paertners" xml:space="preserve">
    <value>Partnerliste</value>
  </data>
  <data name="AlreadyPurchased" xml:space="preserve">
    <value>Sie haben diesen Auftrag bereits gekauft. Ein erneuter Kauf ist nicht möglich.</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Anzahl</value>
  </data>
  <data name="AmountMustBeGreaterThanZero" xml:space="preserve">
    <value>Der Betrag muss grösser als Null sein.</value>
  </data>
  <data name="Apartment" xml:space="preserve">
    <value>Wohnung</value>
  </data>
  <data name="Approx." xml:space="preserve">
    <value>Ca.</value>
  </data>
  <data name="Arabic" xml:space="preserve">
    <value>Arabisch</value>
  </data>
  <data name="Archive" xml:space="preserve">
    <value>Archiv</value>
  </data>
  <data name="Archive Content" xml:space="preserve">
    <value>Archivinhalte</value>
  </data>
  <data name="Archive entry" xml:space="preserve">
    <value>Eintrag archivieren: </value>
  </data>
  <data name="Are you sure you want to delete the selected entry?" xml:space="preserve">
    <value>Möchten Sie den ausgewählten Datensatz wirklich löschen?</value>
  </data>
  <data name="Area" xml:space="preserve">
    <value>Area</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>Möchten Sie mit dem Zurücksetzen fortfahren?</value>
  </data>
  <data name="AssembleFurniture" xml:space="preserve">
    <value>Möbel montieren</value>
  </data>
  <data name="AT_Austria" xml:space="preserve">
    <value>AT–Österreich</value>
  </data>
  <data name="Auszug" xml:space="preserve">
    <value>Auszug</value>
  </data>
  <data name="Background" xml:space="preserve">
    <value>Hintergrund</value>
  </data>
  <data name="BackToList" xml:space="preserve">
    <value>Zurück zur Liste</value>
  </data>
  <data name="BadRequestMessage" xml:space="preserve">
    <value>Etwas ist schief gelaufen. Bitte versuche es später noch einmal.</value>
  </data>
  <data name="BadRequestTitle" xml:space="preserve">
    <value>Fehlerhafte Anfrage ⚠️</value>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>Saldo</value>
  </data>
  <data name="BalanceRechargeHistory" xml:space="preserve">
    <value>Guthabenaufladehistorie</value>
  </data>
  <data name="Balcony" xml:space="preserve">
    <value>Balkon</value>
  </data>
  <data name="Bank" xml:space="preserve">
    <value>Bank</value>
  </data>
  <data name="Bank Name" xml:space="preserve">
    <value>Bankname</value>
  </data>
  <data name="Basement, Cellar" xml:space="preserve">
    <value>Keller</value>
  </data>
  <data name="Basic Cleaning" xml:space="preserve">
    <value>Baureinigung</value>
  </data>
  <data name="Blocked" xml:space="preserve">
    <value>Blockiert</value>
  </data>
  <data name="Both" xml:space="preserve">
    <value>Beide zusammen</value>
  </data>
  <data name="BoxCity" xml:space="preserve">
    <value>PLZ / Ort</value>
  </data>
  <data name="Boxes" xml:space="preserve">
    <value>Kartons</value>
  </data>
  <data name="Branch" xml:space="preserve">
    <value>Branche</value>
  </data>
  <data name="Building Cleaning" xml:space="preserve">
    <value>Gebäudereinigung</value>
  </data>
  <data name="Business" xml:space="preserve">
    <value>Geschäft</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Abbrechen</value>
  </data>
  <data name="Card number" xml:space="preserve">
    <value>Kartennumber</value>
  </data>
  <data name="Cardboard boxes" xml:space="preserve">
    <value>Kartons</value>
  </data>
  <data name="CarpetCleaning" xml:space="preserve">
    <value>Teppichreinigung</value>
  </data>
  <data name="Carpets" xml:space="preserve">
    <value>Teppiche</value>
  </data>
  <data name="Cartons" xml:space="preserve">
    <value>Umzugskartons</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Kategorie</value>
  </data>
  <data name="Category (room)" xml:space="preserve">
    <value>Kategorie (Raum)</value>
  </data>
  <data name="Cellar" xml:space="preserve">
    <value>Keller</value>
  </data>
  <data name="Change password" xml:space="preserve">
    <value>Kennwort ändern</value>
  </data>
  <data name="ChangePasswordHint" xml:space="preserve">
    <value>Lassen Sie dieses Feld leer, wenn keine Änderung gewünscht ist.</value>
  </data>
  <data name="Checkout" xml:space="preserve">
    <value>Zur Kasse…</value>
  </data>
  <data name="CheckoutRedirectMessage" xml:space="preserve">
    <value>Sie sind dabei, Ihr Guthaben aufzuladen. Möchten Sie fortfahren?</value>
  </data>
  <data name="Choose email content" xml:space="preserve">
    <value>Nachricht auswählen</value>
  </data>
  <data name="ChooseActivity" xml:space="preserve">
    <value>Wählen Sie die passende Aktivität</value>
  </data>
  <data name="CH_Switzerland" xml:space="preserve">
    <value>CH-Schweiz</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Stadt</value>
  </data>
  <data name="Cleaning" xml:space="preserve">
    <value>Reinigung</value>
  </data>
  <data name="CleaningAndHandover" xml:space="preserve">
    <value>Reinigung und Übergabe einer</value>
  </data>
  <data name="CleaningDate" xml:space="preserve">
    <value>Reinigungsdatum</value>
  </data>
  <data name="CleaningType" xml:space="preserve">
    <value>Reinigungstyp</value>
  </data>
  <data name="Click buttons to filter by activity" xml:space="preserve">
    <value>Klicken Sie auf die Schaltflächen, um nach Aktivität zu filtern</value>
  </data>
  <data name="Click to buy" xml:space="preserve">
    <value>Kaufen</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Firma</value>
  </data>
  <data name="Company Data" xml:space="preserve">
    <value>Firmen Daten</value>
  </data>
  <data name="Company Move" xml:space="preserve">
    <value>Firmenmzug</value>
  </data>
  <data name="Company Name" xml:space="preserve">
    <value>Firma</value>
  </data>
  <data name="CompanyDetails" xml:space="preserve">
    <value>Firmendetails</value>
  </data>
  <data name="CompareAttribute_MustMatch" xml:space="preserve">
    <value>Die Felder {0} und {1} müssen übereinstimmen.</value>
  </data>
  <data name="CompleteProfileMessage" xml:space="preserve">
    <value>Bitte vervollständigen Sie Ihre Profilinformationen, um die Registrierung korrekt abzuschliessen!</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>Bestätigen</value>
  </data>
  <data name="Confirm New Password" xml:space="preserve">
    <value>Bitte bestätigen Sie das neue Passwort.</value>
  </data>
  <data name="Confirm Password" xml:space="preserve">
    <value>Passwort bestätigen</value>
  </data>
  <data name="ConfirmationCode" xml:space="preserve">
    <value>Bestätigungscode</value>
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value>Bestätigt</value>
  </data>
  <data name="ConfirmMessage" xml:space="preserve">
    <value>Wir haben Ihnen einen Bestätigungscode per E-Mail gesendet. Bitte geben Sie diesen hier ein.</value>
  </data>
  <data name="Construction Cleaning" xml:space="preserve">
    <value>Construction Cleaning</value>
  </data>
  <data name="Contact person" xml:space="preserve">
    <value>Kontaktperson</value>
  </data>
  <data name="ContactDetails" xml:space="preserve">
    <value>Kontaktdaten</value>
  </data>
  <data name="ContactSubTitle" xml:space="preserve">
    <value>Detaillierte Informationen zur Kontaktanfrage</value>
  </data>
  <data name="ContactUs" xml:space="preserve">
    <value>Kontakt</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>Erstellen</value>
  </data>
  <data name="Create a room" xml:space="preserve">
    <value>Ein Raum erfassen</value>
  </data>
  <data name="Create entry" xml:space="preserve">
    <value>Neuer Eintrag</value>
  </data>
  <data name="Create room content" xml:space="preserve">
    <value>Rauminhalte hinzufügen</value>
  </data>
  <data name="Credit card" xml:space="preserve">
    <value>Kreditkarte</value>
  </data>
  <data name="Croatian" xml:space="preserve">
    <value>Kroatisch</value>
  </data>
  <data name="Crook" xml:space="preserve">
    <value>Gauner</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Währung</value>
  </data>
  <data name="Current Balance" xml:space="preserve">
    <value>Aktueller Saldo</value>
  </data>
  <data name="Current balance: CHF" xml:space="preserve">
    <value>Aktueller Saldo: CHF</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Kunde</value>
  </data>
  <data name="CustomerFocus" xml:space="preserve">
    <value>Kundenfokus</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Informationsblatt</value>
  </data>
  <data name="Data imported successfully." xml:space="preserve">
    <value>Die Daten wurden erfolgreich importiert.</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Datum</value>
  </data>
  <data name="DateFrom" xml:space="preserve">
    <value>Bewegungen löschen: von</value>
  </data>
  <data name="Daten reinigen" xml:space="preserve">
    <value>Archivdaten bereinigen</value>
  </data>
  <data name="DateTo" xml:space="preserve">
    <value>bis</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="Delete record" xml:space="preserve">
    <value>Eintrag löschen</value>
  </data>
  <data name="Delete Selected" xml:space="preserve">
    <value>Gewählte Einträge löschen</value>
  </data>
  <data name="Depending on the card type, you will find these in the marked position on the back of the card" xml:space="preserve">
    <value>Sie finden diese je nach Kartentyp an der markierten Stelle auf der Kartenrückseite.</value>
  </data>
  <data name="DE_Germany" xml:space="preserve">
    <value>DE-Deutsch</value>
  </data>
  <data name="Different" xml:space="preserve">
    <value>Verschiedene</value>
  </data>
  <data name="DismantleFurniture" xml:space="preserve">
    <value>Möbel abbauen</value>
  </data>
  <data name="DismantleLamp" xml:space="preserve">
    <value>Lampe abbauen</value>
  </data>
  <data name="Disposal" xml:space="preserve">
    <value>Entsorgung</value>
  </data>
  <data name="Distance" xml:space="preserve">
    <value>Distanz</value>
  </data>
  <data name="Doors" xml:space="preserve">
    <value>Türen</value>
  </data>
  <data name="DoReset" xml:space="preserve">
    <value>Sind Sie sicher?</value>
  </data>
  <data name="Download" xml:space="preserve">
    <value>Herunterladen</value>
  </data>
  <data name="ExportExcel" xml:space="preserve">
    <value>Excel exportieren</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Bearbeiten</value>
  </data>
  <data name="Edit archive data" xml:space="preserve">
    <value>Archivdaten bearbeiten</value>
  </data>
  <data name="Edit category (rooms)" xml:space="preserve">
    <value>Kategorie (Räume) bearbeiten</value>
  </data>
  <data name="Edit entry" xml:space="preserve">
    <value>Branchenadresse bearbeiten</value>
  </data>
  <data name="Edit messages" xml:space="preserve">
    <value>Nachrichten bearbeiten</value>
  </data>
  <data name="Edit objects" xml:space="preserve">
    <value>Gegenstände bearbeiten</value>
  </data>
  <data name="Edit partner profile" xml:space="preserve">
    <value>Partnerprofil bearbeiten</value>
  </data>
  <data name="Edit Profile Data" xml:space="preserve">
    <value>Profildaten bearbeiten</value>
  </data>
  <data name="Edit room content" xml:space="preserve">
    <value>Rauminhalt bearbeiten</value>
  </data>
  <data name="efh" xml:space="preserve">
    <value>EFH</value>
  </data>
  <data name="EightFloor" xml:space="preserve">
    <value>8. Etage</value>
  </data>
  <data name="Einzug" xml:space="preserve">
    <value>Einzug</value>
  </data>
  <data name="Electrician" xml:space="preserve">
    <value>Elektrikerarbeiten</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="Email confirm" xml:space="preserve">
    <value>Email bestätigt</value>
  </data>
  <data name="Email senden" xml:space="preserve">
    <value>Werbemails</value>
  </data>
  <data name="Email texts" xml:space="preserve">
    <value>Korrespondenztexte vorbereiten</value>
  </data>
  <data name="EmailAddressAttribute_ValidationError" xml:space="preserve">
    <value>Das Feld {0} enthält keine gültige E-Mail-Adresse.</value>
  </data>
  <data name="EmailAlreadyExistsError" xml:space="preserve">
    <value>Die E-Mail-Adresse existiert bereits.</value>
  </data>
  <data name="EmailConfirmationMessage1" xml:space="preserve">
    <value>Du hast es fast geschafft!</value>
  </data>
  <data name="EmailConfirmationMessage2" xml:space="preserve">
    <value>Nur noch einer** Schritt, um loszulegen!</value>
  </data>
  <data name="EmailConfirmationMessage3" xml:space="preserve">
    <value>E-Mail-Verifizierung</value>
  </data>
  <data name="EmailConfirmationMessage4" xml:space="preserve">
    <value>Eine E-Mail mit einem Bestätigungscode wird an Sie gesendet.</value>
  </data>
  <data name="EmailConfirmationMessage5" xml:space="preserve">
    <value>Nach dem Absenden geben Sie bitte den Bestätigungscode ein, um Ihre Identität zu bestätigen.</value>
  </data>
  <data name="EmailSenden" xml:space="preserve">
    <value>E-Mail senden</value>
  </data>
  <data name="EmailSentSuccessfully" xml:space="preserve">
    <value>Die E-Mail wurde erfolgreich versendet.</value>
  </data>
  <data name="EmailToCustomer" xml:space="preserve">
    <value>Email an Kunde</value>
  </data>
  <data name="EmptyOrdersMessage" xml:space="preserve">
    <value>Versuchen Sie es bitte zu einem späteren Zeitpunkt noch einmal.</value>
  </data>
  <data name="EmptyOrdersTitle" xml:space="preserve">
    <value>Zurzeit liegen keine Bestellungen vor.</value>
  </data>
  <data name="EmptyPurchasedOrders" xml:space="preserve">
    <value>Sie haben bislang noch keine Anfragen gekauft.</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>Englisch</value>
  </data>
  <data name="Enter the ID number. a" xml:space="preserve">
    <value>Geben Sie die ID-Nr. ein</value>
  </data>
  <data name="EnterCompanyName" xml:space="preserve">
    <value>Geben Sie den Firmennamen ein</value>
  </data>
  <data name="EnterYourEmail" xml:space="preserve">
    <value>Geben Sie Ihre E-Mail-Adresse ein</value>
  </data>
  <data name="Evaluation" xml:space="preserve">
    <value>Auswertung</value>
  </data>
  <data name="Event Cleaning" xml:space="preserve">
    <value>Eventreinigung</value>
  </data>
  <data name="Excellent" xml:space="preserve">
    <value>Exzellent</value>
  </data>
  <data name="Excerpt from" xml:space="preserve">
    <value>Auszug von</value>
  </data>
  <data name="Exec. Date" xml:space="preserve">
    <value>Datum</value>
  </data>
  <data name="ExecDate" xml:space="preserve">
    <value>Ausführungsdatum</value>
  </data>
  <data name="ExecutionDate" xml:space="preserve">
    <value>Ausführungsdatum</value>
  </data>
  <data name="Expiry Date" xml:space="preserve">
    <value>Verfallsdatum</value>
  </data>
  <data name="External" xml:space="preserve">
    <value>Extern</value>
  </data>
  <data name="Final Cleaning" xml:space="preserve">
    <value>Endreinigung</value>
  </data>
  <data name="FiveFloor" xml:space="preserve">
    <value>5. Etage</value>
  </data>
  <data name="Flexibility" xml:space="preserve">
    <value>Flexibilität</value>
  </data>
  <data name="Flexible" xml:space="preserve">
    <value>Flexibilität</value>
  </data>
  <data name="Floor" xml:space="preserve">
    <value>Etage</value>
  </data>
  <data name="Floor Cleaning" xml:space="preserve">
    <value>Bodenreienigung</value>
  </data>
  <data name="FloorAndPanels" xml:space="preserve">
    <value>Böden und Paneele</value>
  </data>
  <data name="Focus" xml:space="preserve">
    <value>Kundenfokus</value>
  </data>
  <data name="ForbiddenMessage" xml:space="preserve">
    <value>Der Zugriff auf diese Ressource ist nicht erlaubt.</value>
  </data>
  <data name="ForbiddenTitle" xml:space="preserve">
    <value>Zugriff verweigert 🚫</value>
  </data>
  <data name="ForgotPassword" xml:space="preserve">
    <value>Passwort vergessen?</value>
  </data>
  <data name="ForgotPasswordMessage" xml:space="preserve">
    <value>Bitte geben Sie Ihre E-Mail-Adresse ein. Wir schicken Ihnen dann Anweisungen zum Zurücksetzen Ihres Passworts.</value>
  </data>
  <data name="FourFloor" xml:space="preserve">
    <value>4. Etage</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>Französisch</value>
  </data>
  <data name="From" xml:space="preserve">
    <value>Von</value>
  </data>
  <data name="Furniture" xml:space="preserve">
    <value>Möbel</value>
  </data>
  <data name="FurnitureAssembly" xml:space="preserve">
    <value>Möbelmontage</value>
  </data>
  <data name="FurnitureLift" xml:space="preserve">
    <value>Möbelaufzug</value>
  </data>
  <data name="Garage" xml:space="preserve">
    <value>Garage</value>
  </data>
  <data name="Garden" xml:space="preserve">
    <value>Garten</value>
  </data>
  <data name="Gastronomy" xml:space="preserve">
    <value>Gastronomie</value>
  </data>
  <data name="General statistics" xml:space="preserve">
    <value>Allgemeine Statistiken</value>
  </data>
  <data name="German" xml:space="preserve">
    <value>Deutsch</value>
  </data>
  <data name="Gisper" xml:space="preserve">
    <value>Gipsarbeiter</value>
  </data>
  <data name="Good" xml:space="preserve">
    <value>Gut</value>
  </data>
  <data name="GreaterTenFloor" xml:space="preserve">
    <value>&gt; 10. Etage</value>
  </data>
  <data name="HandOverDate" xml:space="preserve">
    <value>Übergabedatum</value>
  </data>
  <data name="HasInventory" xml:space="preserve">
    <value>Inventar</value>
  </data>
  <data name="HaveAccount" xml:space="preserve">
    <value>Noch kein Konto?</value>
  </data>
  <data name="Healthcare" xml:space="preserve">
    <value>Gesundheitswesen</value>
  </data>
  <data name="HeatingAndEnergy" xml:space="preserve">
    <value>Heizung und Energie</value>
  </data>
  <data name="HeavyLoad" xml:space="preserve">
    <value>Schwere Ladung</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>Guten Tag</value>
  </data>
  <data name="HighPressure" xml:space="preserve">
    <value>Hochdruck</value>
  </data>
  <data name="Hotel Cleaning" xml:space="preserve">
    <value>Hotelreinigung</value>
  </data>
  <data name="Hotel industry" xml:space="preserve">
    <value>Hotellerie</value>
  </data>
  <data name="House Keeping" xml:space="preserve">
    <value>Hauswartung</value>
  </data>
  <data name="House maintenance" xml:space="preserve">
    <value>Hauswartung</value>
  </data>
  <data name="I have read and agree to the general terms and conditions for purchasing inquiries online via TaskDotNets" xml:space="preserve">
    <value>Ich bestätige, die allgemeinen Geschäftsbedingungen (AGB) für den Online-Kauf von Anfragen auf TaskDotNets.com gelesen zu haben und stimme diesen zu.</value>
  </data>
  <data name="IBAN" xml:space="preserve">
    <value>IBAN-Nr.</value>
  </data>
  <data name="ID Number" xml:space="preserve">
    <value>ID-Nummer</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>Bild</value>
  </data>
  <data name="IndividualActivity" xml:space="preserve">
    <value>individuelle Tätigkeit</value>
  </data>
  <data name="Industrial Cleaning" xml:space="preserve">
    <value>Industriereinigung</value>
  </data>
  <data name="Information Sheet" xml:space="preserve">
    <value>Informationsblatt</value>
  </data>
  <data name="Inspection" xml:space="preserve">
    <value>Besichtigung</value>
  </data>
  <data name="InstallLamp" xml:space="preserve">
    <value>Lampen installieren</value>
  </data>
  <data name="Internal" xml:space="preserve">
    <value>Intern</value>
  </data>
  <data name="Internal/External" xml:space="preserve">
    <value>Intern / Extern</value>
  </data>
  <data name="InternalServerErrorMessage" xml:space="preserve">
    <value>Interner Serverfehler. Bitte kontaktieren Sie den Support, falls das Problem weiterhin besteht.</value>
  </data>
  <data name="InternalServerErrorTitle" xml:space="preserve">
    <value>Interner Serverfehler 💥</value>
  </data>
  <data name="International Move" xml:space="preserve">
    <value>Internationalumzug</value>
  </data>
  <data name="InvalidAmount" xml:space="preserve">
    <value>Ungültiger Betrag</value>
  </data>
  <data name="InvalidInput" xml:space="preserve">
    <value>Ungültige Eingabe</value>
  </data>
  <data name="Inventory" xml:space="preserve">
    <value>Inventar (Räume)</value>
  </data>
  <data name="Inventory Items" xml:space="preserve">
    <value>Inventar (Inhalt)</value>
  </data>
  <data name="Inventory list" xml:space="preserve">
    <value>Inventarliste</value>
  </data>
  <data name="InventoryItem" xml:space="preserve">
    <value>Inventargegenstand</value>
  </data>
  <data name="Italian" xml:space="preserve">
    <value>Italienisch</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>Gegenstände</value>
  </data>
  <data name="ItemsList" xml:space="preserve">
    <value>Rauminhalte hinzufügen und bearbeiten</value>
  </data>
  <data name="KantonWorkingIn" xml:space="preserve">
    <value>Bitte wählen Sie die Kantone aus, in denen Sie arbeiten möchten</value>
  </data>
  <data name="KitchenConstruction" xml:space="preserve">
    <value>Küchenbau</value>
  </data>
  <data name="Kurdish" xml:space="preserve">
    <value>Kurdisch</value>
  </data>
  <data name="Laminate" xml:space="preserve">
    <value>Laminat</value>
  </data>
  <data name="Lamps" xml:space="preserve">
    <value>Lampen</value>
  </data>
  <data name="Land" xml:space="preserve">
    <value>Land</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Sprache</value>
  </data>
  <data name="Lift" xml:space="preserve">
    <value>Lift</value>
  </data>
  <data name="Linoleum" xml:space="preserve">
    <value>Linoleum</value>
  </data>
  <data name="Locksmith" xml:space="preserve">
    <value>Schlosserarbeiten</value>
  </data>
  <data name="Log Out" xml:space="preserve">
    <value>Ausloggen</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Einloggen</value>
  </data>
  <data name="loginDesc" xml:space="preserve">
    <value>Alles im Griff - smarter, schneller, besser organisiert</value>
  </data>
  <data name="LoginHeader" xml:space="preserve">
    <value>Hier erhalten Sie vollständigen Zugriff auf alle Anfragen und Informationen.</value>
  </data>
  <data name="Maintenance Cleaning" xml:space="preserve">
    <value>Unterhaltsreinigung</value>
  </data>
  <data name="Manage Users" xml:space="preserve">
    <value>Benutzer verwalten</value>
  </data>
  <data name="Maximum offers" xml:space="preserve">
    <value>Anzahl der erlaubten Verkäufe</value>
  </data>
  <data name="Mechanic" xml:space="preserve">
    <value>Mechanikerleistungen</value>
  </data>
  <data name="Message" xml:space="preserve">
    <value>Nachricht</value>
  </data>
  <data name="Miss" xml:space="preserve">
    <value>Fräulein</value>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="MoreWork" xml:space="preserve">
    <value>Weitere Arbeiten</value>
  </data>
  <data name="Move And Clean" xml:space="preserve">
    <value>Umzug and Reinigung</value>
  </data>
  <data name="MoveFrom" xml:space="preserve">
    <value>Auszug von</value>
  </data>
  <data name="MoveTo" xml:space="preserve">
    <value>nach</value>
  </data>
  <data name="Moving" xml:space="preserve">
    <value>Umzug </value>
  </data>
  <data name="Moving &amp; Cleaning" xml:space="preserve">
    <value>Umzug &amp; Reinigung</value>
  </data>
  <data name="Moving and Cleaning" xml:space="preserve">
    <value>Umzug und Reinigung</value>
  </data>
  <data name="Moving in" xml:space="preserve">
    <value>Umzug nach</value>
  </data>
  <data name="MovingAndCleaning" xml:space="preserve">
    <value>Umzug &amp; Reinigung</value>
  </data>
  <data name="MovingBoxes" xml:space="preserve">
    <value>Umzugskisten</value>
  </data>
  <data name="Mr" xml:space="preserve">
    <value>Herr</value>
  </data>
  <data name="Mrs" xml:space="preserve">
    <value>Frau</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Name in English" xml:space="preserve">
    <value>Gegenstand in English</value>
  </data>
  <data name="Name in French" xml:space="preserve">
    <value>Gegenstand in Fransözisch</value>
  </data>
  <data name="Name in German" xml:space="preserve">
    <value>Gegenstand in German</value>
  </data>
  <data name="Name in Italian" xml:space="preserve">
    <value>Gegenstand in Italien</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Neu</value>
  </data>
  <data name="New entry" xml:space="preserve">
    <value>Neuer Eintrag</value>
  </data>
  <data name="New message" xml:space="preserve">
    <value>Neue Nachricht</value>
  </data>
  <data name="New Password" xml:space="preserve">
    <value>Neues Passwort</value>
  </data>
  <data name="NewPasswordMessage" xml:space="preserve">
    <value>Neues Passwort eingeben und bestätigen.</value>
  </data>
  <data name="NewRequest" xml:space="preserve">
    <value>Neue Anfrage</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>Nächste</value>
  </data>
  <data name="NineFloor" xml:space="preserve">
    <value>9. Etage</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Nein</value>
  </data>
  <data name="NotEnoughBalance" xml:space="preserve">
    <value>Ihr aktuelles Guthaben reicht nicht aus. Möchten Sie trotzdem fortfahren?</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Anmerkungen</value>
  </data>
  <data name="NotFoundMessage" xml:space="preserve">
    <value>Wir konnten die angeforderte Seite nicht finden.</value>
  </data>
  <data name="NotFoundTitle" xml:space="preserve">
    <value>Seite nicht gefunden ⚠️</value>
  </data>
  <data name="Nothing" xml:space="preserve">
    <value>Keine</value>
  </data>
  <data name="NotRequired" xml:space="preserve">
    <value>Nicht erforderlich</value>
  </data>
  <data name="Number" xml:space="preserve">
    <value>Anzahl</value>
  </data>
  <data name="Object" xml:space="preserve">
    <value>Objekt</value>
  </data>
  <data name="Office Cleaning" xml:space="preserve">
    <value>Büroreinigung</value>
  </data>
  <data name="Office Rooms" xml:space="preserve">
    <value>Büroräume</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Old Paassword" xml:space="preserve">
    <value>Altes Passwort</value>
  </data>
  <data name="OneDay" xml:space="preserve">
    <value>1 Tag</value>
  </data>
  <data name="OneFloor" xml:space="preserve">
    <value>1. Etage</value>
  </data>
  <data name="OneMonth" xml:space="preserve">
    <value>1 Monat</value>
  </data>
  <data name="OneWeek" xml:space="preserve">
    <value>1 Woche</value>
  </data>
  <data name="Order-Nr" xml:space="preserve">
    <value>Anfrage-Nr</value>
  </data>
  <data name="OrderCountTitle" xml:space="preserve">
    <value>Anfragekontrollformular</value>
  </data>
  <data name="OrderNumber" xml:space="preserve">
    <value>Auftragsnummer</value>
  </data>
  <data name="OrdersList" xml:space="preserve">
    <value>Auftragsliste</value>
  </data>
  <data name="OrderType" xml:space="preserve">
    <value>Auftragstyp</value>
  </data>
  <data name="OurPartner" xml:space="preserve">
    <value>Unser Partner</value>
  </data>
  <data name="Owner" xml:space="preserve">
    <value>Eigentümer</value>
  </data>
  <data name="Packing" xml:space="preserve">
    <value>Verpacken</value>
  </data>
  <data name="Pages" xml:space="preserve">
    <value>Seiten</value>
  </data>
  <data name="Paid costs CHF" xml:space="preserve">
    <value>Bezahlte Kosten CHF</value>
  </data>
  <data name="Paid costs CHF1" xml:space="preserve">
    <value>Bezahlte Kosten CHF</value>
  </data>
  <data name="Painting" xml:space="preserve">
    <value>Malen</value>
  </data>
  <data name="Painting &amp; Gipser" xml:space="preserve">
    <value>Maler &amp; Gipsarbeiter</value>
  </data>
  <data name="PaintingAndGisper" xml:space="preserve">
    <value>Maler &amp; Gipsarbeiter</value>
  </data>
  <data name="Parquet" xml:space="preserve">
    <value>Parkett</value>
  </data>
  <data name="Partner Data Updated Successfully" xml:space="preserve">
    <value>Partnerdaten erfolgreich aktualisiert</value>
  </data>
  <data name="Partner Profile" xml:space="preserve">
    <value>Partnerprofil</value>
  </data>
  <data name="Partner Status" xml:space="preserve">
    <value>Partnerstatus</value>
  </data>
  <data name="Partner-No" xml:space="preserve">
    <value>Partner-Nr.</value>
  </data>
  <data name="PartnerBalanceHistory" xml:space="preserve">
    <value>Ladevorgänge</value>
  </data>
  <data name="PartnerDataUpdatedSuccessfully" xml:space="preserve">
    <value>Partnerdaten erfolgreich aktualisiert.</value>
  </data>
  <data name="PartnerList" xml:space="preserve">
    <value>Partnerliste</value>
  </data>
  <data name="PartnerOrders" xml:space="preserve">
    <value>Partneraufträge</value>
  </data>
  <data name="PartnerOrdersReport" xml:space="preserve">
    <value>Partnerauftragsbericht</value>
  </data>
  <data name="Partner_Block_UID" xml:space="preserve">
    <value>Zugriff verweigert: Ihr Zugang zur Plattform wurde aufgrund früherer Verstöße dauerhaft deaktiviert. Eine erneute Registrierung ist nicht möglich.
            Bei Fragen wenden Sie sich <NAME_EMAIL>.</value>
  </data>
  <data name="Partner_Dashboard01" xml:space="preserve">
    <value>Wählen Sie passende Anfrage nach Tätigkeitsart und Region aus.  Es werden Ihnen nur passende angezeigt. </value>
  </data>
  <data name="Partner_Dashboard02" xml:space="preserve">
    <value>Reservieren Sie schnell und kontaktieren Sie den Kunden direkt, um einen Termin zu vereinbaren oder ein Angebot zu senden</value>
  </data>
  <data name="Partner_Dashboard03" xml:space="preserve">
    <value>Der Kunde erwartet ein passendes Angebot, Zuverlässigkeit und Pünktlichkeit.</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Passwort</value>
  </data>
  <data name="Pay CHF" xml:space="preserve">
    <value>Bezahlen CHF</value>
  </data>
  <data name="Pay now" xml:space="preserve">
    <value>Bezahlen Sie jetzt</value>
  </data>
  <data name="Payment" xml:space="preserve">
    <value>Zahlung</value>
  </data>
  <data name="PaymentFehler" xml:space="preserve">
    <value>Bei der Zahlung ist ein Fehler aufgetreten.</value>
  </data>
  <data name="PaymentMethod" xml:space="preserve">
    <value>Zahlungsmethode</value>
  </data>
  <data name="PaymentSuccesfully" xml:space="preserve">
    <value>Die Zahlung wurde erfolgreich durchgeführt.</value>
  </data>
  <data name="Period" xml:space="preserve">
    <value>Zeitraum</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Telefon</value>
  </data>
  <data name="Piano" xml:space="preserve">
    <value>Klavier</value>
  </data>
  <data name="Plates" xml:space="preserve">
    <value>Fliesen</value>
  </data>
  <data name="Please choose a payment method!" xml:space="preserve">
    <value>Bitte wählen Sie eine Zahlungsmethode aus!</value>
  </data>
  <data name="Please pay for the selected order" xml:space="preserve">
    <value>Bezahlen Sie jetzt den ausgewählten Auftrag.</value>
  </data>
  <data name="PleaseAcceptTerms" xml:space="preserve">
    <value>Bitte akzeptiere die Allgemeinen Geschäftsbedingungen.</value>
  </data>
  <data name="Plumbing" xml:space="preserve">
    <value>Klempnerarbeiten</value>
  </data>
  <data name="PName" xml:space="preserve">
    <value>Ansprechpartner</value>
  </data>
  <data name="Portuguese" xml:space="preserve">
    <value>Portugiesisch</value>
  </data>
  <data name="PostBox" xml:space="preserve">
    <value>PLZ</value>
  </data>
  <data name="Preisfuer" xml:space="preserve">
    <value>Preis für</value>
  </data>
  <data name="Press key to show the list of desired activity" xml:space="preserve">
    <value>Bitte drücken Sie die Taste, um die Liste der gewünschten Aktivität zu sehen.</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Preis</value>
  </data>
  <data name="Price of the cleaning request" xml:space="preserve">
    <value>Preis der Reinigungsanfrage</value>
  </data>
  <data name="Price of the combined request" xml:space="preserve">
    <value>Preis der Kombianfrage</value>
  </data>
  <data name="Price of the moving request" xml:space="preserve">
    <value>Preis der Umzugsanfrage</value>
  </data>
  <data name="Price of the painting request" xml:space="preserve">
    <value>Preis der Maleranfrage</value>
  </data>
  <data name="Price of the plastering request" xml:space="preserve">
    <value>Preis der Gipseranfrage</value>
  </data>
  <data name="Price/Quality" xml:space="preserve">
    <value>Preis / Qualität</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Drucken</value>
  </data>
  <data name="PrintReport" xml:space="preserve">
    <value>Bericht drucken</value>
  </data>
  <data name="Privacy Policy" xml:space="preserve">
    <value>Datenschutzerklärung</value>
  </data>
  <data name="Private Move" xml:space="preserve">
    <value>Privatumzug</value>
  </data>
  <data name="Process an order" xml:space="preserve">
    <value>TaskDotNet Plattform:  &lt;br /&gt; 
Die Lösung für produktives Arbeiten </value>
  </data>
  <data name="Purchased Activites" xml:space="preserve">
    <value>Gekaufte Aktivitäten</value>
  </data>
  <data name="Purchased orders" xml:space="preserve">
    <value>Gekaufte Bestellungen</value>
  </data>
  <data name="PurchaseDate" xml:space="preserve">
    <value>Kaufdatum</value>
  </data>
  <data name="Quality" xml:space="preserve">
    <value>Qualität</value>
  </data>
  <data name="RangeAttribute_ValidationError" xml:space="preserve">
    <value>Das Feld {0} muss zwischen {1} und {2} liegen.</value>
  </data>
  <data name="ReadTermsAndCondotions" xml:space="preserve">
    <value>Ich habe die Allgemeine Geschäftsbestimmungen (AGB) gelesen und stimme ihnen zu.</value>
  </data>
  <data name="Recharge credit" xml:space="preserve">
    <value>Guthaben aufladen</value>
  </data>
  <data name="RechargeCreditHead1" xml:space="preserve">
    <value>Hier haben Sie die Möglichkeit, Ihr Guthaben aufzuladen, sodass Sie bei jedem Kauf nicht erneut bezahlen müssen.</value>
  </data>
  <data name="RechargeCreditHead2" xml:space="preserve">
    <value>Ihre persönlichen Daten werden verwendet, um Ihre Zahlung zu verarbeiten, Ihre Nutzererfahrung auf dieser Website zu verbessern und Ihnen transparente Informationen über die Verwendung Ihrer Daten bereitzustellen.</value>
  </data>
  <data name="RechargeCreditHead3" xml:space="preserve">
    <value>Bitte lesen Sie hierzu unsere</value>
  </data>
  <data name="RechargeFailedMessage" xml:space="preserve">
    <value>Aufladen fehlgeschlagen. Bitte versuchen Sie es später erneut.</value>
  </data>
  <data name="RechargeMessage" xml:space="preserve">
    <value>Ihrem Guthaben wurde erfolgreich hinzugefügt.   Vielen Dank!</value>
  </data>
  <data name="RechargeSuccessfullyMessage" xml:space="preserve">
    <value>Das Guthaben wurde erfolgreich aufgeladen.</value>
  </data>
  <data name="Recurring Cleaning" xml:space="preserve">
    <value>Wiederkehrende Reinigung</value>
  </data>
  <data name="RefrigerationTechnician" xml:space="preserve">
    <value>Kältetechnikerleistungen</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>Registrieren</value>
  </data>
  <data name="Register here" xml:space="preserve">
    <value>Registrieren</value>
  </data>
  <data name="Register now" xml:space="preserve">
    <value>Registrieren Sie sich jetzt</value>
  </data>
  <data name="RegisterHeader1" xml:space="preserve">
    <value>Die Plattform für </value>
  </data>
  <data name="RegisterHeader2" xml:space="preserve">
    <value>maximale Produktivität.</value>
  </data>
  <data name="RegisterHeader3" xml:space="preserve">
    <value>Effizient vernetzt. </value>
  </data>
  <data name="RegisterHeader4" xml:space="preserve">
    <value>Effektiv umgesetzt.</value>
  </data>
  <data name="RegisterHeader5" xml:space="preserve">
    <value>Bitte Zugangsdaten eingeben, um sich bei TaskDotNet.com zu registrieren.</value>
  </data>
  <data name="Regular Cleaning" xml:space="preserve">
    <value>Regelmässige Reinigung</value>
  </data>
  <data name="Remember Me" xml:space="preserve">
    <value>Angemeldet bleiben</value>
  </data>
  <data name="Request List" xml:space="preserve">
    <value>Anfragenliste</value>
  </data>
  <data name="RequiredAttribute_ValidationError" xml:space="preserve">
    <value>Das Feld {0} ist erforderlich.</value>
  </data>
  <data name="ResetDashboard" xml:space="preserve">
    <value>Dashboard zurücksetzen</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Passwort zurücksetzen</value>
  </data>
  <data name="ResetPasswordMessage" xml:space="preserve">
    <value>Der Link zum Zurücksetzen des Passworts wurde gesendet. Bitte überprüfen Sie Ihren E-Mail-Posteingang.</value>
  </data>
  <data name="ResetTitle" xml:space="preserve">
    <value>Formular zur Datenbereinigung</value>
  </data>
  <data name="Role" xml:space="preserve">
    <value>Berechtigung</value>
  </data>
  <data name="Roofer" xml:space="preserve">
    <value>Dachdeckerarbeiten</value>
  </data>
  <data name="Room" xml:space="preserve">
    <value>Zimmer</value>
  </data>
  <data name="RoomList" xml:space="preserve">
    <value>Liste der Haupträume</value>
  </data>
  <data name="Rooms" xml:space="preserve">
    <value>Zimmer</value>
  </data>
  <data name="Russian" xml:space="preserve">
    <value>Russisch</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="Save changes" xml:space="preserve">
    <value>Änderungen speichern</value>
  </data>
  <data name="Select an activity" xml:space="preserve">
    <value>Wählen Sie eine Aktivität aus</value>
  </data>
  <data name="Select Branch" xml:space="preserve">
    <value>Branche auswählen</value>
  </data>
  <data name="Select Template" xml:space="preserve">
    <value>Wählen Sie Vorlage aus</value>
  </data>
  <data name="Select the Activity you want" xml:space="preserve">
    <value>Wählen Sie die gewünschten Aktivitäten aus</value>
  </data>
  <data name="Select the cantons in which you would like to work here" xml:space="preserve">
    <value>Wählen Sie hier die Kantone aus, in denen Sie arbeiten möchten.</value>
  </data>
  <data name="Send" xml:space="preserve">
    <value>Senden</value>
  </data>
  <data name="SendEmail" xml:space="preserve">
    <value>E-Mail senden</value>
  </data>
  <data name="SendEmailTo" xml:space="preserve">
    <value>Senden Sie eine E-Mail an den Partner</value>
  </data>
  <data name="SendMessageToTheCustomer" xml:space="preserve">
    <value>Nachricht an den Kunden senden</value>
  </data>
  <data name="SendResetLink" xml:space="preserve">
    <value>Link zum Zurücksetzen senden</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Einstellungen</value>
  </data>
  <data name="SevenFloor" xml:space="preserve">
    <value>7. Etage</value>
  </data>
  <data name="Show all Activity" xml:space="preserve">
    <value>Alle Aktivitäten anzeigen</value>
  </data>
  <data name="Show all Archive data" xml:space="preserve">
    <value>Show all Archive data</value>
  </data>
  <data name="Show all Inventory" xml:space="preserve">
    <value>Räume bearbeiten</value>
  </data>
  <data name="Show all Statistics" xml:space="preserve">
    <value>Alle Statistiken anzeigen</value>
  </data>
  <data name="Sightseeing" xml:space="preserve">
    <value>Besichtigung</value>
  </data>
  <data name="SixFloor" xml:space="preserve">
    <value>6. Etage</value>
  </data>
  <data name="SmallTransport" xml:space="preserve">
    <value>Kleiner Transport</value>
  </data>
  <data name="SoilType" xml:space="preserve">
    <value>Bodentyp</value>
  </data>
  <data name="Sold" xml:space="preserve">
    <value>Verkauft</value>
  </data>
  <data name="SomeThingWentWrong" xml:space="preserve">
    <value>Etwas ist schiefgelaufen. Bitte versuchen Sie es später erneut.</value>
  </data>
  <data name="Space" xml:space="preserve">
    <value>Raum</value>
  </data>
  <data name="Spanish" xml:space="preserve">
    <value>Spanisch</value>
  </data>
  <data name="Start Date" xml:space="preserve">
    <value>Startdatum</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Startdatum</value>
  </data>
  <data name="Statistics" xml:space="preserve">
    <value>Statistiken</value>
  </data>
  <data name="StatisticsPageTitle" xml:space="preserve">
    <value>Diese Tabelle zeigt die Verkaufsbewegungen für alle angekündigten Produkte und Aktivitäten im laufenden Jahr.</value>
  </data>
  <data name="StatisticsPageTitlePartner" xml:space="preserve">
    <value>Diese Tabelle zeigt die Kaufbewegungen für alle im aktuellen Jahr erworbenen Produkte (Aktivitäten).</value>
  </data>
  <data name="StatisticsPageTitlePartner2" xml:space="preserve">
    <value>Die Käufe des Partners für das aktuelle Jahr:</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Storage" xml:space="preserve">
    <value>Lagerung</value>
  </data>
  <data name="Street" xml:space="preserve">
    <value>Strasse</value>
  </data>
  <data name="StringLengthAttribute_ValidationError" xml:space="preserve">
    <value>Das Feld {0} muss eine Zeichenfolge mit einer maximalen Länge von {1} sein.</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Subjekt</value>
  </data>
  <data name="System setup" xml:space="preserve">
    <value>Systemeinstellungen</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Ihr TaskDotNet Team</value>
  </data>
  <data name="TenFloor" xml:space="preserve">
    <value>10. Etage</value>
  </data>
  <data name="TermsNotAccepted" xml:space="preserve">
    <value>AGB nicht bestätigt</value>
  </data>
  <data name="Thank you for your payment..." xml:space="preserve">
    <value>Herzlichen Dank für Ihre Zahlung.</value>
  </data>
  <data name="The achievements of partnere for the current month" xml:space="preserve">
    <value>Erbrachte Leistungen der Partner im aktuellen Jahr.</value>
  </data>
  <data name="The total: CHF" xml:space="preserve">
    <value>Die Gesamtsumme: CHF</value>
  </data>
  <data name="PartnerDashboardMessage" xml:space="preserve">
    <value>Dieses Projekt dient der Zusammenarbeit mit "TaskDotNet.com" und hilft Partnern, Bestellungen zu verfolgen, Statistiken zu analysieren und fundierte Entscheidungen auf Basis aktueller Daten zu treffen.</value>
  </data>
  <data name="This request will be deleted after archiving!" xml:space="preserve">
    <value>Diese Anfrage wird nach der Archivierung gelöscht!</value>
  </data>
  <data name="This table displays the work activity statistics for the current year" xml:space="preserve">
    <value>In dieser Tabelle werden die Arbeitsaktivitätsstatistiken für das laufende Jahr angezeigt.</value>
  </data>
  <data name="ThisIsRequiredRequest" xml:space="preserve">
    <value>Dies ist die konkrete Anfrage.</value>
  </data>
  <data name="ThreeDays" xml:space="preserve">
    <value>3 Tage</value>
  </data>
  <data name="ThreeFloor" xml:space="preserve">
    <value>3. Etage</value>
  </data>
  <data name="ThreeWeeks" xml:space="preserve">
    <value>3 Wochen</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Titel</value>
  </data>
  <data name="To" xml:space="preserve">
    <value>Nach</value>
  </data>
  <data name="To which branch" xml:space="preserve">
    <value>Branche auswählen</value>
  </data>
  <data name="Top Up Balance" xml:space="preserve">
    <value>Guthaben aufladen</value>
  </data>
  <data name="Total volume" xml:space="preserve">
    <value>Gesamtvolumen</value>
  </data>
  <data name="toTheDrainEdge" xml:space="preserve">
    <value>zum Abflussbereich</value>
  </data>
  <data name="toTheLoadingEdge" xml:space="preserve">
    <value>zum Ladebereich</value>
  </data>
  <data name="Turkish" xml:space="preserve">
    <value>Türkisch</value>
  </data>
  <data name="TwoDays" xml:space="preserve">
    <value>2 Tage</value>
  </data>
  <data name="TwoFloor" xml:space="preserve">
    <value>2. Etage</value>
  </data>
  <data name="TwoWeeks" xml:space="preserve">
    <value>2 Wochen</value>
  </data>
  <data name="UID" xml:space="preserve">
    <value>UID-Nr.</value>
  </data>
  <data name="UnauthorizedMessage" xml:space="preserve">
    <value>Leider haben Sie keine Zugriffsberechtigung für diese Seite. Bitte gehen Sie zur Startseite zurück.</value>
  </data>
  <data name="UnauthorizedTitle" xml:space="preserve">
    <value>Du bist nicht autorisiert! 🔐</value>
  </data>
  <data name="Unit" xml:space="preserve">
    <value>Einheit</value>
  </data>
  <data name="Unpacking" xml:space="preserve">
    <value>Auspacken</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Aktualisieren</value>
  </data>
  <data name="Upload File" xml:space="preserve">
    <value>Datei hochladen</value>
  </data>
  <data name="Useless" xml:space="preserve">
    <value>Nutzlos</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Benutzer</value>
  </data>
  <data name="UsersList" xml:space="preserve">
    <value>Benutzerliste</value>
  </data>
  <data name="VerifyAccount" xml:space="preserve">
    <value>Überprüfen Sie bitte Ihr Konto.</value>
  </data>
  <data name="View All" xml:space="preserve">
    <value>Alle anzeigen</value>
  </data>
  <data name="View order" xml:space="preserve">
    <value>Vorschau der Anfrage</value>
  </data>
  <data name="Viewing" xml:space="preserve">
    <value>Besichtigung</value>
  </data>
  <data name="ViewingDate" xml:space="preserve">
    <value>Besichtigungsdatum</value>
  </data>
  <data name="Volume (m3)" xml:space="preserve">
    <value>Volumen (m3)</value>
  </data>
  <data name="Volume in m3" xml:space="preserve">
    <value>Volumen in m3</value>
  </data>
  <data name="Walls" xml:space="preserve">
    <value>Wände</value>
  </data>
  <data name="WallsAndCeilings" xml:space="preserve">
    <value>Wände und Decken</value>
  </data>
  <data name="Washroom" xml:space="preserve">
    <value>Waschraum</value>
  </data>
  <data name="Weak" xml:space="preserve">
    <value>Schwach</value>
  </data>
  <data name="Website" xml:space="preserve">
    <value>Webseite</value>
  </data>
  <data name="WelcomeBack" xml:space="preserve">
    <value>Herzlich willkommen, Partner!</value>
  </data>
  <data name="Welder" xml:space="preserve">
    <value>Schweissarbeiten</value>
  </data>
  <data name="Window Cleaning" xml:space="preserve">
    <value>Fensterreinigung</value>
  </data>
  <data name="Windows" xml:space="preserve">
    <value>Fenster</value>
  </data>
  <data name="Windows Cleaning" xml:space="preserve">
    <value>Fenster Putzen</value>
  </data>
  <data name="WithInventoryList" xml:space="preserve">
    <value>mit Inventarliste</value>
  </data>
  <data name="Workers" xml:space="preserve">
    <value>Arbeiter</value>
  </data>
  <data name="Workspace" xml:space="preserve">
    <value>Bereich</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Ja</value>
  </data>
  <data name="YesOn" xml:space="preserve">
    <value>Ja, am:</value>
  </data>
  <data name="YouWantToPurchaseThisOrder" xml:space="preserve">
    <value>Hinweis: Beim Kauf dieses Auftrags werden {0} von Ihrem Guthaben abgezogen.</value>
  </data>
  <data name="AdminDashboardHeader" xml:space="preserve">
    <value>Umfassende Kontrolle auf der TaskDotNet-Plattform</value>
  </data>
  <data name="AdminDashboardHeader2" xml:space="preserve">
    <value>Dies ist eine Statistikseite, die die Interaktionen  
und Aktivitäten aller Partner mit der Plattform  
veranschaulicht.</value>
  </data>
  <data name="CreateUser" xml:space="preserve">
    <value>Benutzer erstellen</value>
  </data>
  <data name="Handyman" xml:space="preserve">
    <value>Handwerker</value>
  </data>
  <data name="RequestControl" xml:space="preserve">
    <value>Anfragenkontrolle</value>
  </data>
  <data name="UpdateUser" xml:space="preserve">
    <value>Benutzer aktualisieren</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Zurück</value>
  </data>
  <data name="Credits" xml:space="preserve">
    <value>Gutschriften</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="NoCreditsFound" xml:space="preserve">
    <value>Keine Guthaben für den ausgewählten Zeitraum gefunden.</value>
  </data>
  <data name="Partnername" xml:space="preserve">
    <value>Partnername</value>
  </data>
  <data name="PaymentWay" xml:space="preserve">
    <value>Zahlungsart</value>
  </data>
  <data name="Disclaimer" xml:space="preserve">
    <value>Haftungsausschluss</value>
  </data>
  <data name="LegalNotice" xml:space="preserve">
    <value>Impressum</value>
  </data>
  <data name="PrivacyPolicy" xml:space="preserve">
    <value>Datenschutz</value>
  </data>
  <data name="TermsAndConditions" xml:space="preserve">
    <value>AGB</value>
  </data>
  <data name="RequestDataNote" xml:space="preserve">
    <value>Symbole zeigen verfügbare persönliche Kundendaten in der Anfrage an.</value>
  </data>
</root>