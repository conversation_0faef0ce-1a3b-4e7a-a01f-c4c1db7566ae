﻿@using Admin.TaskDotNet.Dtos
@using Comman.Helper.Extensions
@model PaginatedListResponse<Movement>
@{
    ViewData["Title"] = "Purchased Activites";
}

@section Links {
    <style>
        /* Custom animations only */
        @@keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }

        @@keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @@keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @@keyframes shimmer {
            0% {
                background-position: -200px 0;
            }
            100% {
                background-position: calc(200px + 100%) 0;
            }
        }

        @@keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        @@keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Minimal custom classes using Bootstrap variables */
        .animate-fade-up {
            animation: fadeInUp 0.6s ease;
        }

        .animate-slide-left {
            animation: slideInLeft 0.6s ease;
        }

        .animate-pulse:hover {
            animation: pulse 0.6s ease;
        }

        .card-hover {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(var(--bs-primary-rgb), 0.3);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200px 100%;
            animation: shimmer 1.5s infinite;
        }

        .bx-spin {
            animation: spin 1s linear infinite;
        }

        /* Staggered animation delays */
        .stagger-1 { animation-delay: 0.1s; }
        .stagger-2 { animation-delay: 0.2s; }
        .stagger-3 { animation-delay: 0.3s; }
        .stagger-4 { animation-delay: 0.4s; }
        .stagger-5 { animation-delay: 0.5s; }
        .stagger-6 { animation-delay: 0.6s; }
        .stagger-7 { animation-delay: 0.7s; }
        .stagger-8 { animation-delay: 0.8s; }

        table td {
            padding: 0 !important;
            color: #000 !important;
        }
    </style>
}

<!-- Content wrapper -->
<div class="content-wrapper">
    <!-- Content -->
    <div class="container-fluid flex-grow-1">
        <div class="bg-primary bg-gradient rounded-3 p-4 mb-4 animate-fade-up">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h3 class="fw-bold text-white mb-0">
                        <i class="bx bx-check-circle me-2"></i>@SharedLocalizer["Purchased orders"] (@DateTime.Today.ToString("MM.yyyy"))
                    </h3>
                </div>
                <div class="col-md-6 text-end">
                    <h4 class="fw-bold text-white mb-0">
                        @SharedLocalizer["Current Balance"]:
                        <span class="badge bg-light text-primary fs-5 ms-2">CHF @ViewBag.Salodo</span>
                    </h4>
                </div>
            </div>
        </div>
        <!-- Enhanced Main Card -->
        <div class="card shadow-sm mb-4 animate-slide-left">
            <div class="card-body bg-light">
                <div class="row d-flex justify-content-between align-items-center">
                    <div class="col-lg-6">
                        <div class="form-group row align-items-center">
                            <label for="categoryDropdown" class="col-md-5 col-form-label fs-5 fw-semibold text-primary">
                                <i class="bx bx-filter-alt me-2"></i>@SharedLocalizer["Select an activity"]:
                            </label>
                            <div class="col-md-7">
                                <select class="form-select shadow-sm" asp-items="ViewBag.PartnerActivities" id="categoryDropdown">
                                    <option value="0">@SharedLocalizer["View All"]</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <nav aria-label="Pagination Navigation">
                            <ul class="pagination justify-content-end" id="pagination-list">
                                @{
                                    int currentPage = Model.PageNumber;
                                    int totalPages = Model.TotalPages;
                                    int pageRange = 3; // Number of pages to show before and after the current page
                                    int startPage = Math.Max(1, currentPage - pageRange);
                                    int endPage = Math.Min(totalPages, currentPage + pageRange);

                                    // Always show the first page
                                    if (startPage > 1)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Index", "PurchasedActivites", new { PageNumber = 1, SearchTerm = Model.SearchTerm })">1</a>
                                        </li>
                                        if (startPage > 2)
                                        {
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        }
                                    }

                                    // Show the range of pages around the current page
                                    for (int i = startPage; i <= endPage; i++)
                                    {
                                        <li class="page-item @(i == currentPage ? "active" : string.Empty)">
                                            <a class="page-link" href="@Url.Action("Index", "PurchasedActivites", new { PageNumber = i, SearchTerm = Model.SearchTerm })">@i</a>
                                        </li>
                                    }

                                    // Always show the last page
                                    if (endPage < totalPages)
                                    {
                                        if (endPage < totalPages - 1)
                                        {
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        }
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Index", "PurchasedActivites", new { PageNumber = totalPages, SearchTerm = Model.SearchTerm })">@totalPages</a>
                                        </li>
                                    }
                                }
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <div class="p-4">
                @if (!Model.Data.Any())
                {
                    <div class="text-center py-5 animate-fade-up">
                        <div class="row justify-content-center">
                            <div class="col-lg-6 col-md-8 col-sm-10">
                                <img src="~/dashboard/assets/img/Gekauft.png"
                                     alt="No Purchased Orders Found"
                                     class="img-fluid mb-4 animate-pulse"
                                     style="width: 160px; height: auto;" />
                                <h3 class="text-muted fw-light mb-3">
                                    <i class="bx bx-shopping-bag me-2"></i>@SharedLocalizer["EmptyPurchasedOrders"]
                                </h3>
                                
                            </div>
                        </div>
                    </div>
                }
                <!-- Enhanced Card Grid -->
                <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 row-cols-xl-4 g-4">
                    @{int cardIndex = 0;}
                    @foreach (var movement in Model.Data)
                    {
                        var item = movement.Activity;
                        cardIndex++;
                        <div class="col">
                            <div class="card h-100 shadow-sm card-hover animate-fade-up stagger-@(cardIndex % 8 + 1)">
                                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                    <div class="fw-bold fs-6">
                                        <i class="bx bx-check-circle me-2"></i>@item.ActivityType.GetDisplayName()
                                    </div>
                                    <div class="fw-bold fs-6">
                                        <i class="bx bx-calendar me-2"></i>
                                        @if (item.ActivityType == ActivityType.Cleaning)
                                        {
                                            @item.CleaningDate.ToString("dd.MM.yyyy")
                                        }
                                        else
                                        {
                                            @item.MovingDate.ToString("dd.MM.yyyy")
                                        }
                                    </div>
                                </div>
                                <div class="card-body p-4 bg-light bg-gradient">
                                    <!-- Activity Details -->
                                    @if (item.ActivityType == ActivityType.Moving || item.ActivityType == ActivityType.MovingAndCleaning)
                                    {
                                        <table class="table table-borderless">
                                            <tbody>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["From"]:</strong></td>
                                                    <td>@(item.PostBox) @(item.City)</td>
                                                </tr>


                                                <tr>
                                                    <td><strong>@SharedLocalizer["To"]:</strong></td>
                                                    <td>@(item.TPostBox) @(item.TCity)</td>
                                                </tr>

                                                <tr style="height: 20px;"></tr>

                                                <tr>
                                                    <td style=" vertical-align: top; "><strong>@SharedLocalizer["Auszug"]:</strong></td>
                                                    <td>
                                                        @(item.Room) @SharedLocalizer["Room"]-@(SharedLocalizer[item.Object])<br />
                                                        @(SharedLocalizer[item.Floor]), @SharedLocalizer["Lift"]: @(item.FLift ?? false ? @SharedLocalizer["Yes"] : @SharedLocalizer["No"])
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Einzug"]:</strong></td>
                                                    <td>
                                                        @(SharedLocalizer[item.TFloor]), @SharedLocalizer["Lift"]: @(item.TLift ?? false ? @SharedLocalizer["Yes"] : @SharedLocalizer["No"])
                                                    </td>
                                                </tr>
                                                <tr style="height: 10px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Flexibility"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.Flexible])</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        @if (item.ActivityType == ActivityType.MovingAndCleaning)
                                        {
                                            <hr class="m-2" />
                                            <table class="table table-borderless">
                                                <tbody>
                                                    <tr>
                                                        <td><strong>@SharedLocalizer["CleaningDate"]:</strong></td>
                                                        <td>@item.CleaningDate.ToString("dd.MM.yyyy")</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>@SharedLocalizer["HandOverDate"]:</strong></td>
                                                        <td>@item.HandOverDate.ToString("dd.MM.yyyy")</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        }

                                    }
                                    else if (item.ActivityType == ActivityType.Cleaning)
                                    {

                                        <table class="table table-borderless">
                                            <tbody>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Type"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.CleaningType])</td>
                                                </tr>
                                                <tr style="height: 10px;"></tr>

                                                <tr>
                                                    <td style=" vertical-align: top; "><strong>@SharedLocalizer["Ort"]:</strong></td>
                                                    <td>
                                                        @(item.PostBox) @(item.City) <br />
                                                        @(item.Room) @SharedLocalizer["Room"]-@(SharedLocalizer[item.Object])
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Area"]:</strong></td>
                                                    <td>
                                                        @(item.Area), @(SharedLocalizer[item.Floor])
                                                    </td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Flexibility"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.Flexible])</td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["HandOverDate"]:</strong></td>
                                                    <td>@item.HandOverDate.ToString("dd.MM.yyyy")</td>
                                                </tr>
                                            </tbody>
                                        </table>

                                    }
                                    else if (item.ActivityType == ActivityType.PaintingAndGisper)
                                    {
                                        <table class="table table-borderless">
                                            <tbody>

                                                <tr>
                                                    <td style=" vertical-align: top; "><strong>@SharedLocalizer["Ort"]:</strong></td>
                                                    <td>
                                                        @(item.PostBox) @(item.City) <br />
                                                        @(item.Room) @SharedLocalizer["Room"]-@(SharedLocalizer[item.Object]) <br />
                                                        @(SharedLocalizer[item.Floor])
                                                    </td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Area"]:</strong></td>
                                                    <td>
                                                        @(item.Area)
                                                    </td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Workspace"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.Workspace])</td>
                                                </tr>

                                                <tr style="height: 15px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Heater"]:</strong></td>
                                                    <td>@(item.Heater)</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Walls"]:</strong></td>
                                                    <td>@(item.Walls)</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Doors"]:</strong></td>
                                                    <td>@(item.Doors)</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Windows"]:</strong></td>
                                                    <td>@(item.Windows)</td>
                                                </tr>
                                            </tbody>
                                        </table>

                                    }
                                    else
                                    {
                                        <table class="table table-borderless">
                                            <tbody>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Ort"]:</strong></td>
                                                    <td>@(item.PostBox) @(item.City)</td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Object"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.Object])</td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["MoreWork"]:</strong></td>
                                                    <td>@(item.MoreWork)</td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Focus"]:</strong></td>
                                                    <td>
                                                        @(SharedLocalizer[item.Focus])
                                                    </td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Flexibility"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.Flexible])</td>
                                                </tr>
                                                <tr style="height: 15px;"></tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Notes"]:</strong></td>
                                                </tr>
                                                <tr>
                                                    <td class="text-wrap" colspan="2">@(item.Notes)</td>
                                                </tr>
                                            </tbody>
                                        </table>

                                    }
                                </div>
                                <div class="card-footer bg-transparent p-0">
                                    @if (item.ActivityType == ActivityType.Moving || item.ActivityType == ActivityType.MovingAndCleaning)
                                    {
                                        <div class="form-check form-switch m-0 m-3 d-flex align-items-center justify-content-end">
                                            <label class="form-check-label">@SharedLocalizer["HasInventory"]? </label>
                                            <input class="form-check-input m-0 ms-3" type="checkbox" onclick="return false;" @(item.Inventar ?? false ? "checked" : "")
                                                   style="height:25px; width:60px;" />
                                        </div>
                                    }

                                    <div class="bg-primary text-white text-center py-3 fs-5 fw-bold">
                                        <i class="bx bx-money me-2"></i>CHF @(item.Preis.ToString("0.00"))
                                    </div>

                                    <div class="d-flex justify-content-center align-items-center p-3">
                                        <a class="btn btn-outline-primary shadow-sm" asp-controller="PurchasedActivites" asp-action="ActivityDetails" asp-route-id="@movement.Activity.Id">
                                            <i class="bx bx-show me-2"></i>@SharedLocalizer["View order"]
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
        <!--/ Enhanced Purchased Activities Grid -->
    </div>


</div>



@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


    <script>

        $(document).ready(function () {
            const searchTerm = "@Model.SearchTerm";

            if (searchTerm) {
                $("#categoryDropdown").val(decodeURIComponent(searchTerm));
            }

            $("#categoryDropdown").on("change", function () {
                const selectedValue = $(this).val();

                // Add loading effect
                $(this).addClass('loading-shimmer');

                // Construct the URL with proper encoding for the query parameter
                const redirectUrl = `/PurchasedActivites/Index?SearchTerm=${encodeURIComponent(selectedValue)}`;
                window.location.href = redirectUrl;
            });

            // Add ripple effect to cards
            $('.card-hover').on('click', function(e) {
                const ripple = $('<div class="ripple"></div>');
                $(this).append(ripple);

                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                ripple.css({
                    width: size + 'px',
                    height: size + 'px',
                    left: (e.clientX - rect.left - size / 2) + 'px',
                    top: (e.clientY - rect.top - size / 2) + 'px'
                });

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    </script>
}
