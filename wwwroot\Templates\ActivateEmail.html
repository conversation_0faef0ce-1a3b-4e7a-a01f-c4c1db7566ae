﻿



<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaskDotNet-ActivateEmail </title>

</head>
<body>
    <div class="container" style="max-width: 600px;margin: 0 auto;/* padding: 20px; */font-family: Arial, sans-serif;box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);">
        <div class="header" style="text-align: center; ">
            <img src="img/logo.png" style="max-width: 200px;" alt="Your Company Logo">
        </div>
        <div class="content" style="padding: 20px;background-color: #ffffff;border-radius: 5px;/* box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); */background-color: aliceblue;margin-bottom: 20px;">


            <h2 style=" color: #333333;margin-bottom: 10px;">Guten Tag  {0},</h2>
            <p style="color: #666666;font-weight: 600;font-size: 20px;">
                vielen Dank für die Registrierung bei TaskDotNet.
                Es ist grossartig, dass Sie dabei sind.
            </p>
            <p style="color: #666666;font-weight: 600;font-size: 20px;">Zuerst müssen wir jedoch Ihre E-Mail-Adresse richtig aktivieren.</p>

            <div class="button" style="text-align: center;justify-content: center; margin: 50px 0;">
                <a href="{1}" style="padding: 20px 30px; background-color: #145960; font-size: 20px; font-weight: 800; text-decoration: none; color:#ffffff">Email aktivieren </a>
            </div>
            <p style=" color: #666666;font-weight: 600;font-size: 20px;margin-bottom: 20px;">Vielen Dank und freundliche Grüsse,<br>TaskDotNet Team</p>
        </div>

    </div>
</body>
</html>
