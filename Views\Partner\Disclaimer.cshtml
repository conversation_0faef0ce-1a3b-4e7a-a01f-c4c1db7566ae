@{
    ViewData["Title"] = "Disclaimer";
}
@using TaskDotNet.Localization
@using Microsoft.Extensions.Localization
@using TaskDotNet.Localization.Disclaimer
@inject IStringLocalizer<Disclaimer> SharedLocalizer

<style>
    p,li{
        font-size: 18px;
    }
</style>
<div class="container my-5">
    <div class="card shadow-sm border-0 rounded-4">
        <div class="card-body p-5">
            <h3 class="text-center text-primary mb-4">@SharedLocalizer["DisclaimerTitle"]</h3>

            <section>
                <h4 class="text-center text-primary mb-3 border-bottom pb-2">@SharedLocalizer["OnlineOffersTitle"]</h4>
                <p>
                    @SharedLocalizer["OnlineOffersContent"]
                </p>
            </section>

            <section>
                <h4 class="text-center text-primary mb-3 border-bottom pb-2">@SharedLocalizer["ExternalLinksTitle"]</h4>
                <p>
                    @SharedLocalizer["ExternalLinksContent"]
                </p>
            </section>

            <section>
                <h4 class="text-center text-primary mb-3 border-bottom pb-2">@SharedLocalizer["ValidityJurisdictionTitle"]</h4>
                <p>
                    @SharedLocalizer["ValidityJurisdictionContent"]
                </p>
                <p>
                    @SharedLocalizer["CourtJurisdiction"]
                </p>
            </section>
        </div>
    </div>
</div>
