﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using TaskDotNet.Models;

public class CompanyNameViewComponent : ViewComponent
{
    private readonly UserManager<Partner> _userManager;

    public CompanyNameViewComponent(UserManager<Partner> userManager)
    {
        _userManager = userManager;
    }

    public async Task<IViewComponentResult> InvokeAsync()
    {

        var user = await _userManager.GetUserAsync(HttpContext.User);
        string CompanyName = user?.CompanyName ?? "";

        return View("Default", CompanyName);
    }

}
