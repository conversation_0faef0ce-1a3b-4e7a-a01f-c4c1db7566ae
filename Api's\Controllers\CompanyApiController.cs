﻿using TaskDotNet.Comman.DataAccess;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace TaskDotNet.Api_s.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CompanyApiController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public CompanyApiController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet("GetCompanyData")]
        public async Task<IActionResult> GetCompanyData()
        {
            var data = await _context.Company.FirstOrDefaultAsync(m => true);
            if (data != null)
            {
                return Ok(data);
            }
            return Problem("No Company Data");
        }
    }
}
