{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "Dashboard/assets/css/demo.9ok5r7wnkv.css", "AssetFile": "Dashboard/assets/css/demo.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2514"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"6S6cMlFb0Z4msPVyV91LxWzhXanxx4qXv0iJ7C2HECY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9ok5r7wnkv"}, {"Name": "integrity", "Value": "sha256-6S6cMlFb0Z4msPVyV91LxWzhXanxx4qXv0iJ7C2HECY="}, {"Name": "label", "Value": "Dashboard/assets/css/demo.css"}]}, {"Route": "Dashboard/assets/css/demo.css", "AssetFile": "Dashboard/assets/css/demo.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2514"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"6S6cMlFb0Z4msPVyV91LxWzhXanxx4qXv0iJ7C2HECY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6S6cMlFb0Z4msPVyV91LxWzhXanxx4qXv0iJ7C2HECY="}]}, {"Route": "Dashboard/assets/img/AGB.png", "AssetFile": "Dashboard/assets/img/AGB.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "73761"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjshtF3LNYoC4XF6kphSo786E52LjxwO/hPMhHc4nJk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NjshtF3LNYoC4XF6kphSo786E52LjxwO/hPMhHc4nJk="}]}, {"Route": "Dashboard/assets/img/AGB.q7mriwkq8p.png", "AssetFile": "Dashboard/assets/img/AGB.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "73761"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjshtF3LNYoC4XF6kphSo786E52LjxwO/hPMhHc4nJk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q7mriwkq8p"}, {"Name": "integrity", "Value": "sha256-NjshtF3LNYoC4XF6kphSo786E52LjxwO/hPMhHc4nJk="}, {"Name": "label", "Value": "Dashboard/assets/img/AGB.png"}]}, {"Route": "Dashboard/assets/img/ActivateEmail.png", "AssetFile": "Dashboard/assets/img/ActivateEmail.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "45815"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"lON53pF5QfXQPeynlyNLW20fO1Jm3CHH+rwkWshmpjY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lON53pF5QfXQPeynlyNLW20fO1Jm3CHH+rwkWshmpjY="}]}, {"Route": "Dashboard/assets/img/ActivateEmail.wf7zkmmsg2.png", "AssetFile": "Dashboard/assets/img/ActivateEmail.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "45815"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"lON53pF5QfXQPeynlyNLW20fO1Jm3CHH+rwkWshmpjY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wf7zkmmsg2"}, {"Name": "integrity", "Value": "sha256-lON53pF5QfXQPeynlyNLW20fO1Jm3CHH+rwkWshmpjY="}, {"Name": "label", "Value": "Dashboard/assets/img/ActivateEmail.png"}]}, {"Route": "Dashboard/assets/img/Attention-removebg-preview.0eoyn9ev74.png", "AssetFile": "Dashboard/assets/img/Attention-removebg-preview.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "17077"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"+Io5SSy0J/Yr5h8QNITI8ijnOPvZeBRSUn2Kpq2jKOg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0eoyn9ev74"}, {"Name": "integrity", "Value": "sha256-+Io5SSy0J/Yr5h8QNITI8ijnOPvZeBRSUn2Kpq2jKOg="}, {"Name": "label", "Value": "Dashboard/assets/img/Attention-removebg-preview.png"}]}, {"Route": "Dashboard/assets/img/Attention-removebg-preview.png", "AssetFile": "Dashboard/assets/img/Attention-removebg-preview.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17077"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"+Io5SSy0J/Yr5h8QNITI8ijnOPvZeBRSUn2Kpq2jKOg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+Io5SSy0J/Yr5h8QNITI8ijnOPvZeBRSUn2Kpq2jKOg="}]}, {"Route": "Dashboard/assets/img/Dashboard1.6735s5w30p.png", "AssetFile": "Dashboard/assets/img/Dashboard1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "152669"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"jtDYwDrcKCKFzplnq9YZGY013YoPvQrpWxEd1bQFdvQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6735s5w30p"}, {"Name": "integrity", "Value": "sha256-jtDYwDrcKCKFzplnq9YZGY013YoPvQrpWxEd1bQFdvQ="}, {"Name": "label", "Value": "Dashboard/assets/img/Dashboard1.png"}]}, {"Route": "Dashboard/assets/img/Dashboard1.png", "AssetFile": "Dashboard/assets/img/Dashboard1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "152669"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"jtDYwDrcKCKFzplnq9YZGY013YoPvQrpWxEd1bQFdvQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jtDYwDrcKCKFzplnq9YZGY013YoPvQrpWxEd1bQFdvQ="}]}, {"Route": "Dashboard/assets/img/Dashboard2.jpeg", "AssetFile": "Dashboard/assets/img/Dashboard2.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51207"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"UBr3r31nADQgJNCFkoK3xO3U8VLGejZTgd52aJ5qLDg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UBr3r31nADQgJNCFkoK3xO3U8VLGejZTgd52aJ5qLDg="}]}, {"Route": "Dashboard/assets/img/Dashboard2.oq5d0n04gc.jpeg", "AssetFile": "Dashboard/assets/img/Dashboard2.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51207"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"UBr3r31nADQgJNCFkoK3xO3U8VLGejZTgd52aJ5qLDg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "oq5d0n04gc"}, {"Name": "integrity", "Value": "sha256-UBr3r31nADQgJNCFkoK3xO3U8VLGejZTgd52aJ5qLDg="}, {"Name": "label", "Value": "Dashboard/assets/img/Dashboard2.jpeg"}]}, {"Route": "Dashboard/assets/img/Dashboard_01.png", "AssetFile": "Dashboard/assets/img/Dashboard_01.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5418"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nEBoPob46ocCh8FvwhNE9aCwR+ZIDds0r+WtOJJISvo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nEBoPob46ocCh8FvwhNE9aCwR+ZIDds0r+WtOJJISvo="}]}, {"Route": "Dashboard/assets/img/Dashboard_01.wf2p4xcoui.png", "AssetFile": "Dashboard/assets/img/Dashboard_01.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5418"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nEBoPob46ocCh8FvwhNE9aCwR+ZIDds0r+WtOJJISvo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wf2p4xcoui"}, {"Name": "integrity", "Value": "sha256-nEBoPob46ocCh8FvwhNE9aCwR+ZIDds0r+WtOJJISvo="}, {"Name": "label", "Value": "Dashboard/assets/img/Dashboard_01.png"}]}, {"Route": "Dashboard/assets/img/Dashboard_02.bgu0r6dzip.png", "AssetFile": "Dashboard/assets/img/Dashboard_02.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1989"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"K/2Qmr07EaNFTO7GqDYwLxbvOVcs/W4XSQClUHjeLs8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bgu0r6dzip"}, {"Name": "integrity", "Value": "sha256-K/2Qmr07EaNFTO7GqDYwLxbvOVcs/W4XSQClUHjeLs8="}, {"Name": "label", "Value": "Dashboard/assets/img/Dashboard_02.png"}]}, {"Route": "Dashboard/assets/img/Dashboard_02.png", "AssetFile": "Dashboard/assets/img/Dashboard_02.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1989"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"K/2Qmr07EaNFTO7GqDYwLxbvOVcs/W4XSQClUHjeLs8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K/2Qmr07EaNFTO7GqDYwLxbvOVcs/W4XSQClUHjeLs8="}]}, {"Route": "Dashboard/assets/img/Dashboard_03.dr26h7x9hl.png", "AssetFile": "Dashboard/assets/img/Dashboard_03.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3566"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"edBYXJ8iR9Z/u46Jwx1DjKInYvErDCdDqz4yBC5YhD8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dr26h7x9hl"}, {"Name": "integrity", "Value": "sha256-edBYXJ8iR9Z/u46Jwx1DjKInYvErDCdDqz4yBC5YhD8="}, {"Name": "label", "Value": "Dashboard/assets/img/Dashboard_03.png"}]}, {"Route": "Dashboard/assets/img/Dashboard_03.png", "AssetFile": "Dashboard/assets/img/Dashboard_03.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3566"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"edBYXJ8iR9Z/u46Jwx1DjKInYvErDCdDqz4yBC5YhD8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-edBYXJ8iR9Z/u46Jwx1DjKInYvErDCdDqz4yBC5YhD8="}]}, {"Route": "Dashboard/assets/img/Foto_Register_Form.aoece3ix9v.png", "AssetFile": "Dashboard/assets/img/Foto_Register_Form.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "90383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"thCGppWhwHuk0AC0E9YOmgxOjF7rmfmQEoiYQc8kvlw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aoece3ix9v"}, {"Name": "integrity", "Value": "sha256-thCGppWhwHuk0AC0E9YOmgxOjF7rmfmQEoiYQc8kvlw="}, {"Name": "label", "Value": "Dashboard/assets/img/Foto_Register_Form.png"}]}, {"Route": "Dashboard/assets/img/Foto_Register_Form.png", "AssetFile": "Dashboard/assets/img/Foto_Register_Form.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "90383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"thCGppWhwHuk0AC0E9YOmgxOjF7rmfmQEoiYQc8kvlw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-thCGppWhwHuk0AC0E9YOmgxOjF7rmfmQEoiYQc8kvlw="}]}, {"Route": "Dashboard/assets/img/Gekauft.png", "AssetFile": "Dashboard/assets/img/Gekauft.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8670"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"j0FMWEtWiYk/iRWvi7hpMBQJbQ8SSNiqPpgeECSHnBU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j0FMWEtWiYk/iRWvi7hpMBQJbQ8SSNiqPpgeECSHnBU="}]}, {"Route": "Dashboard/assets/img/Gekauft.rlsnhgmo07.png", "AssetFile": "Dashboard/assets/img/Gekauft.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8670"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"j0FMWEtWiYk/iRWvi7hpMBQJbQ8SSNiqPpgeECSHnBU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rlsnhgmo07"}, {"Name": "integrity", "Value": "sha256-j0FMWEtWiYk/iRWvi7hpMBQJbQ8SSNiqPpgeECSHnBU="}, {"Name": "label", "Value": "Dashboard/assets/img/Gekauft.png"}]}, {"Route": "Dashboard/assets/img/LOGO.ftm9a8wvm2.svg", "AssetFile": "Dashboard/assets/img/LOGO.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "103145"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"wwV2JH0uihEBS1I/dXr1qmZYqSEiym4XSJ3eIMuB0D0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ftm9a8wvm2"}, {"Name": "integrity", "Value": "sha256-wwV2JH0uihEBS1I/dXr1qmZYqSEiym4XSJ3eIMuB0D0="}, {"Name": "label", "Value": "Dashboard/assets/img/LOGO.svg"}]}, {"Route": "Dashboard/assets/img/LOGO.svg", "AssetFile": "Dashboard/assets/img/LOGO.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "103145"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"wwV2JH0uihEBS1I/dXr1qmZYqSEiym4XSJ3eIMuB0D0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wwV2JH0uihEBS1I/dXr1qmZYqSEiym4XSJ3eIMuB0D0="}]}, {"Route": "Dashboard/assets/img/Login.png", "AssetFile": "Dashboard/assets/img/Login.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1890749"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"ju26IPf7jQ3PdhCXWOLUCIbTb1S1FKjSXVW1c/fzW+s=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ju26IPf7jQ3PdhCXWOLUCIbTb1S1FKjSXVW1c/fzW+s="}]}, {"Route": "Dashboard/assets/img/Login.u5ennpjbay.png", "AssetFile": "Dashboard/assets/img/Login.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1890749"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"ju26IPf7jQ3PdhCXWOLUCIbTb1S1FKjSXVW1c/fzW+s=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u5ennpjbay"}, {"Name": "integrity", "Value": "sha256-ju26IPf7jQ3PdhCXWOLUCIbTb1S1FKjSXVW1c/fzW+s="}, {"Name": "label", "Value": "Dashboard/assets/img/Login.png"}]}, {"Route": "Dashboard/assets/img/Login2.png", "AssetFile": "Dashboard/assets/img/Login2.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "390213"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"JvebX3YgM9Gjc8rDsAN5eKzg3axmlNKrciSW3clvx4E=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JvebX3YgM9Gjc8rDsAN5eKzg3axmlNKrciSW3clvx4E="}]}, {"Route": "Dashboard/assets/img/Login2.uvazvaoys7.png", "AssetFile": "Dashboard/assets/img/Login2.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "390213"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"JvebX3YgM9Gjc8rDsAN5eKzg3axmlNKrciSW3clvx4E=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uvazvaoys7"}, {"Name": "integrity", "Value": "sha256-JvebX3YgM9Gjc8rDsAN5eKzg3axmlNKrciSW3clvx4E="}, {"Name": "label", "Value": "Dashboard/assets/img/Login2.png"}]}, {"Route": "Dashboard/assets/img/Nathing.6upc1whwmo.png", "AssetFile": "Dashboard/assets/img/Nathing.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9265"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"MsGauKaRuqa583D3Aab27pcdmqqMzdch9Kv3hurUbVU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6upc1whwmo"}, {"Name": "integrity", "Value": "sha256-MsGauKaRuqa583D3Aab27pcdmqqMzdch9Kv3hurUbVU="}, {"Name": "label", "Value": "Dashboard/assets/img/Nathing.png"}]}, {"Route": "Dashboard/assets/img/Nathing.png", "AssetFile": "Dashboard/assets/img/Nathing.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9265"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"MsGauKaRuqa583D3Aab27pcdmqqMzdch9Kv3hurUbVU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MsGauKaRuqa583D3Aab27pcdmqqMzdch9Kv3hurUbVU="}]}, {"Route": "Dashboard/assets/img/Partner.oiiwkv27sk.png", "AssetFile": "Dashboard/assets/img/Partner.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "52265"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"QIqgz+mHXZXoeh4zXnsGSo9coUm+5Kit5w3vbSpA544=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "oiiwkv27sk"}, {"Name": "integrity", "Value": "sha256-QIqgz+mHXZXoeh4zXnsGSo9coUm+5Kit5w3vbSpA544="}, {"Name": "label", "Value": "Dashboard/assets/img/Partner.png"}]}, {"Route": "Dashboard/assets/img/Partner.png", "AssetFile": "Dashboard/assets/img/Partner.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "52265"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"QIqgz+mHXZXoeh4zXnsGSo9coUm+5Kit5w3vbSpA544=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QIqgz+mHXZXoeh4zXnsGSo9coUm+5Kit5w3vbSpA544="}]}, {"Route": "Dashboard/assets/img/PostFinance.4hsu3t0zv7.png", "AssetFile": "Dashboard/assets/img/PostFinance.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3774"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qCKlVCPPZaUg8GPlxVL3MP+kwsFzZ0rqx1DtWus+VIc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4hsu3t0zv7"}, {"Name": "integrity", "Value": "sha256-qCKlVCPPZaUg8GPlxVL3MP+kwsFzZ0rqx1DtWus+VIc="}, {"Name": "label", "Value": "Dashboard/assets/img/PostFinance.png"}]}, {"Route": "Dashboard/assets/img/PostFinance.png", "AssetFile": "Dashboard/assets/img/PostFinance.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3774"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qCKlVCPPZaUg8GPlxVL3MP+kwsFzZ0rqx1DtWus+VIc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qCKlVCPPZaUg8GPlxVL3MP+kwsFzZ0rqx1DtWus+VIc="}]}, {"Route": "Dashboard/assets/img/Post_Code.pgsifodmme.png", "AssetFile": "Dashboard/assets/img/Post_Code.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2469"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"mX/4jdy45NsRcjHViLVn1xne+qFNQA+2eu0x7sUUC+c=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pgsifodmme"}, {"Name": "integrity", "Value": "sha256-mX/4jdy45NsRcjHViLVn1xne+qFNQA+2eu0x7sUUC+c="}, {"Name": "label", "Value": "Dashboard/assets/img/Post_Code.png"}]}, {"Route": "Dashboard/assets/img/Post_Code.png", "AssetFile": "Dashboard/assets/img/Post_Code.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2469"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"mX/4jdy45NsRcjHViLVn1xne+qFNQA+2eu0x7sUUC+c=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mX/4jdy45NsRcjHViLVn1xne+qFNQA+2eu0x7sUUC+c="}]}, {"Route": "Dashboard/assets/img/Saldo.lkqj4z6h2q.png", "AssetFile": "Dashboard/assets/img/Saldo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4743"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"u2Kwr2KXo3qIIBA/Jaxd/YBcl3wSHFB1NMonNUTcpiA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lkqj4z6h2q"}, {"Name": "integrity", "Value": "sha256-u2Kwr2KXo3qIIBA/Jaxd/YBcl3wSHFB1NMonNUTcpiA="}, {"Name": "label", "Value": "Dashboard/assets/img/Saldo.png"}]}, {"Route": "Dashboard/assets/img/Saldo.png", "AssetFile": "Dashboard/assets/img/Saldo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4743"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"u2Kwr2KXo3qIIBA/Jaxd/YBcl3wSHFB1NMonNUTcpiA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u2Kwr2KXo3qIIBA/Jaxd/YBcl3wSHFB1NMonNUTcpiA="}]}, {"Route": "Dashboard/assets/img/Schloss.png", "AssetFile": "Dashboard/assets/img/Schloss.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2413"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"spciVcJ2EWjd0bYyjYcZi1ILajCY8SR2jb16+Y4ev6M=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-spciVcJ2EWjd0bYyjYcZi1ILajCY8SR2jb16+Y4ev6M="}]}, {"Route": "Dashboard/assets/img/Schloss.u42ytrqwrl.png", "AssetFile": "Dashboard/assets/img/Schloss.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2413"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"spciVcJ2EWjd0bYyjYcZi1ILajCY8SR2jb16+Y4ev6M=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u42ytrqwrl"}, {"Name": "integrity", "Value": "sha256-spciVcJ2EWjd0bYyjYcZi1ILajCY8SR2jb16+Y4ev6M="}, {"Name": "label", "Value": "Dashboard/assets/img/Schloss.png"}]}, {"Route": "Dashboard/assets/img/Stop.i5hczuhaqb.png", "AssetFile": "Dashboard/assets/img/Stop.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7615"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"KlOCel4GwCaP/ClyBZAbVRTaQbPE7CxbqkGYm+LFHBs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i5hczuhaqb"}, {"Name": "integrity", "Value": "sha256-KlOCel4GwCaP/ClyBZAbVRTaQbPE7CxbqkGYm+LFHBs="}, {"Name": "label", "Value": "Dashboard/assets/img/Stop.png"}]}, {"Route": "Dashboard/assets/img/Stop.png", "AssetFile": "Dashboard/assets/img/Stop.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7615"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"KlOCel4GwCaP/ClyBZAbVRTaQbPE7CxbqkGYm+LFHBs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KlOCel4GwCaP/ClyBZAbVRTaQbPE7CxbqkGYm+LFHBs="}]}, {"Route": "Dashboard/assets/img/Twint.png", "AssetFile": "Dashboard/assets/img/Twint.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1882"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"yQxjtf5ZFzgwAPiT53kFUGvFHg7Qi+6TwRbWtVysetM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yQxjtf5ZFzgwAPiT53kFUGvFHg7Qi+6TwRbWtVysetM="}]}, {"Route": "Dashboard/assets/img/Twint.xag9m31aj6.png", "AssetFile": "Dashboard/assets/img/Twint.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1882"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"yQxjtf5ZFzgwAPiT53kFUGvFHg7Qi+6TwRbWtVysetM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xag9m31aj6"}, {"Name": "integrity", "Value": "sha256-yQxjtf5ZFzgwAPiT53kFUGvFHg7Qi+6TwRbWtVysetM="}, {"Name": "label", "Value": "Dashboard/assets/img/Twint.png"}]}, {"Route": "Dashboard/assets/img/Visa_Konto.6yr6353tw7.png", "AssetFile": "Dashboard/assets/img/Visa_Konto.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7240"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"AqYCkkCKKf2n+zRXmeRtcU1/uQXS7ImrdvSIJ0/hpsg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6yr6353tw7"}, {"Name": "integrity", "Value": "sha256-AqYCkkCKKf2n+zRXmeRtcU1/uQXS7ImrdvSIJ0/hpsg="}, {"Name": "label", "Value": "Dashboard/assets/img/Visa_Konto.png"}]}, {"Route": "Dashboard/assets/img/Visa_Konto.png", "AssetFile": "Dashboard/assets/img/Visa_Konto.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7240"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"AqYCkkCKKf2n+zRXmeRtcU1/uQXS7ImrdvSIJ0/hpsg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AqYCkkCKKf2n+zRXmeRtcU1/uQXS7ImrdvSIJ0/hpsg="}]}, {"Route": "Dashboard/assets/img/avatars/1.5dw95i7i20.png", "AssetFile": "Dashboard/assets/img/avatars/1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14015"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"GakibeacIikL1hB1FheaFS4jzVDwrlSuxEt7/2BM/58=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5dw95i7i20"}, {"Name": "integrity", "Value": "sha256-GakibeacIikL1hB1FheaFS4jzVDwrlSuxEt7/2BM/58="}, {"Name": "label", "Value": "Dashboard/assets/img/avatars/1.png"}]}, {"Route": "Dashboard/assets/img/avatars/1.png", "AssetFile": "Dashboard/assets/img/avatars/1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14015"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"GakibeacIikL1hB1FheaFS4jzVDwrlSuxEt7/2BM/58=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GakibeacIikL1hB1FheaFS4jzVDwrlSuxEt7/2BM/58="}]}, {"Route": "Dashboard/assets/img/avatars/5.png", "AssetFile": "Dashboard/assets/img/avatars/5.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "20488"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"HvLf/joyX29gx1iOJmVCH48YYR9PJky53y7HIrgMoZg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HvLf/joyX29gx1iOJmVCH48YYR9PJky53y7HIrgMoZg="}]}, {"Route": "Dashboard/assets/img/avatars/5.y7wk20hnyd.png", "AssetFile": "Dashboard/assets/img/avatars/5.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "20488"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"HvLf/joyX29gx1iOJmVCH48YYR9PJky53y7HIrgMoZg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y7wk20hnyd"}, {"Name": "integrity", "Value": "sha256-HvLf/joyX29gx1iOJmVCH48YYR9PJky53y7HIrgMoZg="}, {"Name": "label", "Value": "Dashboard/assets/img/avatars/5.png"}]}, {"Route": "Dashboard/assets/img/avatars/6.laj0069a7v.png", "AssetFile": "Dashboard/assets/img/avatars/6.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15198"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"U7ZWHRdqadq3R4Pdjgmy8IfOW0ph9f71C/aTXSwrUTQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "laj0069a7v"}, {"Name": "integrity", "Value": "sha256-U7ZWHRdqadq3R4Pdjgmy8IfOW0ph9f71C/aTXSwrUTQ="}, {"Name": "label", "Value": "Dashboard/assets/img/avatars/6.png"}]}, {"Route": "Dashboard/assets/img/avatars/6.png", "AssetFile": "Dashboard/assets/img/avatars/6.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15198"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"U7ZWHRdqadq3R4Pdjgmy8IfOW0ph9f71C/aTXSwrUTQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-U7ZWHRdqadq3R4Pdjgmy8IfOW0ph9f71C/aTXSwrUTQ="}]}, {"Route": "Dashboard/assets/img/avatars/7.jzhuf0rlpo.png", "AssetFile": "Dashboard/assets/img/avatars/7.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15180"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"ceq+EmgetHDkW1J4Nxhn3STmQTcJAMU5qVoeY7CFo68=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jzhuf0rlpo"}, {"Name": "integrity", "Value": "sha256-ceq+EmgetHDkW1J4Nxhn3STmQTcJAMU5qVoeY7CFo68="}, {"Name": "label", "Value": "Dashboard/assets/img/avatars/7.png"}]}, {"Route": "Dashboard/assets/img/avatars/7.png", "AssetFile": "Dashboard/assets/img/avatars/7.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15180"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"ceq+EmgetHDkW1J4Nxhn3STmQTcJAMU5qVoeY7CFo68=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ceq+EmgetHDkW1J4Nxhn3STmQTcJAMU5qVoeY7CFo68="}]}, {"Route": "Dashboard/assets/img/backgrounds/18.jpg", "AssetFile": "Dashboard/assets/img/backgrounds/18.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "88783"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"/fVYHI8QJL+aw0jk/AjqF8Q8usxEunzAwNftu49AN/o=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/fVYHI8QJL+aw0jk/AjqF8Q8usxEunzAwNftu49AN/o="}]}, {"Route": "Dashboard/assets/img/backgrounds/18.zcrq1vg6ym.jpg", "AssetFile": "Dashboard/assets/img/backgrounds/18.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "88783"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"/fVYHI8QJL+aw0jk/AjqF8Q8usxEunzAwNftu49AN/o=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zcrq1vg6ym"}, {"Name": "integrity", "Value": "sha256-/fVYHI8QJL+aw0jk/AjqF8Q8usxEunzAwNftu49AN/o="}, {"Name": "label", "Value": "Dashboard/assets/img/backgrounds/18.jpg"}]}, {"Route": "Dashboard/assets/img/cleaning.png", "AssetFile": "Dashboard/assets/img/cleaning.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1739"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"7r9n/l0ePgee2rx7jPgsDkH7+zeuOLGVqlySnPuze/w=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7r9n/l0ePgee2rx7jPgsDkH7+zeuOLGVqlySnPuze/w="}]}, {"Route": "Dashboard/assets/img/cleaning.q6iwg7513g.png", "AssetFile": "Dashboard/assets/img/cleaning.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1739"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"7r9n/l0ePgee2rx7jPgsDkH7+zeuOLGVqlySnPuze/w=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q6iwg7513g"}, {"Name": "integrity", "Value": "sha256-7r9n/l0ePgee2rx7jPgsDkH7+zeuOLGVqlySnPuze/w="}, {"Name": "label", "Value": "Dashboard/assets/img/cleaning.png"}]}, {"Route": "Dashboard/assets/img/color.66as5qo0c3.png", "AssetFile": "Dashboard/assets/img/color.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1203"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"fm+qDDuQ4MB4tpAFy89RQaaqpuRvRqYn6hkWRs1YqR8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "66as5qo0c3"}, {"Name": "integrity", "Value": "sha256-fm+qDDuQ4MB4tpAFy89RQaaqpuRvRqYn6hkWRs1YqR8="}, {"Name": "label", "Value": "Dashboard/assets/img/color.png"}]}, {"Route": "Dashboard/assets/img/color.png", "AssetFile": "Dashboard/assets/img/color.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1203"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"fm+qDDuQ4MB4tpAFy89RQaaqpuRvRqYn6hkWRs1YqR8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fm+qDDuQ4MB4tpAFy89RQaaqpuRvRqYn6hkWRs1YqR8="}]}, {"Route": "Dashboard/assets/img/de-flag.png", "AssetFile": "Dashboard/assets/img/de-flag.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "20056"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"szXHI+v9CKe9gq/sSk7wCT3Ff+xiY9WkgU9T3GkN/Co=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-szXHI+v9CKe9gq/sSk7wCT3Ff+xiY9WkgU9T3GkN/Co="}]}, {"Route": "Dashboard/assets/img/de-flag.xjszvk8663.png", "AssetFile": "Dashboard/assets/img/de-flag.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "20056"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"szXHI+v9CKe9gq/sSk7wCT3Ff+xiY9WkgU9T3GkN/Co=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xjszvk8663"}, {"Name": "integrity", "Value": "sha256-szXHI+v9CKe9gq/sSk7wCT3Ff+xiY9WkgU9T3GkN/Co="}, {"Name": "label", "Value": "Dashboard/assets/img/de-flag.png"}]}, {"Route": "Dashboard/assets/img/elements/1.1dcgywla5o.jpg", "AssetFile": "Dashboard/assets/img/elements/1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "18135"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"a6t4IujkLq+637JfyvpU/86ABXnFoB8MdTUNkb6kgRI=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1dcgywla5o"}, {"Name": "integrity", "Value": "sha256-a6t4IujkLq+637JfyvpU/86ABXnFoB8MdTUNkb6kgRI="}, {"Name": "label", "Value": "Dashboard/assets/img/elements/1.jpg"}]}, {"Route": "Dashboard/assets/img/elements/1.jpg", "AssetFile": "Dashboard/assets/img/elements/1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "18135"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"a6t4IujkLq+637JfyvpU/86ABXnFoB8MdTUNkb6kgRI=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a6t4IujkLq+637JfyvpU/86ABXnFoB8MdTUNkb6kgRI="}]}, {"Route": "Dashboard/assets/img/elements/11.jpg", "AssetFile": "Dashboard/assets/img/elements/11.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19087"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"5vGI0LfpSt/t7R3BRN5cbvkiIndtuQc2P26VTmhBCjc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5vGI0LfpSt/t7R3BRN5cbvkiIndtuQc2P26VTmhBCjc="}]}, {"Route": "Dashboard/assets/img/elements/11.uj5sfxbjnm.jpg", "AssetFile": "Dashboard/assets/img/elements/11.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19087"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"5vGI0LfpSt/t7R3BRN5cbvkiIndtuQc2P26VTmhBCjc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uj5sfxbjnm"}, {"Name": "integrity", "Value": "sha256-5vGI0LfpSt/t7R3BRN5cbvkiIndtuQc2P26VTmhBCjc="}, {"Name": "label", "Value": "Dashboard/assets/img/elements/11.jpg"}]}, {"Route": "Dashboard/assets/img/elements/12.jpg", "AssetFile": "Dashboard/assets/img/elements/12.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "28075"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"qRb0YTolNxhZcBKxjilnX5nPu5QGChNoOdFZiNDx9pA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qRb0YTolNxhZcBKxjilnX5nPu5QGChNoOdFZiNDx9pA="}]}, {"Route": "Dashboard/assets/img/elements/12.xqqhdiskkx.jpg", "AssetFile": "Dashboard/assets/img/elements/12.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28075"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"qRb0YTolNxhZcBKxjilnX5nPu5QGChNoOdFZiNDx9pA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xqqhdiskkx"}, {"Name": "integrity", "Value": "sha256-qRb0YTolNxhZcBKxjilnX5nPu5QGChNoOdFZiNDx9pA="}, {"Name": "label", "Value": "Dashboard/assets/img/elements/12.jpg"}]}, {"Route": "Dashboard/assets/img/elements/13.jpg", "AssetFile": "Dashboard/assets/img/elements/13.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12929"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"LE9DuCIGutXSCGXben/ETqROLKWFYowfILx/d+sCErs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LE9DuCIGutXSCGXben/ETqROLKWFYowfILx/d+sCErs="}]}, {"Route": "Dashboard/assets/img/elements/13.sqtpmqt4ko.jpg", "AssetFile": "Dashboard/assets/img/elements/13.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12929"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"LE9DuCIGutXSCGXben/ETqROLKWFYowfILx/d+sCErs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sqtpmqt4ko"}, {"Name": "integrity", "Value": "sha256-LE9DuCIGutXSCGXben/ETqROLKWFYowfILx/d+sCErs="}, {"Name": "label", "Value": "Dashboard/assets/img/elements/13.jpg"}]}, {"Route": "Dashboard/assets/img/elements/17.jpg", "AssetFile": "Dashboard/assets/img/elements/17.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19716"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"0rrsaoqTIj8PlnoHjT2iT93sfPLwJt9QQLN4CYmiwYo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0rrsaoqTIj8PlnoHjT2iT93sfPLwJt9QQLN4CYmiwYo="}]}, {"Route": "Dashboard/assets/img/elements/17.mbfplb2995.jpg", "AssetFile": "Dashboard/assets/img/elements/17.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19716"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"0rrsaoqTIj8PlnoHjT2iT93sfPLwJt9QQLN4CYmiwYo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mbfplb2995"}, {"Name": "integrity", "Value": "sha256-0rrsaoqTIj8PlnoHjT2iT93sfPLwJt9QQLN4CYmiwYo="}, {"Name": "label", "Value": "Dashboard/assets/img/elements/17.jpg"}]}, {"Route": "Dashboard/assets/img/elements/18.4kfik9hhpd.jpg", "AssetFile": "Dashboard/assets/img/elements/18.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "31223"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"RMeMm27YNLobckPL3MTiDAEeuUKVF+/AY4KPuNp+cwU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4kfik9hhpd"}, {"Name": "integrity", "Value": "sha256-RMeMm27YNLobckPL3MTiDAEeuUKVF+/AY4KPuNp+cwU="}, {"Name": "label", "Value": "Dashboard/assets/img/elements/18.jpg"}]}, {"Route": "Dashboard/assets/img/elements/18.jpg", "AssetFile": "Dashboard/assets/img/elements/18.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "31223"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"RMeMm27YNLobckPL3MTiDAEeuUKVF+/AY4KPuNp+cwU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RMeMm27YNLobckPL3MTiDAEeuUKVF+/AY4KPuNp+cwU="}]}, {"Route": "Dashboard/assets/img/elements/19.a3beqhyfaj.jpg", "AssetFile": "Dashboard/assets/img/elements/19.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15553"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"WrLKEezeub2INc0IH/3ZSISrGotKXmApabXkuOlGUHw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a3beqhyfaj"}, {"Name": "integrity", "Value": "sha256-WrLKEezeub2INc0IH/3ZSISrGotKXmApabXkuOlGUHw="}, {"Name": "label", "Value": "Dashboard/assets/img/elements/19.jpg"}]}, {"Route": "Dashboard/assets/img/elements/19.jpg", "AssetFile": "Dashboard/assets/img/elements/19.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15553"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"WrLKEezeub2INc0IH/3ZSISrGotKXmApabXkuOlGUHw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WrLKEezeub2INc0IH/3ZSISrGotKXmApabXkuOlGUHw="}]}, {"Route": "Dashboard/assets/img/elements/2.j3cw3e50ho.jpg", "AssetFile": "Dashboard/assets/img/elements/2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "13332"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"wc+6ap17bBXk7icsl441cpNtf70chVoSoXrXRHqu/jo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j3cw3e50ho"}, {"Name": "integrity", "Value": "sha256-wc+6ap17bBXk7icsl441cpNtf70chVoSoXrXRHqu/jo="}, {"Name": "label", "Value": "Dashboard/assets/img/elements/2.jpg"}]}, {"Route": "Dashboard/assets/img/elements/2.jpg", "AssetFile": "Dashboard/assets/img/elements/2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13332"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"wc+6ap17bBXk7icsl441cpNtf70chVoSoXrXRHqu/jo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wc+6ap17bBXk7icsl441cpNtf70chVoSoXrXRHqu/jo="}]}, {"Route": "Dashboard/assets/img/elements/20.6ffy4hs6lt.jpg", "AssetFile": "Dashboard/assets/img/elements/20.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16593"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"kipYcAx+dLYKPwZ98vbg++WhjJgPD9C+aVL0/wOWyXI=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6ffy4hs6lt"}, {"Name": "integrity", "Value": "sha256-kipYcAx+dLYKPwZ98vbg++WhjJgPD9C+aVL0/wOWyXI="}, {"Name": "label", "Value": "Dashboard/assets/img/elements/20.jpg"}]}, {"Route": "Dashboard/assets/img/elements/20.jpg", "AssetFile": "Dashboard/assets/img/elements/20.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16593"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"kipYcAx+dLYKPwZ98vbg++WhjJgPD9C+aVL0/wOWyXI=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kipYcAx+dLYKPwZ98vbg++WhjJgPD9C+aVL0/wOWyXI="}]}, {"Route": "Dashboard/assets/img/elements/3.jpg", "AssetFile": "Dashboard/assets/img/elements/3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24285"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"u5X5mp03DIEAhT9BNpA8dRldGSRpNg/DKCgmtuN7fSA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u5X5mp03DIEAhT9BNpA8dRldGSRpNg/DKCgmtuN7fSA="}]}, {"Route": "Dashboard/assets/img/elements/3.rga7lkqrdc.jpg", "AssetFile": "Dashboard/assets/img/elements/3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24285"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"u5X5mp03DIEAhT9BNpA8dRldGSRpNg/DKCgmtuN7fSA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rga7lkqrdc"}, {"Name": "integrity", "Value": "sha256-u5X5mp03DIEAhT9BNpA8dRldGSRpNg/DKCgmtuN7fSA="}, {"Name": "label", "Value": "Dashboard/assets/img/elements/3.jpg"}]}, {"Route": "Dashboard/assets/img/elements/4.jpg", "AssetFile": "Dashboard/assets/img/elements/4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22875"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"h6CzEKD4MIeFaGkx+b0QuMkWMTa+YkPPY7/hdZKSjG0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h6CzEKD4MIeFaGkx+b0QuMkWMTa+YkPPY7/hdZKSjG0="}]}, {"Route": "Dashboard/assets/img/elements/4.tv0gns518i.jpg", "AssetFile": "Dashboard/assets/img/elements/4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22875"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"h6CzEKD4MIeFaGkx+b0QuMkWMTa+YkPPY7/hdZKSjG0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tv0gns518i"}, {"Name": "integrity", "Value": "sha256-h6CzEKD4MIeFaGkx+b0QuMkWMTa+YkPPY7/hdZKSjG0="}, {"Name": "label", "Value": "Dashboard/assets/img/elements/4.jpg"}]}, {"Route": "Dashboard/assets/img/elements/5.jpg", "AssetFile": "Dashboard/assets/img/elements/5.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22938"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"IePvmLa1hS+rQhdTHvyz2DDS8FVrxQ9W28QJ4S0lIwA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IePvmLa1hS+rQhdTHvyz2DDS8FVrxQ9W28QJ4S0lIwA="}]}, {"Route": "Dashboard/assets/img/elements/5.vk7ev4b9zi.jpg", "AssetFile": "Dashboard/assets/img/elements/5.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22938"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"IePvmLa1hS+rQhdTHvyz2DDS8FVrxQ9W28QJ4S0lIwA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vk7ev4b9zi"}, {"Name": "integrity", "Value": "sha256-IePvmLa1hS+rQhdTHvyz2DDS8FVrxQ9W28QJ4S0lIwA="}, {"Name": "label", "Value": "Dashboard/assets/img/elements/5.jpg"}]}, {"Route": "Dashboard/assets/img/elements/7.jpg", "AssetFile": "Dashboard/assets/img/elements/7.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "21183"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"/fRu+iZq/ZKlxul5HBvvGwMnXsHOKSKQ7FF0ftE37Wg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/fRu+iZq/ZKlxul5HBvvGwMnXsHOKSKQ7FF0ftE37Wg="}]}, {"Route": "Dashboard/assets/img/elements/7.n38sfx2p3i.jpg", "AssetFile": "Dashboard/assets/img/elements/7.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "21183"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"/fRu+iZq/ZKlxul5HBvvGwMnXsHOKSKQ7FF0ftE37Wg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n38sfx2p3i"}, {"Name": "integrity", "Value": "sha256-/fRu+iZq/ZKlxul5HBvvGwMnXsHOKSKQ7FF0ftE37Wg="}, {"Name": "label", "Value": "Dashboard/assets/img/elements/7.jpg"}]}, {"Route": "Dashboard/assets/img/favicon/favicon.2hcxmlxs2r.ico", "AssetFile": "Dashboard/assets/img/favicon/favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1393"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"ZhdZMzzh105upKWgioimw/aKt56k3eqSVgY26ka5mSM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2hcxmlxs2r"}, {"Name": "integrity", "Value": "sha256-ZhdZMzzh105upKWgioimw/aKt56k3eqSVgY26ka5mSM="}, {"Name": "label", "Value": "Dashboard/assets/img/favicon/favicon.ico"}]}, {"Route": "Dashboard/assets/img/favicon/favicon.ico", "AssetFile": "Dashboard/assets/img/favicon/favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1393"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"ZhdZMzzh105upKWgioimw/aKt56k3eqSVgY26ka5mSM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZhdZMzzh105upKWgioimw/aKt56k3eqSVgY26ka5mSM="}]}, {"Route": "Dashboard/assets/img/fr-flag.l2zu6d6aky.png", "AssetFile": "Dashboard/assets/img/fr-flag.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16395"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"DQdfguK09Pp2IQGulUiKQl0t+fQMW34SCVRQnGNYeb8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l2zu6d6aky"}, {"Name": "integrity", "Value": "sha256-DQdfguK09Pp2IQGulUiKQl0t+fQMW34SCVRQnGNYeb8="}, {"Name": "label", "Value": "Dashboard/assets/img/fr-flag.png"}]}, {"Route": "Dashboard/assets/img/fr-flag.png", "AssetFile": "Dashboard/assets/img/fr-flag.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16395"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"DQdfguK09Pp2IQGulUiKQl0t+fQMW34SCVRQnGNYeb8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DQdfguK09Pp2IQGulUiKQl0t+fQMW34SCVRQnGNYeb8="}]}, {"Route": "Dashboard/assets/img/gisper.c7xywxqxv1.png", "AssetFile": "Dashboard/assets/img/gisper.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "808"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"mACdmOyWbfVfaOT7snBVUQudHFS9dFQCDOwkEha0dKQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c7xywxqxv1"}, {"Name": "integrity", "Value": "sha256-mACdmOyWbfVfaOT7snBVUQudHFS9dFQCDOwkEha0dKQ="}, {"Name": "label", "Value": "Dashboard/assets/img/gisper.png"}]}, {"Route": "Dashboard/assets/img/gisper.png", "AssetFile": "Dashboard/assets/img/gisper.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "808"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"mACdmOyWbfVfaOT7snBVUQudHFS9dFQCDOwkEha0dKQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mACdmOyWbfVfaOT7snBVUQudHFS9dFQCDOwkEha0dKQ="}]}, {"Route": "Dashboard/assets/img/iconfacebook.dtpc2iklyd.png", "AssetFile": "Dashboard/assets/img/iconfacebook.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1316"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"sVeKg9pECr80NtWARqhMXCHDeAyjIcsvQA7rxIg2vfc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dtpc2iklyd"}, {"Name": "integrity", "Value": "sha256-sVeKg9pECr80NtWARqhMXCHDeAyjIcsvQA7rxIg2vfc="}, {"Name": "label", "Value": "Dashboard/assets/img/iconfacebook.png"}]}, {"Route": "Dashboard/assets/img/iconfacebook.png", "AssetFile": "Dashboard/assets/img/iconfacebook.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1316"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"sVeKg9pECr80NtWARqhMXCHDeAyjIcsvQA7rxIg2vfc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sVeKg9pECr80NtWARqhMXCHDeAyjIcsvQA7rxIg2vfc="}]}, {"Route": "Dashboard/assets/img/iconinsta.g2myl4c9mh.png", "AssetFile": "Dashboard/assets/img/iconinsta.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3489"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"OGns6sYmgaRTAhNaahcbjd0J77De5QfwTOXBBAvTyAc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g2myl4c9mh"}, {"Name": "integrity", "Value": "sha256-OGns6sYmgaRTAhNaahcbjd0J77De5QfwTOXBBAvTyAc="}, {"Name": "label", "Value": "Dashboard/assets/img/iconinsta.png"}]}, {"Route": "Dashboard/assets/img/iconinsta.png", "AssetFile": "Dashboard/assets/img/iconinsta.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3489"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"OGns6sYmgaRTAhNaahcbjd0J77De5QfwTOXBBAvTyAc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OGns6sYmgaRTAhNaahcbjd0J77De5QfwTOXBBAvTyAc="}]}, {"Route": "Dashboard/assets/img/iconlinked.64c1t2dskq.png", "AssetFile": "Dashboard/assets/img/iconlinked.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "608"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"lki8XsRUb1INt7yaPDLa4rU4TZO9VVRmOkspedGnl0E=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "64c1t2dskq"}, {"Name": "integrity", "Value": "sha256-lki8XsRUb1INt7yaPDLa4rU4TZO9VVRmOkspedGnl0E="}, {"Name": "label", "Value": "Dashboard/assets/img/iconlinked.png"}]}, {"Route": "Dashboard/assets/img/iconlinked.png", "AssetFile": "Dashboard/assets/img/iconlinked.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "608"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"lki8XsRUb1INt7yaPDLa4rU4TZO9VVRmOkspedGnl0E=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lki8XsRUb1INt7yaPDLa4rU4TZO9VVRmOkspedGnl0E="}]}, {"Route": "Dashboard/assets/img/icons/brands/asana.m8ed7lopx2.png", "AssetFile": "Dashboard/assets/img/icons/brands/asana.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2236"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qtt8Djtym3fWp5n+PpH9HVsMcfniOIs8KlSEVvgMFcs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m8ed7lopx2"}, {"Name": "integrity", "Value": "sha256-qtt8Djtym3fWp5n+PpH9HVsMcfniOIs8KlSEVvgMFcs="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/brands/asana.png"}]}, {"Route": "Dashboard/assets/img/icons/brands/asana.png", "AssetFile": "Dashboard/assets/img/icons/brands/asana.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2236"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qtt8Djtym3fWp5n+PpH9HVsMcfniOIs8KlSEVvgMFcs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qtt8Djtym3fWp5n+PpH9HVsMcfniOIs8KlSEVvgMFcs="}]}, {"Route": "Dashboard/assets/img/icons/brands/behance.coaxahx6t3.png", "AssetFile": "Dashboard/assets/img/icons/brands/behance.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1731"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"tI1zFLsfbdaZZ0d8qXIHD26raxUCvNRQIvc/gNw+5ck=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "coaxahx6t3"}, {"Name": "integrity", "Value": "sha256-tI1zFLsfbdaZZ0d8qXIHD26raxUCvNRQIvc/gNw+5ck="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/brands/behance.png"}]}, {"Route": "Dashboard/assets/img/icons/brands/behance.png", "AssetFile": "Dashboard/assets/img/icons/brands/behance.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1731"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"tI1zFLsfbdaZZ0d8qXIHD26raxUCvNRQIvc/gNw+5ck=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tI1zFLsfbdaZZ0d8qXIHD26raxUCvNRQIvc/gNw+5ck="}]}, {"Route": "Dashboard/assets/img/icons/brands/dribbble.png", "AssetFile": "Dashboard/assets/img/icons/brands/dribbble.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2848"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nFjwdkk45306nydk17zF40vBBVlUD7in5gK7bzesB7c=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nFjwdkk45306nydk17zF40vBBVlUD7in5gK7bzesB7c="}]}, {"Route": "Dashboard/assets/img/icons/brands/dribbble.w706m358zy.png", "AssetFile": "Dashboard/assets/img/icons/brands/dribbble.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2848"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nFjwdkk45306nydk17zF40vBBVlUD7in5gK7bzesB7c=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w706m358zy"}, {"Name": "integrity", "Value": "sha256-nFjwdkk45306nydk17zF40vBBVlUD7in5gK7bzesB7c="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/brands/dribbble.png"}]}, {"Route": "Dashboard/assets/img/icons/brands/facebook.h6xi8zk54u.png", "AssetFile": "Dashboard/assets/img/icons/brands/facebook.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "681"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"h5NQXedd04yWx+AtNmndoyZpe9mF71FgFrcv2/XGHIk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h6xi8zk54u"}, {"Name": "integrity", "Value": "sha256-h5NQXedd04yWx+AtNmndoyZpe9mF71FgFrcv2/XGHIk="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/brands/facebook.png"}]}, {"Route": "Dashboard/assets/img/icons/brands/facebook.png", "AssetFile": "Dashboard/assets/img/icons/brands/facebook.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "681"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"h5NQXedd04yWx+AtNmndoyZpe9mF71FgFrcv2/XGHIk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h5NQXedd04yWx+AtNmndoyZpe9mF71FgFrcv2/XGHIk="}]}, {"Route": "Dashboard/assets/img/icons/brands/github.png", "AssetFile": "Dashboard/assets/img/icons/brands/github.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2169"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"SsEbqlIero6v0KTzFtKULqgTbbHlf2fopiVU3rNo/eQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SsEbqlIero6v0KTzFtKULqgTbbHlf2fopiVU3rNo/eQ="}]}, {"Route": "Dashboard/assets/img/icons/brands/github.q7i6lubamj.png", "AssetFile": "Dashboard/assets/img/icons/brands/github.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2169"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"SsEbqlIero6v0KTzFtKULqgTbbHlf2fopiVU3rNo/eQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q7i6lubamj"}, {"Name": "integrity", "Value": "sha256-SsEbqlIero6v0KTzFtKULqgTbbHlf2fopiVU3rNo/eQ="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/brands/github.png"}]}, {"Route": "Dashboard/assets/img/icons/brands/google.png", "AssetFile": "Dashboard/assets/img/icons/brands/google.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1932"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"fk0pyxCqMVZMWsLcBvKoiymP8ge6euW1y0hLwnjzXhw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fk0pyxCqMVZMWsLcBvKoiymP8ge6euW1y0hLwnjzXhw="}]}, {"Route": "Dashboard/assets/img/icons/brands/google.yn247w80id.png", "AssetFile": "Dashboard/assets/img/icons/brands/google.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1932"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"fk0pyxCqMVZMWsLcBvKoiymP8ge6euW1y0hLwnjzXhw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yn247w80id"}, {"Name": "integrity", "Value": "sha256-fk0pyxCqMVZMWsLcBvKoiymP8ge6euW1y0hLwnjzXhw="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/brands/google.png"}]}, {"Route": "Dashboard/assets/img/icons/brands/instagram.png", "AssetFile": "Dashboard/assets/img/icons/brands/instagram.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3128"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"rzPFpJpaFX/c33A9uelKUw7/WreRQb6FwDQYR+bg/6Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rzPFpJpaFX/c33A9uelKUw7/WreRQb6FwDQYR+bg/6Q="}]}, {"Route": "Dashboard/assets/img/icons/brands/instagram.t9b1hn39ce.png", "AssetFile": "Dashboard/assets/img/icons/brands/instagram.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3128"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"rzPFpJpaFX/c33A9uelKUw7/WreRQb6FwDQYR+bg/6Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t9b1hn39ce"}, {"Name": "integrity", "Value": "sha256-rzPFpJpaFX/c33A9uelKUw7/WreRQb6FwDQYR+bg/6Q="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/brands/instagram.png"}]}, {"Route": "Dashboard/assets/img/icons/brands/mailchimp.37ik0yzl65.png", "AssetFile": "Dashboard/assets/img/icons/brands/mailchimp.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1405"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"H9EcNXJ47xJSU4UZYTpkdBFBcAQ0s+ggWOtxrWNfzK4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "37ik0yzl65"}, {"Name": "integrity", "Value": "sha256-H9EcNXJ47xJSU4UZYTpkdBFBcAQ0s+ggWOtxrWNfzK4="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/brands/mailchimp.png"}]}, {"Route": "Dashboard/assets/img/icons/brands/mailchimp.png", "AssetFile": "Dashboard/assets/img/icons/brands/mailchimp.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1405"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"H9EcNXJ47xJSU4UZYTpkdBFBcAQ0s+ggWOtxrWNfzK4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H9EcNXJ47xJSU4UZYTpkdBFBcAQ0s+ggWOtxrWNfzK4="}]}, {"Route": "Dashboard/assets/img/icons/brands/slack.png", "AssetFile": "Dashboard/assets/img/icons/brands/slack.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2550"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oE0mYTJAJstA8DXOzLlAUnWFXyWFtU+LhHcbRcjs0mM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oE0mYTJAJstA8DXOzLlAUnWFXyWFtU+LhHcbRcjs0mM="}]}, {"Route": "Dashboard/assets/img/icons/brands/slack.w43jfux8yp.png", "AssetFile": "Dashboard/assets/img/icons/brands/slack.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2550"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oE0mYTJAJstA8DXOzLlAUnWFXyWFtU+LhHcbRcjs0mM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w43jfux8yp"}, {"Name": "integrity", "Value": "sha256-oE0mYTJAJstA8DXOzLlAUnWFXyWFtU+LhHcbRcjs0mM="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/brands/slack.png"}]}, {"Route": "Dashboard/assets/img/icons/brands/twitter.btqwmt39wx.png", "AssetFile": "Dashboard/assets/img/icons/brands/twitter.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1564"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"vymBSGM14nM2pEAl01p8GKLm8OifsEwP3CPlsZTdCfI=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "btqwmt39wx"}, {"Name": "integrity", "Value": "sha256-vymBSGM14nM2pEAl01p8GKLm8OifsEwP3CPlsZTdCfI="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/brands/twitter.png"}]}, {"Route": "Dashboard/assets/img/icons/brands/twitter.png", "AssetFile": "Dashboard/assets/img/icons/brands/twitter.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1564"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"vymBSGM14nM2pEAl01p8GKLm8OifsEwP3CPlsZTdCfI=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vymBSGM14nM2pEAl01p8GKLm8OifsEwP3CPlsZTdCfI="}]}, {"Route": "Dashboard/assets/img/icons/unicons/cc-primary.7cz3d43tgn.png", "AssetFile": "Dashboard/assets/img/icons/unicons/cc-primary.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "702"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"pwKhaz9nifgYMTnW965kJL3jWm/1EKMaa9+0juV+SQ0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7cz3d43tgn"}, {"Name": "integrity", "Value": "sha256-pwKhaz9nifgYMTnW965kJL3jWm/1EKMaa9+0juV+SQ0="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/unicons/cc-primary.png"}]}, {"Route": "Dashboard/assets/img/icons/unicons/cc-primary.png", "AssetFile": "Dashboard/assets/img/icons/unicons/cc-primary.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "702"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"pwKhaz9nifgYMTnW965kJL3jWm/1EKMaa9+0juV+SQ0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pwKhaz9nifgYMTnW965kJL3jWm/1EKMaa9+0juV+SQ0="}]}, {"Route": "Dashboard/assets/img/icons/unicons/cc-success.png", "AssetFile": "Dashboard/assets/img/icons/unicons/cc-success.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "776"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"rC5AxYDuZkEY6+UekvJZ4HJdxe48g5byrHETLimP4Lc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rC5AxYDuZkEY6+UekvJZ4HJdxe48g5byrHETLimP4Lc="}]}, {"Route": "Dashboard/assets/img/icons/unicons/cc-success.sozk5ui7zb.png", "AssetFile": "Dashboard/assets/img/icons/unicons/cc-success.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "776"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"rC5AxYDuZkEY6+UekvJZ4HJdxe48g5byrHETLimP4Lc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sozk5ui7zb"}, {"Name": "integrity", "Value": "sha256-rC5AxYDuZkEY6+UekvJZ4HJdxe48g5byrHETLimP4Lc="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/unicons/cc-success.png"}]}, {"Route": "Dashboard/assets/img/icons/unicons/cc-warning.png", "AssetFile": "Dashboard/assets/img/icons/unicons/cc-warning.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "689"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"5uSksHD1iBGBuMKXXgpOIOvLhnKzUAGCp2pLj4cCnOg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5uSksHD1iBGBuMKXXgpOIOvLhnKzUAGCp2pLj4cCnOg="}]}, {"Route": "Dashboard/assets/img/icons/unicons/cc-warning.yqdeq55wew.png", "AssetFile": "Dashboard/assets/img/icons/unicons/cc-warning.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "689"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"5uSksHD1iBGBuMKXXgpOIOvLhnKzUAGCp2pLj4cCnOg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yqdeq55wew"}, {"Name": "integrity", "Value": "sha256-5uSksHD1iBGBuMKXXgpOIOvLhnKzUAGCp2pLj4cCnOg="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/unicons/cc-warning.png"}]}, {"Route": "Dashboard/assets/img/icons/unicons/chart-success.9zqdfavt9a.png", "AssetFile": "Dashboard/assets/img/icons/unicons/chart-success.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1528"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"1dA1GFlgp4AwyiK2EtGiZitxoN4Up5nVjVMxdn/Fxfk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9zqdfavt9a"}, {"Name": "integrity", "Value": "sha256-1dA1GFlgp4AwyiK2EtGiZitxoN4Up5nVjVMxdn/Fxfk="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/unicons/chart-success.png"}]}, {"Route": "Dashboard/assets/img/icons/unicons/chart-success.png", "AssetFile": "Dashboard/assets/img/icons/unicons/chart-success.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1528"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"1dA1GFlgp4AwyiK2EtGiZitxoN4Up5nVjVMxdn/Fxfk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1dA1GFlgp4AwyiK2EtGiZitxoN4Up5nVjVMxdn/Fxfk="}]}, {"Route": "Dashboard/assets/img/icons/unicons/chart.j55jf7gsvq.png", "AssetFile": "Dashboard/assets/img/icons/unicons/chart.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1491"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"KXQATGROWvL9P+yaqtuEY7vfQo711XTl3mY+X1UTsYo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j55jf7gsvq"}, {"Name": "integrity", "Value": "sha256-KXQATGROWvL9P+yaqtuEY7vfQo711XTl3mY+X1UTsYo="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/unicons/chart.png"}]}, {"Route": "Dashboard/assets/img/icons/unicons/chart.png", "AssetFile": "Dashboard/assets/img/icons/unicons/chart.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1491"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"KXQATGROWvL9P+yaqtuEY7vfQo711XTl3mY+X1UTsYo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KXQATGROWvL9P+yaqtuEY7vfQo711XTl3mY+X1UTsYo="}]}, {"Route": "Dashboard/assets/img/icons/unicons/paypal.png", "AssetFile": "Dashboard/assets/img/icons/unicons/paypal.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1090"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"MmXG7F0O5+Y2+eC/my8aBVs83MestyL6XZQHgb/yzvI=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MmXG7F0O5+Y2+eC/my8aBVs83MestyL6XZQHgb/yzvI="}]}, {"Route": "Dashboard/assets/img/icons/unicons/paypal.yw16yx1qqs.png", "AssetFile": "Dashboard/assets/img/icons/unicons/paypal.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1090"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"MmXG7F0O5+Y2+eC/my8aBVs83MestyL6XZQHgb/yzvI=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yw16yx1qqs"}, {"Name": "integrity", "Value": "sha256-MmXG7F0O5+Y2+eC/my8aBVs83MestyL6XZQHgb/yzvI="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/unicons/paypal.png"}]}, {"Route": "Dashboard/assets/img/icons/unicons/wallet-info.png", "AssetFile": "Dashboard/assets/img/icons/unicons/wallet-info.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "936"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"hmsDCiMg2IYZ46fBHTBAql6505f9g0bUtvRwdnU3lGk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hmsDCiMg2IYZ46fBHTBAql6505f9g0bUtvRwdnU3lGk="}]}, {"Route": "Dashboard/assets/img/icons/unicons/wallet-info.uyaw46hsrb.png", "AssetFile": "Dashboard/assets/img/icons/unicons/wallet-info.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "936"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"hmsDCiMg2IYZ46fBHTBAql6505f9g0bUtvRwdnU3lGk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uyaw46hsrb"}, {"Name": "integrity", "Value": "sha256-hmsDCiMg2IYZ46fBHTBAql6505f9g0bUtvRwdnU3lGk="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/unicons/wallet-info.png"}]}, {"Route": "Dashboard/assets/img/icons/unicons/wallet.eik0yqeyxb.png", "AssetFile": "Dashboard/assets/img/icons/unicons/wallet.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "920"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Ki7fmsT7OVyKlrdyWPF5ao36IBgIsjPmTUQ+uhFMjuc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eik0yqeyxb"}, {"Name": "integrity", "Value": "sha256-Ki7fmsT7OVyKlrdyWPF5ao36IBgIsjPmTUQ+uhFMjuc="}, {"Name": "label", "Value": "Dashboard/assets/img/icons/unicons/wallet.png"}]}, {"Route": "Dashboard/assets/img/icons/unicons/wallet.png", "AssetFile": "Dashboard/assets/img/icons/unicons/wallet.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "920"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Ki7fmsT7OVyKlrdyWPF5ao36IBgIsjPmTUQ+uhFMjuc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ki7fmsT7OVyKlrdyWPF5ao36IBgIsjPmTUQ+uhFMjuc="}]}, {"Route": "Dashboard/assets/img/icontwitter.png", "AssetFile": "Dashboard/assets/img/icontwitter.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1834"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oFeO3c4DUPYd2M0TQzorohaZv0paDHMg5w9+wfmmWHE=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oFeO3c4DUPYd2M0TQzorohaZv0paDHMg5w9+wfmmWHE="}]}, {"Route": "Dashboard/assets/img/icontwitter.stbbn09hjr.png", "AssetFile": "Dashboard/assets/img/icontwitter.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1834"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oFeO3c4DUPYd2M0TQzorohaZv0paDHMg5w9+wfmmWHE=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "stbbn09hjr"}, {"Name": "integrity", "Value": "sha256-oFeO3c4DUPYd2M0TQzorohaZv0paDHMg5w9+wfmmWHE="}, {"Name": "label", "Value": "Dashboard/assets/img/icontwitter.png"}]}, {"Route": "Dashboard/assets/img/illustrations/girl-doing-yoga-light.png", "AssetFile": "Dashboard/assets/img/illustrations/girl-doing-yoga-light.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "219096"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"IeHIs4cgSDEbvvHmQ0Gw4pMHvi4ux5eIJE9JZ4nOrOU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IeHIs4cgSDEbvvHmQ0Gw4pMHvi4ux5eIJE9JZ4nOrOU="}]}, {"Route": "Dashboard/assets/img/illustrations/girl-doing-yoga-light.pqilfow7ks.png", "AssetFile": "Dashboard/assets/img/illustrations/girl-doing-yoga-light.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "219096"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"IeHIs4cgSDEbvvHmQ0Gw4pMHvi4ux5eIJE9JZ4nOrOU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pqilfow7ks"}, {"Name": "integrity", "Value": "sha256-IeHIs4cgSDEbvvHmQ0Gw4pMHvi4ux5eIJE9JZ4nOrOU="}, {"Name": "label", "Value": "Dashboard/assets/img/illustrations/girl-doing-yoga-light.png"}]}, {"Route": "Dashboard/assets/img/illustrations/girl-with-laptop-light.2cvzwvwj7q.png", "AssetFile": "Dashboard/assets/img/illustrations/girl-with-laptop-light.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "183173"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nrpYnyWa0DzjPEfjd+RkI9dyXy9Holiw4FjsD7dGLc0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2cvzwvwj7q"}, {"Name": "integrity", "Value": "sha256-nrpYnyWa0DzjPEfjd+RkI9dyXy9Holiw4FjsD7dGLc0="}, {"Name": "label", "Value": "Dashboard/assets/img/illustrations/girl-with-laptop-light.png"}]}, {"Route": "Dashboard/assets/img/illustrations/girl-with-laptop-light.png", "AssetFile": "Dashboard/assets/img/illustrations/girl-with-laptop-light.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "183173"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nrpYnyWa0DzjPEfjd+RkI9dyXy9Holiw4FjsD7dGLc0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nrpYnyWa0DzjPEfjd+RkI9dyXy9Holiw4FjsD7dGLc0="}]}, {"Route": "Dashboard/assets/img/illustrations/in-prograss.png", "AssetFile": "Dashboard/assets/img/illustrations/in-prograss.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "173307"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Tu1DFpVbrkuJP9LdkBgP9PPU/GkSrUnbk+aKWCngXgs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Tu1DFpVbrkuJP9LdkBgP9PPU/GkSrUnbk+aKWCngXgs="}]}, {"Route": "Dashboard/assets/img/illustrations/in-prograss.u5yfcb6n2t.png", "AssetFile": "Dashboard/assets/img/illustrations/in-prograss.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "173307"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Tu1DFpVbrkuJP9LdkBgP9PPU/GkSrUnbk+aKWCngXgs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u5yfcb6n2t"}, {"Name": "integrity", "Value": "sha256-Tu1DFpVbrkuJP9LdkBgP9PPU/GkSrUnbk+aKWCngXgs="}, {"Name": "label", "Value": "Dashboard/assets/img/illustrations/in-prograss.png"}]}, {"Route": "Dashboard/assets/img/illustrations/man-with-laptop-light.lbgi9tpxrg.png", "AssetFile": "Dashboard/assets/img/illustrations/man-with-laptop-light.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8826"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"ITDy9whlrMB5qxY945SKc+tYgjQxgFndh3dqPZzC8V0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lbgi9tpxrg"}, {"Name": "integrity", "Value": "sha256-ITDy9whlrMB5qxY945SKc+tYgjQxgFndh3dqPZzC8V0="}, {"Name": "label", "Value": "Dashboard/assets/img/illustrations/man-with-laptop-light.png"}]}, {"Route": "Dashboard/assets/img/illustrations/man-with-laptop-light.png", "AssetFile": "Dashboard/assets/img/illustrations/man-with-laptop-light.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8826"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"ITDy9whlrMB5qxY945SKc+tYgjQxgFndh3dqPZzC8V0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ITDy9whlrMB5qxY945SKc+tYgjQxgFndh3dqPZzC8V0="}]}, {"Route": "Dashboard/assets/img/illustrations/page-misc-error-light.4yk20wtg5w.png", "AssetFile": "Dashboard/assets/img/illustrations/page-misc-error-light.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "139086"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"dNmrwUBStbi15vXz0sKEf3NKeZGzrNuH0z5C0q7KeLw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4yk20wtg5w"}, {"Name": "integrity", "Value": "sha256-dNmrwUBStbi15vXz0sKEf3NKeZGzrNuH0z5C0q7KeLw="}, {"Name": "label", "Value": "Dashboard/assets/img/illustrations/page-misc-error-light.png"}]}, {"Route": "Dashboard/assets/img/illustrations/page-misc-error-light.png", "AssetFile": "Dashboard/assets/img/illustrations/page-misc-error-light.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "139086"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"dNmrwUBStbi15vXz0sKEf3NKeZGzrNuH0z5C0q7KeLw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dNmrwUBStbi15vXz0sKEf3NKeZGzrNuH0z5C0q7KeLw="}]}, {"Route": "Dashboard/assets/img/illustrations/page-misc-error-light2.hu89au1p7j.png", "AssetFile": "Dashboard/assets/img/illustrations/page-misc-error-light2.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "216684"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"CWf8MTkHsvJlZ3hLYNI0PXzOeDweP2ZTv49aGlJaIhk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hu89au1p7j"}, {"Name": "integrity", "Value": "sha256-CWf8MTkHsvJlZ3hLYNI0PXzOeDweP2ZTv49aGlJaIhk="}, {"Name": "label", "Value": "Dashboard/assets/img/illustrations/page-misc-error-light2.png"}]}, {"Route": "Dashboard/assets/img/illustrations/page-misc-error-light2.png", "AssetFile": "Dashboard/assets/img/illustrations/page-misc-error-light2.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "216684"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"CWf8MTkHsvJlZ3hLYNI0PXzOeDweP2ZTv49aGlJaIhk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CWf8MTkHsvJlZ3hLYNI0PXzOeDweP2ZTv49aGlJaIhk="}]}, {"Route": "Dashboard/assets/img/it-flag.png", "AssetFile": "Dashboard/assets/img/it-flag.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13908"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"CkRSn/BfPWJHtk62uU1VLGlTgBcBuJm9I1JdLJvaR2g=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CkRSn/BfPWJHtk62uU1VLGlTgBcBuJm9I1JdLJvaR2g="}]}, {"Route": "Dashboard/assets/img/it-flag.qssp6wqowt.png", "AssetFile": "Dashboard/assets/img/it-flag.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "13908"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"CkRSn/BfPWJHtk62uU1VLGlTgBcBuJm9I1JdLJvaR2g=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qssp6wqowt"}, {"Name": "integrity", "Value": "sha256-CkRSn/BfPWJHtk62uU1VLGlTgBcBuJm9I1JdLJvaR2g="}, {"Name": "label", "Value": "Dashboard/assets/img/it-flag.png"}]}, {"Route": "Dashboard/assets/img/language.j2dl353fui.png", "AssetFile": "Dashboard/assets/img/language.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9250"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"lcNl3e+JH+C5Ey3+BNS9kbCRVmYHFtRCqf/cje/+ZpQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j2dl353fui"}, {"Name": "integrity", "Value": "sha256-lcNl3e+JH+C5Ey3+BNS9kbCRVmYHFtRCqf/cje/+ZpQ="}, {"Name": "label", "Value": "Dashboard/assets/img/language.png"}]}, {"Route": "Dashboard/assets/img/language.png", "AssetFile": "Dashboard/assets/img/language.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9250"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"lcNl3e+JH+C5Ey3+BNS9kbCRVmYHFtRCqf/cje/+ZpQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lcNl3e+JH+C5Ey3+BNS9kbCRVmYHFtRCqf/cje/+ZpQ="}]}, {"Route": "Dashboard/assets/img/lock.png", "AssetFile": "Dashboard/assets/img/lock.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8410"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"EvuiWDbTArvfCwsh09ebJZCI38uYItaoz0rkV3X8iLY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EvuiWDbTArvfCwsh09ebJZCI38uYItaoz0rkV3X8iLY="}]}, {"Route": "Dashboard/assets/img/lock.qdws6rar45.png", "AssetFile": "Dashboard/assets/img/lock.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8410"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"EvuiWDbTArvfCwsh09ebJZCI38uYItaoz0rkV3X8iLY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qdws6rar45"}, {"Name": "integrity", "Value": "sha256-EvuiWDbTArvfCwsh09ebJZCI38uYItaoz0rkV3X8iLY="}, {"Name": "label", "Value": "Dashboard/assets/img/lock.png"}]}, {"Route": "Dashboard/assets/img/logo.95njsei89b.png", "AssetFile": "Dashboard/assets/img/logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "48171"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"E1C5YkxAxHD+s7KAP4l4wgPVe4EhOkWrhwmYfp31lRE=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "95njsei89b"}, {"Name": "integrity", "Value": "sha256-E1C5YkxAxHD+s7KAP4l4wgPVe4EhOkWrhwmYfp31lRE="}, {"Name": "label", "Value": "Dashboard/assets/img/logo.png"}]}, {"Route": "Dashboard/assets/img/logo.png", "AssetFile": "Dashboard/assets/img/logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48171"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"E1C5YkxAxHD+s7KAP4l4wgPVe4EhOkWrhwmYfp31lRE=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E1C5YkxAxHD+s7KAP4l4wgPVe4EhOkWrhwmYfp31lRE="}]}, {"Route": "Dashboard/assets/img/moving and cleaning.h26kxx62xe.png", "AssetFile": "Dashboard/assets/img/moving and cleaning.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2571"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"R9+k3y6WyZfSVpUddiYxxuFrKPW0qHtrA/KWWvuQLHA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h26kxx62xe"}, {"Name": "integrity", "Value": "sha256-R9+k3y6WyZfSVpUddiYxxuFrKPW0qHtrA/KWWvuQLHA="}, {"Name": "label", "Value": "Dashboard/assets/img/moving and cleaning.png"}]}, {"Route": "Dashboard/assets/img/moving and cleaning.png", "AssetFile": "Dashboard/assets/img/moving and cleaning.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2571"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"R9+k3y6WyZfSVpUddiYxxuFrKPW0qHtrA/KWWvuQLHA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R9+k3y6WyZfSVpUddiYxxuFrKPW0qHtrA/KWWvuQLHA="}]}, {"Route": "Dashboard/assets/img/moving.08zxpr0k6o.png", "AssetFile": "Dashboard/assets/img/moving.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2074"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"cMrkb+qVwGbDhAEsk4jQ+QwjJP3kglgmXwa1RHnYHM0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "08zxpr0k6o"}, {"Name": "integrity", "Value": "sha256-cMrkb+qVwGbDhAEsk4jQ+QwjJP3kglgmXwa1RHnYHM0="}, {"Name": "label", "Value": "Dashboard/assets/img/moving.png"}]}, {"Route": "Dashboard/assets/img/moving.png", "AssetFile": "Dashboard/assets/img/moving.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2074"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"cMrkb+qVwGbDhAEsk4jQ+QwjJP3kglgmXwa1RHnYHM0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cMrkb+qVwGbDhAEsk4jQ+QwjJP3kglgmXwa1RHnYHM0="}]}, {"Route": "Dashboard/assets/img/painting.4nmlm6ldy8.png", "AssetFile": "Dashboard/assets/img/painting.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1571"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"IFzlHkoMHSnToaEqbRVLW72gW3Nxl/sbu2lvTMfjTFs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4nmlm6ldy8"}, {"Name": "integrity", "Value": "sha256-IFzlHkoMHSnToaEqbRVLW72gW3Nxl/sbu2lvTMfjTFs="}, {"Name": "label", "Value": "Dashboard/assets/img/painting.png"}]}, {"Route": "Dashboard/assets/img/painting.png", "AssetFile": "Dashboard/assets/img/painting.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1571"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"IFzlHkoMHSnToaEqbRVLW72gW3Nxl/sbu2lvTMfjTFs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IFzlHkoMHSnToaEqbRVLW72gW3Nxl/sbu2lvTMfjTFs="}]}, {"Route": "Dashboard/assets/img/pizaa.png", "AssetFile": "Dashboard/assets/img/pizaa.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "49796"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"obyR/G5Lx8H0lsDbHOhl0gVCl6Qu+v4MXzSWeyFvzCo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-obyR/G5Lx8H0lsDbHOhl0gVCl6Qu+v4MXzSWeyFvzCo="}]}, {"Route": "Dashboard/assets/img/pizaa.r9shujdryk.png", "AssetFile": "Dashboard/assets/img/pizaa.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "49796"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"obyR/G5Lx8H0lsDbHOhl0gVCl6Qu+v4MXzSWeyFvzCo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r9shujdryk"}, {"Name": "integrity", "Value": "sha256-obyR/G5Lx8H0lsDbHOhl0gVCl6Qu+v4MXzSWeyFvzCo="}, {"Name": "label", "Value": "Dashboard/assets/img/pizaa.png"}]}, {"Route": "Dashboard/assets/img/thank-you.22o4zjfsw6.png", "AssetFile": "Dashboard/assets/img/thank-you.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "968719"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"No6YrlyHLTe5qjcM8QFjbJ9Z2idCdUPrbsB5ArZmL/I=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "22o4zjfsw6"}, {"Name": "integrity", "Value": "sha256-No6YrlyHLTe5qjcM8QFjbJ9Z2idCdUPrbsB5ArZmL/I="}, {"Name": "label", "Value": "Dashboard/assets/img/thank-you.png"}]}, {"Route": "Dashboard/assets/img/thank-you.png", "AssetFile": "Dashboard/assets/img/thank-you.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "968719"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"No6YrlyHLTe5qjcM8QFjbJ9Z2idCdUPrbsB5ArZmL/I=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-No6YrlyHLTe5qjcM8QFjbJ9Z2idCdUPrbsB5ArZmL/I="}]}, {"Route": "Dashboard/assets/img/thankyou.06v2xtonbi.png", "AssetFile": "Dashboard/assets/img/thankyou.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "75950"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"SJ/4wZakyhUkuptLDZRktXrfle14iJO7dfM2cF67FLc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "06v2xtonbi"}, {"Name": "integrity", "Value": "sha256-SJ/4wZakyhUkuptLDZRktXrfle14iJO7dfM2cF67FLc="}, {"Name": "label", "Value": "Dashboard/assets/img/thankyou.png"}]}, {"Route": "Dashboard/assets/img/thankyou.png", "AssetFile": "Dashboard/assets/img/thankyou.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "75950"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"SJ/4wZakyhUkuptLDZRktXrfle14iJO7dfM2cF67FLc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SJ/4wZakyhUkuptLDZRktXrfle14iJO7dfM2cF67FLc="}]}, {"Route": "Dashboard/assets/img/us-flag.6rnqyqozqm.png", "AssetFile": "Dashboard/assets/img/us-flag.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24631"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"wkRNKPLm/0134O8A2OXzaNfAFIDTxFK+eSQCEPhPsB4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6rnqyqozqm"}, {"Name": "integrity", "Value": "sha256-wkRNKPLm/0134O8A2OXzaNfAFIDTxFK+eSQCEPhPsB4="}, {"Name": "label", "Value": "Dashboard/assets/img/us-flag.png"}]}, {"Route": "Dashboard/assets/img/us-flag.png", "AssetFile": "Dashboard/assets/img/us-flag.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24631"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"wkRNKPLm/0134O8A2OXzaNfAFIDTxFK+eSQCEPhPsB4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wkRNKPLm/0134O8A2OXzaNfAFIDTxFK+eSQCEPhPsB4="}]}, {"Route": "Dashboard/assets/js/config.jrtn85pb04.js", "AssetFile": "Dashboard/assets/js/config.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "753"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"b4I4UbhUid0FGfY/FZMhucoNeiPUZiuyX51y/wZps6U=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jrtn85pb04"}, {"Name": "integrity", "Value": "sha256-b4I4UbhUid0FGfY/FZMhucoNeiPUZiuyX51y/wZps6U="}, {"Name": "label", "Value": "Dashboard/assets/js/config.js"}]}, {"Route": "Dashboard/assets/js/config.js", "AssetFile": "Dashboard/assets/js/config.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "753"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"b4I4UbhUid0FGfY/FZMhucoNeiPUZiuyX51y/wZps6U=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b4I4UbhUid0FGfY/FZMhucoNeiPUZiuyX51y/wZps6U="}]}, {"Route": "Dashboard/assets/js/main.ey5eontg2m.js", "AssetFile": "Dashboard/assets/js/main.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3789"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"prl4KIIKB0JqbbXfjcgLFh/3EmuKhR5RvfUJctuqYc0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ey5eontg2m"}, {"Name": "integrity", "Value": "sha256-prl4KIIKB0JqbbXfjcgLFh/3EmuKhR5RvfUJctuqYc0="}, {"Name": "label", "Value": "Dashboard/assets/js/main.js"}]}, {"Route": "Dashboard/assets/js/main.js", "AssetFile": "Dashboard/assets/js/main.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3789"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"prl4KIIKB0JqbbXfjcgLFh/3EmuKhR5RvfUJctuqYc0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-prl4KIIKB0JqbbXfjcgLFh/3EmuKhR5RvfUJctuqYc0="}]}, {"Route": "Dashboard/assets/vendor/css/core.css", "AssetFile": "Dashboard/assets/vendor/css/core.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "986733"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"21uvDVBawlNTpG+D5VMfC1bb/9QhPhnjE1ACKek8FMs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-21uvDVBawlNTpG+D5VMfC1bb/9QhPhnjE1ACKek8FMs="}]}, {"Route": "Dashboard/assets/vendor/css/core.z666vu0l64.css", "AssetFile": "Dashboard/assets/vendor/css/core.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "986733"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"21uvDVBawlNTpG+D5VMfC1bb/9QhPhnjE1ACKek8FMs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z666vu0l64"}, {"Name": "integrity", "Value": "sha256-21uvDVBawlNTpG+D5VMfC1bb/9QhPhnjE1ACKek8FMs="}, {"Name": "label", "Value": "Dashboard/assets/vendor/css/core.css"}]}, {"Route": "Dashboard/assets/vendor/css/page-auth.css", "AssetFile": "Dashboard/assets/vendor/css/page-auth.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16195"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MpcNIJxfGjwnslj6av+r2qwj/dzHMiSkOfxbc5VHFOs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MpcNIJxfGjwnslj6av+r2qwj/dzHMiSkOfxbc5VHFOs="}]}, {"Route": "Dashboard/assets/vendor/css/page-auth.mnmed00ooq.css", "AssetFile": "Dashboard/assets/vendor/css/page-auth.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16195"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MpcNIJxfGjwnslj6av+r2qwj/dzHMiSkOfxbc5VHFOs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mnmed00ooq"}, {"Name": "integrity", "Value": "sha256-MpcNIJxfGjwnslj6av+r2qwj/dzHMiSkOfxbc5VHFOs="}, {"Name": "label", "Value": "Dashboard/assets/vendor/css/page-auth.css"}]}, {"Route": "Dashboard/assets/vendor/css/theme-default.css", "AssetFile": "Dashboard/assets/vendor/css/theme-default.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "74010"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fTJ53gfOzMoueTyi96LoXEk6yAfFODjoHe79ydgHOVg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fTJ53gfOzMoueTyi96LoXEk6yAfFODjoHe79ydgHOVg="}]}, {"Route": "Dashboard/assets/vendor/css/theme-default.dk2ytlcajt.css", "AssetFile": "Dashboard/assets/vendor/css/theme-default.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "74010"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fTJ53gfOzMoueTyi96LoXEk6yAfFODjoHe79ydgHOVg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dk2ytlcajt"}, {"Name": "integrity", "Value": "sha256-fTJ53gfOzMoueTyi96LoXEk6yAfFODjoHe79ydgHOVg="}, {"Name": "label", "Value": "Dashboard/assets/vendor/css/theme-default.css"}]}, {"Route": "Dashboard/assets/vendor/fonts/boxicons.4hzulchcxt.css", "AssetFile": "Dashboard/assets/vendor/fonts/boxicons.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "79999"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"6PtP/PAu3KHfRD5F/OZci1sJmwuz4PclKdyVpD+ryrU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4hzulchcxt"}, {"Name": "integrity", "Value": "sha256-6PtP/PAu3KHfRD5F/OZci1sJmwuz4PclKdyVpD+ryrU="}, {"Name": "label", "Value": "Dashboard/assets/vendor/fonts/boxicons.css"}]}, {"Route": "Dashboard/assets/vendor/fonts/boxicons.css", "AssetFile": "Dashboard/assets/vendor/fonts/boxicons.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "79999"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"6PtP/PAu3KHfRD5F/OZci1sJmwuz4PclKdyVpD+ryrU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6PtP/PAu3KHfRD5F/OZci1sJmwuz4PclKdyVpD+ryrU="}]}, {"Route": "Dashboard/assets/vendor/fonts/boxicons/boxicons.1oivsu8mcf.woff", "AssetFile": "Dashboard/assets/vendor/fonts/boxicons/boxicons.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "292480"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"QUTt2cNsIJkbGnEWpEpwnZlrpeMI1ScfSQeTJdFKv7Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1oivsu8mcf"}, {"Name": "integrity", "Value": "sha256-QUTt2cNsIJkbGnEWpEpwnZlrpeMI1ScfSQeTJdFKv7Q="}, {"Name": "label", "Value": "Dashboard/assets/vendor/fonts/boxicons/boxicons.woff"}]}, {"Route": "Dashboard/assets/vendor/fonts/boxicons/boxicons.5j3plm5gu1.woff2", "AssetFile": "Dashboard/assets/vendor/fonts/boxicons/boxicons.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "102988"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"34RYJip9PU2tKFFlXeKzujtxH1Liv85jzgNIcwu4Gcc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5j3plm5gu1"}, {"Name": "integrity", "Value": "sha256-34RYJip9PU2tKFFlXeKzujtxH1Liv85jzgNIcwu4Gcc="}, {"Name": "label", "Value": "Dashboard/assets/vendor/fonts/boxicons/boxicons.woff2"}]}, {"Route": "Dashboard/assets/vendor/fonts/boxicons/boxicons.eot", "AssetFile": "Dashboard/assets/vendor/fonts/boxicons/boxicons.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "292572"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"Bx0iFactVs1XqMnGSXNgQHykpAgae6y4BaEOGtip7PU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Bx0iFactVs1XqMnGSXNgQHykpAgae6y4BaEOGtip7PU="}]}, {"Route": "Dashboard/assets/vendor/fonts/boxicons/boxicons.q9t8biukf5.ttf", "AssetFile": "Dashboard/assets/vendor/fonts/boxicons/boxicons.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "292404"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"Lpkz1ATMc/40f+ahhgq7eGVTP83nN2WgwqKSph5k2wo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9t8biukf5"}, {"Name": "integrity", "Value": "sha256-Lpkz1ATMc/40f+ahhgq7eGVTP83nN2WgwqKSph5k2wo="}, {"Name": "label", "Value": "Dashboard/assets/vendor/fonts/boxicons/boxicons.ttf"}]}, {"Route": "Dashboard/assets/vendor/fonts/boxicons/boxicons.rri4hp3ggs.eot", "AssetFile": "Dashboard/assets/vendor/fonts/boxicons/boxicons.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "292572"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"Bx0iFactVs1XqMnGSXNgQHykpAgae6y4BaEOGtip7PU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rri4hp3ggs"}, {"Name": "integrity", "Value": "sha256-Bx0iFactVs1XqMnGSXNgQHykpAgae6y4BaEOGtip7PU="}, {"Name": "label", "Value": "Dashboard/assets/vendor/fonts/boxicons/boxicons.eot"}]}, {"Route": "Dashboard/assets/vendor/fonts/boxicons/boxicons.svg", "AssetFile": "Dashboard/assets/vendor/fonts/boxicons/boxicons.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1125137"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"+VSgQwtBSscYXjLRa66DCDyS2wF0SeFmwY8rnLntP1U=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+VSgQwtBSscYXjLRa66DCDyS2wF0SeFmwY8rnLntP1U="}]}, {"Route": "Dashboard/assets/vendor/fonts/boxicons/boxicons.ttf", "AssetFile": "Dashboard/assets/vendor/fonts/boxicons/boxicons.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "292404"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"Lpkz1ATMc/40f+ahhgq7eGVTP83nN2WgwqKSph5k2wo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Lpkz1ATMc/40f+ahhgq7eGVTP83nN2WgwqKSph5k2wo="}]}, {"Route": "Dashboard/assets/vendor/fonts/boxicons/boxicons.woff", "AssetFile": "Dashboard/assets/vendor/fonts/boxicons/boxicons.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "292480"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"QUTt2cNsIJkbGnEWpEpwnZlrpeMI1ScfSQeTJdFKv7Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QUTt2cNsIJkbGnEWpEpwnZlrpeMI1ScfSQeTJdFKv7Q="}]}, {"Route": "Dashboard/assets/vendor/fonts/boxicons/boxicons.woff2", "AssetFile": "Dashboard/assets/vendor/fonts/boxicons/boxicons.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "102988"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"34RYJip9PU2tKFFlXeKzujtxH1Liv85jzgNIcwu4Gcc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-34RYJip9PU2tKFFlXeKzujtxH1Liv85jzgNIcwu4Gcc="}]}, {"Route": "Dashboard/assets/vendor/fonts/boxicons/boxicons.x6kftl55k2.svg", "AssetFile": "Dashboard/assets/vendor/fonts/boxicons/boxicons.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1125137"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"+VSgQwtBSscYXjLRa66DCDyS2wF0SeFmwY8rnLntP1U=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x6kftl55k2"}, {"Name": "integrity", "Value": "sha256-+VSgQwtBSscYXjLRa66DCDyS2wF0SeFmwY8rnLntP1U="}, {"Name": "label", "Value": "Dashboard/assets/vendor/fonts/boxicons/boxicons.svg"}]}, {"Route": "Dashboard/assets/vendor/jquery/jquery.js", "AssetFile": "Dashboard/assets/vendor/jquery/jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "832977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cb/bjNYToPeDvDNynkjSvinOLAoB12Vc6Th+Kr6HkYM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cb/bjNYToPeDvDNynkjSvinOLAoB12Vc6Th+Kr6HkYM="}]}, {"Route": "Dashboard/assets/vendor/jquery/jquery.v0zti25kgd.js", "AssetFile": "Dashboard/assets/vendor/jquery/jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "832977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cb/bjNYToPeDvDNynkjSvinOLAoB12Vc6Th+Kr6HkYM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zti25kgd"}, {"Name": "integrity", "Value": "sha256-cb/bjNYToPeDvDNynkjSvinOLAoB12Vc6Th+Kr6HkYM="}, {"Name": "label", "Value": "Dashboard/assets/vendor/jquery/jquery.js"}]}, {"Route": "Dashboard/assets/vendor/js/bootstrap.gv7dia6x1y.js", "AssetFile": "Dashboard/assets/vendor/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "114929"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5Dx1Vix9ycXK46KMgwa4gGIQxADuq1GQf6iW9W6GIjg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gv7dia6x1y"}, {"Name": "integrity", "Value": "sha256-5Dx1Vix9ycXK46KMgwa4gGIQxADuq1GQf6iW9W6GIjg="}, {"Name": "label", "Value": "Dashboard/assets/vendor/js/bootstrap.js"}]}, {"Route": "Dashboard/assets/vendor/js/bootstrap.js", "AssetFile": "Dashboard/assets/vendor/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "114929"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5Dx1Vix9ycXK46KMgwa4gGIQxADuq1GQf6iW9W6GIjg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5Dx1Vix9ycXK46KMgwa4gGIQxADuq1GQf6iW9W6GIjg="}]}, {"Route": "Dashboard/assets/vendor/js/helpers.js", "AssetFile": "Dashboard/assets/vendor/js/helpers.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "103079"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HXrFFoLZabWsBv3KEyINijNnu/c4dtre5twTWNMN72E=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HXrFFoLZabWsBv3KEyINijNnu/c4dtre5twTWNMN72E="}]}, {"Route": "Dashboard/assets/vendor/js/helpers.nnhel8mq3c.js", "AssetFile": "Dashboard/assets/vendor/js/helpers.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "103079"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HXrFFoLZabWsBv3KEyINijNnu/c4dtre5twTWNMN72E=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nnhel8mq3c"}, {"Name": "integrity", "Value": "sha256-HXrFFoLZabWsBv3KEyINijNnu/c4dtre5twTWNMN72E="}, {"Name": "label", "Value": "Dashboard/assets/vendor/js/helpers.js"}]}, {"Route": "Dashboard/assets/vendor/js/menu.9h18ncb23z.js", "AssetFile": "Dashboard/assets/vendor/js/menu.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "80183"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g++rCLb8zL7EX8p2ZBiNPm/M+TlZA75BaVzDqpvDIVM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9h18ncb23z"}, {"Name": "integrity", "Value": "sha256-g++rCLb8zL7EX8p2ZBiNPm/M+TlZA75BaVzDqpvDIVM="}, {"Name": "label", "Value": "Dashboard/assets/vendor/js/menu.js"}]}, {"Route": "Dashboard/assets/vendor/js/menu.js", "AssetFile": "Dashboard/assets/vendor/js/menu.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "80183"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g++rCLb8zL7EX8p2ZBiNPm/M+TlZA75BaVzDqpvDIVM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g++rCLb8zL7EX8p2ZBiNPm/M+TlZA75BaVzDqpvDIVM="}]}, {"Route": "Dashboard/assets/vendor/js/template-customizer.3r8b2a0bt1.js", "AssetFile": "Dashboard/assets/vendor/js/template-customizer.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3r8b2a0bt1"}, {"Name": "integrity", "Value": "sha256-8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U="}, {"Name": "label", "Value": "Dashboard/assets/vendor/js/template-customizer.js"}]}, {"Route": "Dashboard/assets/vendor/js/template-customizer.js", "AssetFile": "Dashboard/assets/vendor/js/template-customizer.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U="}]}, {"Route": "Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.css", "AssetFile": "Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5008"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Jti2qrM0g65NYKkwOIi8T09Zmf0GukGJuQknZS4L/Iw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jti2qrM0g65NYKkwOIi8T09Zmf0GukGJuQknZS4L/Iw="}]}, {"Route": "Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.js", "AssetFile": "Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "108747"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8mDv4eiuJCHTCzjnz2G/MxNQyE0UhRporarxN2CEvS0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8mDv4eiuJCHTCzjnz2G/MxNQyE0UhRporarxN2CEvS0="}]}, {"Route": "Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.m8j3cetw7e.css", "AssetFile": "Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5008"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Jti2qrM0g65NYKkwOIi8T09Zmf0GukGJuQknZS4L/Iw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m8j3cetw7e"}, {"Name": "integrity", "Value": "sha256-Jti2qrM0g65NYKkwOIi8T09Zmf0GukGJuQknZS4L/Iw="}, {"Name": "label", "Value": "Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.css"}]}, {"Route": "Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.mibndavuub.js", "AssetFile": "Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "108747"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8mDv4eiuJCHTCzjnz2G/MxNQyE0UhRporarxN2CEvS0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-8mDv4eiuJCHTCzjnz2G/MxNQyE0UhRporarxN2CEvS0="}, {"Name": "label", "Value": "Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.js"}]}, {"Route": "Dashboard/assets/vendor/popper/popper.js", "AssetFile": "Dashboard/assets/vendor/popper/popper.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "57748"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"R2f8gp75bjn7zoUXi9LxF4C4/zrBY8MFzpR3h38Fenk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R2f8gp75bjn7zoUXi9LxF4C4/zrBY8MFzpR3h38Fenk="}]}, {"Route": "Dashboard/assets/vendor/popper/popper.lln95dcem0.js", "AssetFile": "Dashboard/assets/vendor/popper/popper.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "57748"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"R2f8gp75bjn7zoUXi9LxF4C4/zrBY8MFzpR3h38Fenk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lln95dcem0"}, {"Name": "integrity", "Value": "sha256-R2f8gp75bjn7zoUXi9LxF4C4/zrBY8MFzpR3h38Fenk="}, {"Name": "label", "Value": "Dashboard/assets/vendor/popper/popper.js"}]}, {"Route": "Identity/css/site.css", "AssetFile": "Identity/css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1378"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"TjhuPxIaovHDthInF3L1YN67qxqghzNA1py01oh2FJA=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TjhuPxIaovHDthInF3L1YN67qxqghzNA1py01oh2FJA="}]}, {"Route": "Identity/favicon.ico", "AssetFile": "Identity/favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "32038"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0="}]}, {"Route": "Identity/js/site.js", "AssetFile": "Identity/js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "230"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4q1jwFhaPaZgr8WAUSrux6hAuh0XDg9kPS3xIVq36I0=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4q1jwFhaPaZgr8WAUSrux6hAuh0XDg9kPS3xIVq36I0="}]}, {"Route": "Identity/lib/bootstrap/LICENSE", "AssetFile": "Identity/lib/bootstrap/LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70538"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "196535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51319"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "117439"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70612"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "196539"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "117516"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5850"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "105138"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4646"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "35330"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5827"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "105151"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4718"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "41570"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "71584"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "192271"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53479"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "111875"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "71451"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "192214"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53407"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "111710"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "204136"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "536547"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "203803"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "536461"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162825"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "661035"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "208492"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "425643"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "78468"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "327261"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "139019"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "288320"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "72016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "222508"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "148168"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "289522"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "59511"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "217145"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88="}]}, {"Route": "Identity/lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "Identity/lib/jquery-validation-unobtrusive/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM="}]}, {"Route": "Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetFile": "Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19366"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk="}]}, {"Route": "Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetFile": "Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5867"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4xJOkIkaeBplN5tlcvxnCbBz34o5/kzpXcpjNkonVos=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4xJOkIkaeBplN5tlcvxnCbBz34o5/kzpXcpjNkonVos="}]}, {"Route": "Identity/lib/jquery-validation/LICENSE.md", "AssetFile": "Identity/lib/jquery-validation/LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "Identity/lib/jquery-validation/dist/additional-methods.js", "AssetFile": "Identity/lib/jquery-validation/dist/additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "52977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E="}]}, {"Route": "Identity/lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "Identity/lib/jquery-validation/dist/additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc="}]}, {"Route": "Identity/lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "Identity/lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}]}, {"Route": "Identity/lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "Identity/lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}]}, {"Route": "Identity/lib/jquery/LICENSE.txt", "AssetFile": "Identity/lib/jquery/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1641"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM="}]}, {"Route": "Identity/lib/jquery/dist/jquery.js", "AssetFile": "Identity/lib/jquery/dist/jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "287630"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QWo7LDvxbWT2tbbQ97B53yJnYU3WhH/C8ycbRAkjPDc=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QWo7LDvxbWT2tbbQ97B53yJnYU3WhH/C8ycbRAkjPDc="}]}, {"Route": "Identity/lib/jquery/dist/jquery.min.js", "AssetFile": "Identity/lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "89476"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0="}]}, {"Route": "Identity/lib/jquery/dist/jquery.min.map", "AssetFile": "Identity/lib/jquery/dist/jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "137974"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PVB97BePQ5JED6qSU7EvSun5iseF5FinCKtqsv5X9uA=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jul 2024 17:47:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PVB97BePQ5JED6qSU7EvSun5iseF5FinCKtqsv5X9uA="}]}, {"Route": "TaskDotNet.styles.css", "AssetFile": "TaskDotNet.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1127"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GyGE2nXm6Pk2GkV+VkYdV1LNQg24qoOovxuzLSHz6O8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:04 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GyGE2nXm6Pk2GkV+VkYdV1LNQg24qoOovxuzLSHz6O8="}]}, {"Route": "TaskDotNet.za1yuy05ie.styles.css", "AssetFile": "TaskDotNet.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1127"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GyGE2nXm6Pk2GkV+VkYdV1LNQg24qoOovxuzLSHz6O8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:04 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "za1yuy05ie"}, {"Name": "integrity", "Value": "sha256-GyGE2nXm6Pk2GkV+VkYdV1LNQg24qoOovxuzLSHz6O8="}, {"Name": "label", "Value": "TaskDotNet.styles.css"}]}, {"Route": "Templates/AGB.j4m08jd11l.pdf", "AssetFile": "Templates/AGB.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "87467"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"A7Tumj1R+JdtOAwm84k/hod9Wvpf8y7WwuEAb7Js1F4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j4m08jd11l"}, {"Name": "integrity", "Value": "sha256-A7Tumj1R+JdtOAwm84k/hod9Wvpf8y7WwuEAb7Js1F4="}, {"Name": "label", "Value": "Templates/AGB.pdf"}]}, {"Route": "Templates/AGB.pdf", "AssetFile": "Templates/AGB.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "87467"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"A7Tumj1R+JdtOAwm84k/hod9Wvpf8y7WwuEAb7Js1F4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-A7Tumj1R+JdtOAwm84k/hod9Wvpf8y7WwuEAb7Js1F4="}]}, {"Route": "Templates/ActivateEmail.html", "AssetFile": "Templates/ActivateEmail.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1657"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"so98aLKqKnptmAhoPbgYBbmi+M9SFzNaRC9dKgaUOS4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-so98aLKqKnptmAhoPbgYBbmi+M9SFzNaRC9dKgaUOS4="}]}, {"Route": "Templates/ActivateEmail.udop0am2c7.html", "AssetFile": "Templates/ActivateEmail.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1657"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"so98aLKqKnptmAhoPbgYBbmi+M9SFzNaRC9dKgaUOS4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "udop0am2c7"}, {"Name": "integrity", "Value": "sha256-so98aLKqKnptmAhoPbgYBbmi+M9SFzNaRC9dKgaUOS4="}, {"Name": "label", "Value": "Templates/ActivateEmail.html"}]}, {"Route": "Templates/AdminMessgAfterRegister.html", "AssetFile": "Templates/AdminMessgAfterRegister.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2895"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"mR2QAn+33Qwvt2MyO0eIQ4PfCbU8bPjhJuDdBaRGhQA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mR2QAn+33Qwvt2MyO0eIQ4PfCbU8bPjhJuDdBaRGhQA="}]}, {"Route": "Templates/AdminMessgAfterRegister.lhamn20ixv.html", "AssetFile": "Templates/AdminMessgAfterRegister.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2895"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"mR2QAn+33Qwvt2MyO0eIQ4PfCbU8bPjhJuDdBaRGhQA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lhamn20ixv"}, {"Name": "integrity", "Value": "sha256-mR2QAn+33Qwvt2MyO0eIQ4PfCbU8bPjhJuDdBaRGhQA="}, {"Name": "label", "Value": "Templates/AdminMessgAfterRegister.html"}]}, {"Route": "Templates/OTP-de.html", "AssetFile": "Templates/OTP-de.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3467"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Z4rM5p8OQI96kv4iVWP4IEWhvPnaFCFC7QgupkJJdu0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Z4rM5p8OQI96kv4iVWP4IEWhvPnaFCFC7QgupkJJdu0="}]}, {"Route": "Templates/OTP-de.jtsyozonns.html", "AssetFile": "Templates/OTP-de.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3467"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Z4rM5p8OQI96kv4iVWP4IEWhvPnaFCFC7QgupkJJdu0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jtsyozonns"}, {"Name": "integrity", "Value": "sha256-Z4rM5p8OQI96kv4iVWP4IEWhvPnaFCFC7QgupkJJdu0="}, {"Name": "label", "Value": "Templates/OTP-de.html"}]}, {"Route": "Templates/OTP-en.html", "AssetFile": "Templates/OTP-en.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3439"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ly6d2QJbkWDhZc7PKFNcL1+tcMBEfTJUYJryuSgOy/0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ly6d2QJbkWDhZc7PKFNcL1+tcMBEfTJUYJryuSgOy/0="}]}, {"Route": "Templates/OTP-en.t3if8ouq9v.html", "AssetFile": "Templates/OTP-en.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3439"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ly6d2QJbkWDhZc7PKFNcL1+tcMBEfTJUYJryuSgOy/0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t3if8ouq9v"}, {"Name": "integrity", "Value": "sha256-ly6d2QJbkWDhZc7PKFNcL1+tcMBEfTJUYJryuSgOy/0="}, {"Name": "label", "Value": "Templates/OTP-en.html"}]}, {"Route": "Templates/OTP-fr.auiifrug0u.html", "AssetFile": "Templates/OTP-fr.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3458"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"HjWT89RwQPSnMCsO7Pig99y/e9h9nRhiWJfYjj6zjKk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "auiifrug0u"}, {"Name": "integrity", "Value": "sha256-HjWT89RwQPSnMCsO7Pig99y/e9h9nRhiWJfYjj6zjKk="}, {"Name": "label", "Value": "Templates/OTP-fr.html"}]}, {"Route": "Templates/OTP-fr.html", "AssetFile": "Templates/OTP-fr.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3458"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"HjWT89RwQPSnMCsO7Pig99y/e9h9nRhiWJfYjj6zjKk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HjWT89RwQPSnMCsO7Pig99y/e9h9nRhiWJfYjj6zjKk="}]}, {"Route": "Templates/OTP-it.html", "AssetFile": "Templates/OTP-it.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3452"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"C+bxxAo9GFO1dtSn6NHFqJjqFs7oFLhyEllsWiZ5/u4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C+bxxAo9GFO1dtSn6NHFqJjqFs7oFLhyEllsWiZ5/u4="}]}, {"Route": "Templates/OTP-it.teadc6g5tj.html", "AssetFile": "Templates/OTP-it.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3452"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"C+bxxAo9GFO1dtSn6NHFqJjqFs7oFLhyEllsWiZ5/u4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "teadc6g5tj"}, {"Name": "integrity", "Value": "sha256-C+bxxAo9GFO1dtSn6NHFqJjqFs7oFLhyEllsWiZ5/u4="}, {"Name": "label", "Value": "Templates/OTP-it.html"}]}, {"Route": "Templates/PaintingEmail.html", "AssetFile": "Templates/PaintingEmail.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9448"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"yuhnMbRxIWeYNqDsnJr2e8ygZHEgbF2xjlg8p1FHbcs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yuhnMbRxIWeYNqDsnJr2e8ygZHEgbF2xjlg8p1FHbcs="}]}, {"Route": "Templates/PaintingEmail.msrqokysyy.html", "AssetFile": "Templates/PaintingEmail.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9448"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"yuhnMbRxIWeYNqDsnJr2e8ygZHEgbF2xjlg8p1FHbcs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "msrqokysyy"}, {"Name": "integrity", "Value": "sha256-yuhnMbRxIWeYNqDsnJr2e8ygZHEgbF2xjlg8p1FHbcs="}, {"Name": "label", "Value": "Templates/PaintingEmail.html"}]}, {"Route": "Templates/ResetPassword-de.hdbkv46alh.html", "AssetFile": "Templates/ResetPassword-de.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2596"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"lWV1M7p4FWgs/XrLXNTvJuq24AXAzziRtEluW+eaLXA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hdbkv46alh"}, {"Name": "integrity", "Value": "sha256-lWV1M7p4FWgs/XrLXNTvJuq24AXAzziRtEluW+eaLXA="}, {"Name": "label", "Value": "Templates/ResetPassword-de.html"}]}, {"Route": "Templates/ResetPassword-de.html", "AssetFile": "Templates/ResetPassword-de.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2596"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"lWV1M7p4FWgs/XrLXNTvJuq24AXAzziRtEluW+eaLXA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lWV1M7p4FWgs/XrLXNTvJuq24AXAzziRtEluW+eaLXA="}]}, {"Route": "Templates/ResetPassword-en.bxwiwpvlxj.html", "AssetFile": "Templates/ResetPassword-en.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2514"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"L8vWwB7n8YFoS4m4nAftVPSqNx3AStDi1iMOo83/ZQY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bxwiwpvlxj"}, {"Name": "integrity", "Value": "sha256-L8vWwB7n8YFoS4m4nAftVPSqNx3AStDi1iMOo83/ZQY="}, {"Name": "label", "Value": "Templates/ResetPassword-en.html"}]}, {"Route": "Templates/ResetPassword-en.html", "AssetFile": "Templates/ResetPassword-en.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2514"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"L8vWwB7n8YFoS4m4nAftVPSqNx3AStDi1iMOo83/ZQY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L8vWwB7n8YFoS4m4nAftVPSqNx3AStDi1iMOo83/ZQY="}]}, {"Route": "Templates/ResetPassword-fr.html", "AssetFile": "Templates/ResetPassword-fr.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2612"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"p8js5BUx7dC11HnExmgcxKeVBpWqAr/vTICkFAQDghQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p8js5BUx7dC11HnExmgcxKeVBpWqAr/vTICkFAQDghQ="}]}, {"Route": "Templates/ResetPassword-fr.pwodc3864l.html", "AssetFile": "Templates/ResetPassword-fr.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2612"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"p8js5BUx7dC11HnExmgcxKeVBpWqAr/vTICkFAQDghQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pwodc3864l"}, {"Name": "integrity", "Value": "sha256-p8js5BUx7dC11HnExmgcxKeVBpWqAr/vTICkFAQDghQ="}, {"Name": "label", "Value": "Templates/ResetPassword-fr.html"}]}, {"Route": "Templates/ResetPassword-it.601gadu4t9.html", "AssetFile": "Templates/ResetPassword-it.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2565"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"6hgckUQy1p2zE1IY1/yQVBPHoacOCNvbv5f331HZXWg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "601gadu4t9"}, {"Name": "integrity", "Value": "sha256-6hgckUQy1p2zE1IY1/yQVBPHoacOCNvbv5f331HZXWg="}, {"Name": "label", "Value": "Templates/ResetPassword-it.html"}]}, {"Route": "Templates/ResetPassword-it.html", "AssetFile": "Templates/ResetPassword-it.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2565"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"6hgckUQy1p2zE1IY1/yQVBPHoacOCNvbv5f331HZXWg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6hgckUQy1p2zE1IY1/yQVBPHoacOCNvbv5f331HZXWg="}]}, {"Route": "Templates/ThankYou-de.html", "AssetFile": "Templates/ThankYou-de.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2337"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"zFFsAZY/Gf0Ac3TtrLt6u/s2OBVjTXdxjKGYl+XD4bA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zFFsAZY/Gf0Ac3TtrLt6u/s2OBVjTXdxjKGYl+XD4bA="}]}, {"Route": "Templates/ThankYou-de.o58e1cibh7.html", "AssetFile": "Templates/ThankYou-de.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2337"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"zFFsAZY/Gf0Ac3TtrLt6u/s2OBVjTXdxjKGYl+XD4bA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o58e1cibh7"}, {"Name": "integrity", "Value": "sha256-zFFsAZY/Gf0Ac3TtrLt6u/s2OBVjTXdxjKGYl+XD4bA="}, {"Name": "label", "Value": "Templates/ThankYou-de.html"}]}, {"Route": "Templates/ThankYou-en.hemmnn75si.html", "AssetFile": "Templates/ThankYou-en.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2303"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"FwIJV5Ps3z2Kncz7jEnzjuvo+65nd+5g7k2/9BvgeaM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hemmnn75si"}, {"Name": "integrity", "Value": "sha256-FwIJV5Ps3z2Kncz7jEnzjuvo+65nd+5g7k2/9BvgeaM="}, {"Name": "label", "Value": "Templates/ThankYou-en.html"}]}, {"Route": "Templates/ThankYou-en.html", "AssetFile": "Templates/ThankYou-en.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2303"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"FwIJV5Ps3z2Kncz7jEnzjuvo+65nd+5g7k2/9BvgeaM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FwIJV5Ps3z2Kncz7jEnzjuvo+65nd+5g7k2/9BvgeaM="}]}, {"Route": "Templates/ThankYou-fr.6nh9u6qvzv.html", "AssetFile": "Templates/ThankYou-fr.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2373"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"biio33qLgD+Oz3busPRlCyDjih1WDXjo+9caC57oM4o=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6nh9u6qvzv"}, {"Name": "integrity", "Value": "sha256-biio33qLgD+Oz3busPRlCyDjih1WDXjo+9caC57oM4o="}, {"Name": "label", "Value": "Templates/ThankYou-fr.html"}]}, {"Route": "Templates/ThankYou-fr.html", "AssetFile": "Templates/ThankYou-fr.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2373"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"biio33qLgD+Oz3busPRlCyDjih1WDXjo+9caC57oM4o=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-biio33qLgD+Oz3busPRlCyDjih1WDXjo+9caC57oM4o="}]}, {"Route": "Templates/ThankYou-it.9q9oxzsxlo.html", "AssetFile": "Templates/ThankYou-it.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2381"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"v4uLFordz1mainzhWH7i84VDaMaZ+yDtJnk8bHC60jY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9q9oxzsxlo"}, {"Name": "integrity", "Value": "sha256-v4uLFordz1mainzhWH7i84VDaMaZ+yDtJnk8bHC60jY="}, {"Name": "label", "Value": "Templates/ThankYou-it.html"}]}, {"Route": "Templates/ThankYou-it.html", "AssetFile": "Templates/ThankYou-it.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2381"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"v4uLFordz1mainzhWH7i84VDaMaZ+yDtJnk8bHC60jY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-v4uLFordz1mainzhWH7i84VDaMaZ+yDtJnk8bHC60jY="}]}, {"Route": "Templates/ThankYouCustomer-de.html", "AssetFile": "Templates/ThankYouCustomer-de.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2605"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"q9QcxtnT34lmtcNYfCUJZTwcDbFK84YnXxRSgb8hr+4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q9QcxtnT34lmtcNYfCUJZTwcDbFK84YnXxRSgb8hr+4="}]}, {"Route": "Templates/ThankYouCustomer-de.nt4xq5ndu1.html", "AssetFile": "Templates/ThankYouCustomer-de.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2605"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"q9QcxtnT34lmtcNYfCUJZTwcDbFK84YnXxRSgb8hr+4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nt4xq5ndu1"}, {"Name": "integrity", "Value": "sha256-q9QcxtnT34lmtcNYfCUJZTwcDbFK84YnXxRSgb8hr+4="}, {"Name": "label", "Value": "Templates/ThankYouCustomer-de.html"}]}, {"Route": "Templates/ThankYouCustomer-en.html", "AssetFile": "Templates/ThankYouCustomer-en.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2710"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"uTt5oQGoIqqT6Is4ATphkPEdcRIIf+M0Tz+/F4gUUeM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uTt5oQGoIqqT6Is4ATphkPEdcRIIf+M0Tz+/F4gUUeM="}]}, {"Route": "Templates/ThankYouCustomer-en.nssyq9ewpc.html", "AssetFile": "Templates/ThankYouCustomer-en.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2710"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"uTt5oQGoIqqT6Is4ATphkPEdcRIIf+M0Tz+/F4gUUeM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nssyq9ewpc"}, {"Name": "integrity", "Value": "sha256-uTt5oQGoIqqT6Is4ATphkPEdcRIIf+M0Tz+/F4gUUeM="}, {"Name": "label", "Value": "Templates/ThankYouCustomer-en.html"}]}, {"Route": "Templates/ThankYouCustomer-fr.html", "AssetFile": "Templates/ThankYouCustomer-fr.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2822"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"vCZQCDfLa1eqTTDC1eBxsFlJByq2/8rt559j/R2IKrM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vCZQCDfLa1eqTTDC1eBxsFlJByq2/8rt559j/R2IKrM="}]}, {"Route": "Templates/ThankYouCustomer-fr.sa0j1gvmik.html", "AssetFile": "Templates/ThankYouCustomer-fr.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2822"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"vCZQCDfLa1eqTTDC1eBxsFlJByq2/8rt559j/R2IKrM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sa0j1gvmik"}, {"Name": "integrity", "Value": "sha256-vCZQCDfLa1eqTTDC1eBxsFlJByq2/8rt559j/R2IKrM="}, {"Name": "label", "Value": "Templates/ThankYouCustomer-fr.html"}]}, {"Route": "Templates/ThankYouCustomer-it.4sfjp0ek4d.html", "AssetFile": "Templates/ThankYouCustomer-it.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2749"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XJA5i0qj7q+sC+9uQYm5i3v0EOFHIeLQx+TxYD+VKiw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4sfjp0ek4d"}, {"Name": "integrity", "Value": "sha256-XJA5i0qj7q+sC+9uQYm5i3v0EOFHIeLQx+TxYD+VKiw="}, {"Name": "label", "Value": "Templates/ThankYouCustomer-it.html"}]}, {"Route": "Templates/ThankYouCustomer-it.html", "AssetFile": "Templates/ThankYouCustomer-it.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2749"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XJA5i0qj7q+sC+9uQYm5i3v0EOFHIeLQx+TxYD+VKiw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XJA5i0qj7q+sC+9uQYm5i3v0EOFHIeLQx+TxYD+VKiw="}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1723"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"nDHJdDTU6Iz19np37pHzXUkRFktd+Xe1TIcSYBSEXng=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nDHJdDTU6Iz19np37pHzXUkRFktd+Xe1TIcSYBSEXng="}]}, {"Route": "css/site.kc2urnfzw5.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1723"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"nDHJdDTU6Iz19np37pHzXUkRFktd+Xe1TIcSYBSEXng=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kc2urnfzw5"}, {"Name": "integrity", "Value": "sha256-nDHJdDTU6Iz19np37pHzXUkRFktd+Xe1TIcSYBSEXng="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "js/site.hi2kjbe51q.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1456"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uWjtMtYC5kszJ1cwKh0NmtSCsNWSiRtuFhVI+7fhODQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hi2kjbe51q"}, {"Name": "integrity", "Value": "sha256-uWjtMtYC5kszJ1cwKh0NmtSCsNWSiRtuFhVI+7fhODQ="}, {"Name": "label", "Value": "js/site.js"}]}, {"Route": "js/site.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1456"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uWjtMtYC5kszJ1cwKh0NmtSCsNWSiRtuFhVI+7fhODQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uWjtMtYC5kszJ1cwKh0NmtSCsNWSiRtuFhVI+7fhODQ="}]}, {"Route": "lib/flatpickr/flatpickr.min.5i5ypew3r7.js", "AssetFile": "lib/flatpickr/flatpickr.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "58061"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oz6OpgDwrizfPiuCTQX5Ug80vo7DGQOJb2A4nL67q6c=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5i5ypew3r7"}, {"Name": "integrity", "Value": "sha256-oz6OpgDwrizfPiuCTQX5Ug80vo7DGQOJb2A4nL67q6c="}, {"Name": "label", "Value": "lib/flatpickr/flatpickr.min.js"}]}, {"Route": "lib/flatpickr/flatpickr.min.css", "AssetFile": "lib/flatpickr/flatpickr.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23051"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NMWQZRHPM5ffxy8dRoyH7oxIu6MYvA73jKaxUlFrHPg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NMWQZRHPM5ffxy8dRoyH7oxIu6MYvA73jKaxUlFrHPg="}]}, {"Route": "lib/flatpickr/flatpickr.min.gno4lkuonv.css", "AssetFile": "lib/flatpickr/flatpickr.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23051"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NMWQZRHPM5ffxy8dRoyH7oxIu6MYvA73jKaxUlFrHPg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gno4lkuonv"}, {"Name": "integrity", "Value": "sha256-NMWQZRHPM5ffxy8dRoyH7oxIu6MYvA73jKaxUlFrHPg="}, {"Name": "label", "Value": "lib/flatpickr/flatpickr.min.css"}]}, {"Route": "lib/flatpickr/flatpickr.min.js", "AssetFile": "lib/flatpickr/flatpickr.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "58061"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oz6OpgDwrizfPiuCTQX5Ug80vo7DGQOJb2A4nL67q6c=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oz6OpgDwrizfPiuCTQX5Ug80vo7DGQOJb2A4nL67q6c="}]}, {"Route": "lib/jquery-ui/jquery-ui.5tbpznqf90.js", "AssetFile": "lib/jquery-ui/jquery-ui.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "549952"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CSeOsMhq+M856gm/B5dxlHOaggTUiPMq7vwBF5YbKmA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5tbpznqf90"}, {"Name": "integrity", "Value": "sha256-CSeOsMhq+M856gm/B5dxlHOaggTUiPMq7vwBF5YbKmA="}, {"Name": "label", "Value": "lib/jquery-ui/jquery-ui.js"}]}, {"Route": "lib/jquery-ui/jquery-ui.89idfrxomp.css", "AssetFile": "lib/jquery-ui/jquery-ui.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "43221"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rGh6mkaaOyd/13on2ZiAFbzoLj49snCWUDCR0RtG6Q8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "89idfrxomp"}, {"Name": "integrity", "Value": "sha256-rGh6mkaaOyd/13on2ZiAFbzoLj49snCWUDCR0RtG6Q8="}, {"Name": "label", "Value": "lib/jquery-ui/jquery-ui.css"}]}, {"Route": "lib/jquery-ui/jquery-ui.css", "AssetFile": "lib/jquery-ui/jquery-ui.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "43221"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rGh6mkaaOyd/13on2ZiAFbzoLj49snCWUDCR0RtG6Q8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rGh6mkaaOyd/13on2ZiAFbzoLj49snCWUDCR0RtG6Q8="}]}, {"Route": "lib/jquery-ui/jquery-ui.js", "AssetFile": "lib/jquery-ui/jquery-ui.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "549952"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CSeOsMhq+M856gm/B5dxlHOaggTUiPMq7vwBF5YbKmA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CSeOsMhq+M856gm/B5dxlHOaggTUiPMq7vwBF5YbKmA="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.4ez5mqxv2b.txt", "AssetFile": "lib/jquery-validation-unobtrusive/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "575"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"bJBFwc85FxNITit+4+dmzq5fRpbBEauvU+1eI+EpziQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4ez5mqxv2b"}, {"Name": "integrity", "Value": "sha256-bJBFwc85FxNITit+4+dmzq5fRpbBEauvU+1eI+EpziQ="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt"}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "lib/jquery-validation-unobtrusive/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "575"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"bJBFwc85FxNITit+4+dmzq5fRpbBEauvU+1eI+EpziQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bJBFwc85FxNITit+4+dmzq5fRpbBEauvU+1eI+EpziQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.0td7jq9nxb.js", "AssetFile": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19366"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0td7jq9nxb"}, {"Name": "integrity", "Value": "sha256-XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetFile": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19366"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetFile": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5868"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zV8SHd+o2hq7FLST9WlWzpZMGfniOYeMMrQT6lTxjls=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zV8SHd+o2hq7FLST9WlWzpZMGfniOYeMMrQT6lTxjls="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.zsyoy62yqm.js", "AssetFile": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5868"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zV8SHd+o2hq7FLST9WlWzpZMGfniOYeMMrQT6lTxjls=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zsyoy62yqm"}, {"Name": "integrity", "Value": "sha256-zV8SHd+o2hq7FLST9WlWzpZMGfniOYeMMrQT6lTxjls="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}]}, {"Route": "lib/jquery-validation/LICENSE.md", "AssetFile": "lib/jquery-validation/LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1095"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"85iHjKszi4aWOL2sGurna/OsEbK4nabgtovBpkVzNEA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-85iHjKszi4aWOL2sGurna/OsEbK4nabgtovBpkVzNEA="}]}, {"Route": "lib/jquery-validation/LICENSE.xzw0cte36n.md", "AssetFile": "lib/jquery-validation/LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1095"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"85iHjKszi4aWOL2sGurna/OsEbK4nabgtovBpkVzNEA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xzw0cte36n"}, {"Name": "integrity", "Value": "sha256-85iHjKszi4aWOL2sGurna/OsEbK4nabgtovBpkVzNEA="}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md"}]}, {"Route": "lib/jquery-validation/dist/additional-methods.gcjdx5jb8l.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51466"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0HeMWyQUbTbe7SGnSYbjj9+HVA0hKDmEUtbYoTKe+Bk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gcjdx5jb8l"}, {"Name": "integrity", "Value": "sha256-0HeMWyQUbTbe7SGnSYbjj9+HVA0hKDmEUtbYoTKe+Bk="}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js"}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51466"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0HeMWyQUbTbe7SGnSYbjj9+HVA0hKDmEUtbYoTKe+Bk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0HeMWyQUbTbe7SGnSYbjj9+HVA0hKDmEUtbYoTKe+Bk="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.35gxhxa0gh.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22174"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"90Rlzuz8pIZK4g9o2I7nGK+9n5cU9Rbdt4GtxRO5arA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "35gxhxa0gh"}, {"Name": "integrity", "Value": "sha256-90R<PERSON><PERSON>z8pIZK4g9o2I7nGK+9n5cU9Rbdt4GtxRO5arA="}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js"}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22174"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"90Rlzuz8pIZK4g9o2I7nGK+9n5cU9Rbdt4GtxRO5arA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-90R<PERSON><PERSON>z8pIZK4g9o2I7nGK+9n5cU9Rbdt4GtxRO5arA="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.b7iojwaux1.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7iojwaux1"}, {"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js"}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.pzqfkb6aqo.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pzqfkb6aqo"}, {"Name": "integrity", "Value": "sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js"}]}, {"Route": "lib/sweetalert2/sweet-alerts.init.1hy9sozciy.js", "AssetFile": "lib/sweetalert2/sweet-alerts.init.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5186"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O713m4S+Nla9Fz3hm7uRVYXjm3RKdyjANyAcMxQUMqM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1hy9sozciy"}, {"Name": "integrity", "Value": "sha256-O713m4S+Nla9Fz3hm7uRVYXjm3RKdyjANyAcMxQUMqM="}, {"Name": "label", "Value": "lib/sweetalert2/sweet-alerts.init.js"}]}, {"Route": "lib/sweetalert2/sweet-alerts.init.js", "AssetFile": "lib/sweetalert2/sweet-alerts.init.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5186"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O713m4S+Nla9Fz3hm7uRVYXjm3RKdyjANyAcMxQUMqM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O713m4S+Nla9Fz3hm7uRVYXjm3RKdyjANyAcMxQUMqM="}]}, {"Route": "lib/sweetalert2/sweetalert2.min.css", "AssetFile": "lib/sweetalert2/sweetalert2.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "40533"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hLMJZipQRPPE02iB4hqYxrGKht4CPqqm4hXfxnJ1RYA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hLMJZipQRPPE02iB4hqYxrGKht4CPqqm4hXfxnJ1RYA="}]}, {"Route": "lib/sweetalert2/sweetalert2.min.js", "AssetFile": "lib/sweetalert2/sweetalert2.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "79229"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hN/E1DmwAKy7yKvTL+rrKp+5jow0GskgNjaiFv4bGNI=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hN/E1DmwAKy7yKvTL+rrKp+5jow0GskgNjaiFv4bGNI="}]}, {"Route": "lib/sweetalert2/sweetalert2.min.ka04m46apy.js", "AssetFile": "lib/sweetalert2/sweetalert2.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "79229"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hN/E1DmwAKy7yKvTL+rrKp+5jow0GskgNjaiFv4bGNI=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ka04m46apy"}, {"Name": "integrity", "Value": "sha256-hN/E1DmwAKy7yKvTL+rrKp+5jow0GskgNjaiFv4bGNI="}, {"Name": "label", "Value": "lib/sweetalert2/sweetalert2.min.js"}]}, {"Route": "lib/sweetalert2/sweetalert2.min.o9isx86jyx.css", "AssetFile": "lib/sweetalert2/sweetalert2.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "40533"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hLMJZipQRPPE02iB4hqYxrGKht4CPqqm4hXfxnJ1RYA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o9isx86jyx"}, {"Name": "integrity", "Value": "sha256-hLMJZipQRPPE02iB4hqYxrGKht4CPqqm4hXfxnJ1RYA="}, {"Name": "label", "Value": "lib/sweetalert2/sweetalert2.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/icons/default/icons.min.a311uwf1bl.js", "AssetFile": "lib/tinymce/js/tinymce/icons/default/icons.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "67093"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OrZKRXNWpYLJeuB40gXMbY1NYzcDdjTWhHMWRrx3S0w=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a311uwf1bl"}, {"Name": "integrity", "Value": "sha256-OrZKRXNWpYLJeuB40gXMbY1NYzcDdjTWhHMWRrx3S0w="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/icons/default/icons.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/icons/default/icons.min.js", "AssetFile": "lib/tinymce/js/tinymce/icons/default/icons.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "67093"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OrZKRXNWpYLJeuB40gXMbY1NYzcDdjTWhHMWRrx3S0w=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OrZKRXNWpYLJeuB40gXMbY1NYzcDdjTWhHMWRrx3S0w="}]}, {"Route": "lib/tinymce/js/tinymce/langs/de.js", "AssetFile": "lib/tinymce/js/tinymce/langs/de.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14915"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jNQyFHX7GGkWkNQd6l7k/TBBYaukmJG/lGf2OgKPRlg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jNQyFHX7GGkWkNQd6l7k/TBBYaukmJG/lGf2OgKPRlg="}]}, {"Route": "lib/tinymce/js/tinymce/langs/de.wvtppe077w.js", "AssetFile": "lib/tinymce/js/tinymce/langs/de.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14915"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jNQyFHX7GGkWkNQd6l7k/TBBYaukmJG/lGf2OgKPRlg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wvtppe077w"}, {"Name": "integrity", "Value": "sha256-jNQyFHX7GGkWkNQd6l7k/TBBYaukmJG/lGf2OgKPRlg="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/langs/de.js"}]}, {"Route": "lib/tinymce/js/tinymce/langs/fr.js", "AssetFile": "lib/tinymce/js/tinymce/langs/fr.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15498"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WqwlPjuN4XA63pipcPv3eVWtZFCBMkLV/CCkgY7QjYY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WqwlPjuN4XA63pipcPv3eVWtZFCBMkLV/CCkgY7QjYY="}]}, {"Route": "lib/tinymce/js/tinymce/langs/fr.qch6z36znb.js", "AssetFile": "lib/tinymce/js/tinymce/langs/fr.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15498"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WqwlPjuN4XA63pipcPv3eVWtZFCBMkLV/CCkgY7QjYY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qch6z36znb"}, {"Name": "integrity", "Value": "sha256-WqwlPjuN4XA63pipcPv3eVWtZFCBMkLV/CCkgY7QjYY="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/langs/fr.js"}]}, {"Route": "lib/tinymce/js/tinymce/langs/it.js", "AssetFile": "lib/tinymce/js/tinymce/langs/it.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14864"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0R45OOMQehpekdzjm9XFoAhlY6XP5tgdhCuPnV1gf88=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0R45OOMQehpekdzjm9XFoAhlY6XP5tgdhCuPnV1gf88="}]}, {"Route": "lib/tinymce/js/tinymce/langs/it.xwf5nig97g.js", "AssetFile": "lib/tinymce/js/tinymce/langs/it.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14864"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0R45OOMQehpekdzjm9XFoAhlY6XP5tgdhCuPnV1gf88=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xwf5nig97g"}, {"Name": "integrity", "Value": "sha256-0R45OOMQehpekdzjm9XFoAhlY6XP5tgdhCuPnV1gf88="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/langs/it.js"}]}, {"Route": "lib/tinymce/js/tinymce/license.6q9vckcrvl.txt", "AssetFile": "lib/tinymce/js/tinymce/license.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"AuFIf7NMpoTAxHiBJGeJ+fnLu3ZdewyqbfBPQmvu6Ss=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6q9vckcrvl"}, {"Name": "integrity", "Value": "sha256-AuFIf7NMpoTAxHiBJGeJ+fnLu3ZdewyqbfBPQmvu6Ss="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/license.txt"}]}, {"Route": "lib/tinymce/js/tinymce/license.txt", "AssetFile": "lib/tinymce/js/tinymce/license.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"AuFIf7NMpoTAxHiBJGeJ+fnLu3ZdewyqbfBPQmvu6Ss=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AuFIf7NMpoTAxHiBJGeJ+fnLu3ZdewyqbfBPQmvu6Ss="}]}, {"Route": "lib/tinymce/js/tinymce/models/dom/model.min.duy7y4d4ys.js", "AssetFile": "lib/tinymce/js/tinymce/models/dom/model.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "96635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JW4mmSPFbukV+MlXJt4j3+Ck9/YvSjsTIeWxKqhApYw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "duy7y4d4ys"}, {"Name": "integrity", "Value": "sha256-JW4mmSPFbukV+MlXJt4j3+Ck9/YvSjsTIeWxKqhApYw="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/models/dom/model.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/models/dom/model.min.js", "AssetFile": "lib/tinymce/js/tinymce/models/dom/model.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "96635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JW4mmSPFbukV+MlXJt4j3+Ck9/YvSjsTIeWxKqhApYw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JW4mmSPFbukV+MlXJt4j3+Ck9/YvSjsTIeWxKqhApYw="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/advlist/plugin.min.8mf5u9u62l.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/advlist/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3596"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sB88l2veKSasFiX6UwgdOgItMhHOnvbYD1EI41o9ZKQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8mf5u9u62l"}, {"Name": "integrity", "Value": "sha256-sB88l2veKSasFiX6UwgdOgItMhHOnvbYD1EI41o9ZKQ="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/advlist/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/advlist/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/advlist/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3596"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sB88l2veKSasFiX6UwgdOgItMhHOnvbYD1EI41o9ZKQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sB88l2veKSasFiX6UwgdOgItMhHOnvbYD1EI41o9ZKQ="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/anchor/plugin.min.jgmmjjao6x.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/anchor/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2519"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"M9rXE4zOfBNceqEEVYRF9Wc0eJFKN5nN6XJsqouEm9w=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jgmmjjao6x"}, {"Name": "integrity", "Value": "sha256-M9rXE4zOfBNceqEEVYRF9Wc0eJFKN5nN6XJsqouEm9w="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/anchor/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/anchor/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/anchor/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2519"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"M9rXE4zOfBNceqEEVYRF9Wc0eJFKN5nN6XJsqouEm9w=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M9rXE4zOfBNceqEEVYRF9Wc0eJFKN5nN6XJsqouEm9w="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/autolink/plugin.min.5j7akqptjm.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/autolink/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3262"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X6/iPTXo3DHoRrhIBYx3G11EP9yoqNxuEB+EALh70nY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5j7akqptjm"}, {"Name": "integrity", "Value": "sha256-X6/iPTXo3DHoRrhIBYx3G11EP9yoqNxuEB+EALh70nY="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/autolink/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/autolink/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/autolink/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3262"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X6/iPTXo3DHoRrhIBYx3G11EP9yoqNxuEB+EALh70nY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X6/iPTXo3DHoRrhIBYx3G11EP9yoqNxuEB+EALh70nY="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/autoresize/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/autoresize/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2523"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"NZDPk3nIjTd5kYSwUjepixD/wI3Pg2xE/Z0vwbw5ty8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NZDPk3nIjTd5kYSwUjepixD/wI3Pg2xE/Z0vwbw5ty8="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/autoresize/plugin.min.prxhfvklc1.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/autoresize/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2523"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"NZDPk3nIjTd5kYSwUjepixD/wI3Pg2xE/Z0vwbw5ty8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "prxhfvklc1"}, {"Name": "integrity", "Value": "sha256-NZDPk3nIjTd5kYSwUjepixD/wI3Pg2xE/Z0vwbw5ty8="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/autoresize/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/autosave/plugin.min.cjafcovg7d.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/autosave/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3331"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GL9/sUtFd7E90S/DlSlb1k6CZ07rPy8AxoRv6HYPdUc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cjafcovg7d"}, {"Name": "integrity", "Value": "sha256-GL9/sUtFd7E90S/DlSlb1k6CZ07rPy8AxoRv6HYPdUc="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/autosave/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/autosave/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/autosave/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3331"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GL9/sUtFd7E90S/DlSlb1k6CZ07rPy8AxoRv6HYPdUc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GL9/sUtFd7E90S/DlSlb1k6CZ07rPy8AxoRv6HYPdUc="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/charmap/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/charmap/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11014"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YDZqO1M5WTzWCRJFvlCjvgTNAyHWEtGIZiWCMg/q7I4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YDZqO1M5WTzWCRJFvlCjvgTNAyHWEtGIZiWCMg/q7I4="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/charmap/plugin.min.k7ldt4dufz.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/charmap/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11014"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YDZqO1M5WTzWCRJFvlCjvgTNAyHWEtGIZiWCMg/q7I4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k7ldt4dufz"}, {"Name": "integrity", "Value": "sha256-YDZqO1M5WTzWCRJFvlCjvgTNAyHWEtGIZiWCMg/q7I4="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/charmap/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/code/plugin.min.4ddczsiarp.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/code/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "880"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WJ7/J6y6iN/ejUeEuBKwRHYYr19C+dswBchCCc4i4dc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4ddczsiarp"}, {"Name": "integrity", "Value": "sha256-WJ7/J6y6iN/ejUeEuBKwRHYYr19C+dswBchCCc4i4dc="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/code/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/code/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/code/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "880"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WJ7/J6y6iN/ejUeEuBKwRHYYr19C+dswBchCCc4i4dc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WJ7/J6y6iN/ejUeEuBKwRHYYr19C+dswBchCCc4i4dc="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/codesample/plugin.min.c8susjty0k.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/codesample/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "48071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lP14sGZBt232AXx2IgiffG05ToLDl94/8gelcAqKz/k=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c8susjty0k"}, {"Name": "integrity", "Value": "sha256-lP14sGZBt232AXx2IgiffG05ToLDl94/8gelcAqKz/k="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/codesample/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/codesample/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/codesample/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lP14sGZBt232AXx2IgiffG05ToLDl94/8gelcAqKz/k=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lP14sGZBt232AXx2IgiffG05ToLDl94/8gelcAqKz/k="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/directionality/plugin.min.3a71kovr6i.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/directionality/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4363"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G38CauAcr2c29p6hTsmx7Vf7g8aVD3XLMraDzT1LFkg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3a71kovr6i"}, {"Name": "integrity", "Value": "sha256-G38CauAcr2c29p6hTsmx7Vf7g8aVD3XLMraDzT1LFkg="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/directionality/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/directionality/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/directionality/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4363"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G38CauAcr2c29p6hTsmx7Vf7g8aVD3XLMraDzT1LFkg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G38CauAcr2c29p6hTsmx7Vf7g8aVD3XLMraDzT1LFkg="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "410112"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HUSrMDE/gPFhzb1KoZuCEGxbRLDTVw7EK0xrnLV2MLw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HUSrMDE/gPFhzb1KoZuCEGxbRLDTVw7EK0xrnLV2MLw="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.liga2smdmf.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "410112"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HUSrMDE/gPFhzb1KoZuCEGxbRLDTVw7EK0xrnLV2MLw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "liga2smdmf"}, {"Name": "integrity", "Value": "sha256-HUSrMDE/gPFhzb1KoZuCEGxbRLDTVw7EK0xrnLV2MLw="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "416097"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VhbNgwleZ0aBrlDYhC0HoCXNM/tFWw334aHWDzsX7X4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VhbNgwleZ0aBrlDYhC0HoCXNM/tFWw334aHWDzsX7X4="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.min.yu9bjgbclx.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "416097"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VhbNgwleZ0aBrlDYhC0HoCXNM/tFWw334aHWDzsX7X4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yu9bjgbclx"}, {"Name": "integrity", "Value": "sha256-VhbNgwleZ0aBrlDYhC0HoCXNM/tFWw334aHWDzsX7X4="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "186921"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cd/Y1gLoV++Ss2+uK/pBed5ezvmZvvkKUbiNZlAfeNg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cd/Y1gLoV++Ss2+uK/pBed5ezvmZvvkKUbiNZlAfeNg="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "192857"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9qA+svinem//r5QpAqi0hY5wSTsRMxZYDlVFDiqxHUk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9qA+svinem//r5QpAqi0hY5wSTsRMxZYDlVFDiqxHUk="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.min.uvtj9eiepa.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "192857"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9qA+svinem//r5QpAqi0hY5wSTsRMxZYDlVFDiqxHUk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uvtj9eiepa"}, {"Name": "integrity", "Value": "sha256-9qA+svinem//r5QpAqi0hY5wSTsRMxZYDlVFDiqxHUk="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.zrqg1ly84r.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "186921"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cd/Y1gLoV++Ss2+uK/pBed5ezvmZvvkKUbiNZlAfeNg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zrqg1ly84r"}, {"Name": "integrity", "Value": "sha256-cd/Y1gLoV++Ss2+uK/pBed5ezvmZvvkKUbiNZlAfeNg="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/emoticons/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/emoticons/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6393"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O6NJFEHcJ+C0px3Y2uzYyU7A9CGNi5anTyYRX0nBm/w=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O6NJFEHcJ+C0px3Y2uzYyU7A9CGNi5anTyYRX0nBm/w="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/emoticons/plugin.min.tyqdbacaxt.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/emoticons/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6393"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O6NJFEHcJ+C0px3Y2uzYyU7A9CGNi5anTyYRX0nBm/w=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tyqdbacaxt"}, {"Name": "integrity", "Value": "sha256-O6NJFEHcJ+C0px3Y2uzYyU7A9CGNi5anTyYRX0nBm/w="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/emoticons/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/fullscreen/plugin.min.h0f14fag6o.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/fullscreen/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14724"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QcJ3NX+BjeJwiDpHt7zSuredna6DxB4sJjLbd5W9jAw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h0f14fag6o"}, {"Name": "integrity", "Value": "sha256-QcJ3NX+BjeJwiDpHt7zSuredna6DxB4sJjLbd5W9jAw="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/fullscreen/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/fullscreen/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/fullscreen/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14724"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QcJ3NX+BjeJwiDpHt7zSuredna6DxB4sJjLbd5W9jAw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QcJ3NX+BjeJwiDpHt7zSuredna6DxB4sJjLbd5W9jAw="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/help/plugin.min.44sxdvkzh1.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/help/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14146"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bOqLAZv7afzVj3xwnDmzzxhShG2oP5ULhWytG5x1FzM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "44sxdvkzh1"}, {"Name": "integrity", "Value": "sha256-bOqLAZv7afzVj3xwnDmzzxhShG2oP5ULhWytG5x1FzM="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/help/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/help/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/help/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14146"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bOqLAZv7afzVj3xwnDmzzxhShG2oP5ULhWytG5x1FzM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bOqLAZv7afzVj3xwnDmzzxhShG2oP5ULhWytG5x1FzM="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/image/plugin.min.cblduhn0ke.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/image/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19345"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KN/12ahHVS1ShB0dbnggNsZbe05+a6TxiB9U8Z0Hcm4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cblduhn0ke"}, {"Name": "integrity", "Value": "sha256-KN/12ahHVS1ShB0dbnggNsZbe05+a6TxiB9U8Z0Hcm4="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/image/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/image/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/image/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19345"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KN/12ahHVS1ShB0dbnggNsZbe05+a6TxiB9U8Z0Hcm4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KN/12ahHVS1ShB0dbnggNsZbe05+a6TxiB9U8Z0Hcm4="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/importcss/plugin.min.47anb2mjko.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/importcss/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4056"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YOYNs4IzRdIEB9dGXNeF4+xuMtua3xaUzIgtruEYDRY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47anb2m<PERSON>o"}, {"Name": "integrity", "Value": "sha256-YOYNs4IzRdIEB9dGXNeF4+xuMtua3xaUzIgtruEYDRY="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/importcss/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/importcss/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/importcss/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4056"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YOYNs4IzRdIEB9dGXNeF4+xuMtua3xaUzIgtruEYDRY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YOYNs4IzRdIEB9dGXNeF4+xuMtua3xaUzIgtruEYDRY="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/insertdatetime/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/insertdatetime/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2886"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9rVcmFSBroQ24NjkfK+7PfynK30dkzpwA4j7PjXNLV8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9rVcmFSBroQ24NjkfK+7PfynK30dkzpwA4j7PjXNLV8="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/insertdatetime/plugin.min.qv8sml9f80.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/insertdatetime/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2886"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9rVcmFSBroQ24NjkfK+7PfynK30dkzpwA4j7PjXNLV8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qv8sml9f80"}, {"Name": "integrity", "Value": "sha256-9rVcmFSBroQ24NjkfK+7PfynK30dkzpwA4j7PjXNLV8="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/insertdatetime/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/link/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/link/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15655"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qdTUMHQ1ivMAFqkbnzyLjg2G2Lw2B0BFmnktKidGdBo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qdTUMHQ1ivMAFqkbnzyLjg2G2Lw2B0BFmnktKidGdBo="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/link/plugin.min.l1bjazi1ht.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/link/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15655"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qdTUMHQ1ivMAFqkbnzyLjg2G2Lw2B0BFmnktKidGdBo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l1bjazi1ht"}, {"Name": "integrity", "Value": "sha256-qdTUMHQ1ivMAFqkbnzyLjg2G2Lw2B0BFmnktKidGdBo="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/link/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/lists/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/lists/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24503"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"q5ZgdHCBZQ/KgkREVuLMEvNl8DRZqDr2Srzf+EkYTIw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q5ZgdHCBZQ/KgkREVuLMEvNl8DRZqDr2Srzf+EkYTIw="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/lists/plugin.min.pbb8yvsmll.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/lists/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24503"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"q5ZgdHCBZQ/KgkREVuLMEvNl8DRZqDr2Srzf+EkYTIw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pbb8yvsmll"}, {"Name": "integrity", "Value": "sha256-q5ZgdHCBZQ/KgkREVuLMEvNl8DRZqDr2Srzf+EkYTIw="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/lists/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/media/plugin.min.7iyho1y9tr.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/media/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16698"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7+RnW02dMHJvkS6j43M/XM7ARJaREuqlDmv56akcSwE=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7iyho1y9tr"}, {"Name": "integrity", "Value": "sha256-7+RnW02dMHJvkS6j43M/XM7ARJaREuqlDmv56akcSwE="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/media/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/media/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/media/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16698"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7+RnW02dMHJvkS6j43M/XM7ARJaREuqlDmv56akcSwE=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7+RnW02dMHJvkS6j43M/XM7ARJaREuqlDmv56akcSwE="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/nonbreaking/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/nonbreaking/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1419"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WZwYNun61Lm2ws06s18mAs/AvYzrr1ios6vNaZIZVVg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WZwYNun61Lm2ws06s18mAs/AvYzrr1ios6vNaZIZVVg="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/nonbreaking/plugin.min.zlpcbzoevc.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/nonbreaking/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1419"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WZwYNun61Lm2ws06s18mAs/AvYzrr1ios6vNaZIZVVg=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zlpcbzoevc"}, {"Name": "integrity", "Value": "sha256-WZwYNun61Lm2ws06s18mAs/AvYzrr1ios6vNaZIZVVg="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/nonbreaking/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/pagebreak/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/pagebreak/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1509"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"NHGv4s6mg2R/cI+Sc+llgOQTEuBxCFcWgA7XO4s5wQs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NHGv4s6mg2R/cI+Sc+llgOQTEuBxCFcWgA7XO4s5wQs="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/pagebreak/plugin.min.ovwbmcvqah.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/pagebreak/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1509"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"NHGv4s6mg2R/cI+Sc+llgOQTEuBxCFcWgA7XO4s5wQs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ovwbmcvqah"}, {"Name": "integrity", "Value": "sha256-NHGv4s6mg2R/cI+Sc+llgOQTEuBxCFcWgA7XO4s5wQs="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/pagebreak/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/preview/plugin.min.6lkls1wqwx.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/preview/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1721"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"huRPOwMMtY+MMuzfPGVPxjZUeQGltOOkZW8rz5Il5Kc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6lkls1wqwx"}, {"Name": "integrity", "Value": "sha256-huRPOwMMtY+MMuzfPGVPxjZUeQGltOOkZW8rz5Il5Kc="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/preview/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/preview/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/preview/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1721"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"huRPOwMMtY+MMuzfPGVPxjZUeQGltOOkZW8rz5Il5Kc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-huRPOwMMtY+MMuzfPGVPxjZUeQGltOOkZW8rz5Il5Kc="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/quickbars/plugin.min.3qkdkjqh6m.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/quickbars/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"K0ps/5joHKY9GZlryyMCpNUw99ak1IuWHXWJTh22uKQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3qkdkjqh6m"}, {"Name": "integrity", "Value": "sha256-K0ps/5joHKY9GZlryyMCpNUw99ak1IuWHXWJTh22uKQ="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/quickbars/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/quickbars/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/quickbars/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"K0ps/5joHKY9GZlryyMCpNUw99ak1IuWHXWJTh22uKQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K0ps/5joHKY9GZlryyMCpNUw99ak1IuWHXWJTh22uKQ="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/save/plugin.min.39ecmwg4fk.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/save/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1595"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2dbVtLEsfZ2352U4i1aW0fuWKhVe65ROOQW3S1abmik=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "39ecmwg4fk"}, {"Name": "integrity", "Value": "sha256-2dbVtLEsfZ2352U4i1aW0fuWKhVe65ROOQW3S1abmik="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/save/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/save/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/save/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1595"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2dbVtLEsfZ2352U4i1aW0fuWKhVe65ROOQW3S1abmik=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2dbVtLEsfZ2352U4i1aW0fuWKhVe65ROOQW3S1abmik="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/searchreplace/plugin.min.hd8f6h4flh.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/searchreplace/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "13420"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"230LS7ZgEYDlOs8uqnxzpIKgvz8+OM7f3HBwWyLBjR4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hd8f6h4flh"}, {"Name": "integrity", "Value": "sha256-230LS7ZgEYDlOs8uqnxzpIKgvz8+OM7f3HBwWyLBjR4="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/searchreplace/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/searchreplace/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/searchreplace/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13420"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"230LS7ZgEYDlOs8uqnxzpIKgvz8+OM7f3HBwWyLBjR4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-230LS7ZgEYDlOs8uqnxzpIKgvz8+OM7f3HBwWyLBjR4="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/table/plugin.min.gt40yiyx87.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/table/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "47313"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZGIB1LWXJ3klIWlgODDC4bv5gWbNfOJREI0zGq/Pjwk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gt40yiyx87"}, {"Name": "integrity", "Value": "sha256-ZGIB1LWXJ3klIWlgODDC4bv5gWbNfOJREI0zGq/Pjwk="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/table/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/table/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/table/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "47313"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZGIB1LWXJ3klIWlgODDC4bv5gWbNfOJREI0zGq/Pjwk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZGIB1LWXJ3klIWlgODDC4bv5gWbNfOJREI0zGq/Pjwk="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/template/plugin.min.3jv5k5vtg9.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/template/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8262"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IdmBFLikOBbH3OD3ocUMDgphC7jurcOkFI4e5YbG9f0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3jv5k5vtg9"}, {"Name": "integrity", "Value": "sha256-IdmBFLikOBbH3OD3ocUMDgphC7jurcOkFI4e5YbG9f0="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/template/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/template/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/template/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8262"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IdmBFLikOBbH3OD3ocUMDgphC7jurcOkFI4e5YbG9f0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IdmBFLikOBbH3OD3ocUMDgphC7jurcOkFI4e5YbG9f0="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/visualblocks/plugin.min.gbx3kasexj.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/visualblocks/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1233"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bAe4i6oJLcx6T2RXYh9KGSCiHKbrwTZW9mBz1Rxw85w=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gbx3kasexj"}, {"Name": "integrity", "Value": "sha256-bAe4i6oJLcx6T2RXYh9KGSCiHKbrwTZW9mBz1Rxw85w="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/visualblocks/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/visualblocks/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/visualblocks/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1233"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bAe4i6oJLcx6T2RXYh9KGSCiHKbrwTZW9mBz1Rxw85w=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bAe4i6oJLcx6T2RXYh9KGSCiHKbrwTZW9mBz1Rxw85w="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/visualchars/plugin.min.4igg1krqg4.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/visualchars/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5872"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jFjjJIxMlLkRAawsiueK5ENxrPCTNk6RCm+HijgbZpw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4igg1krqg4"}, {"Name": "integrity", "Value": "sha256-jFjjJIxMlLkRAawsiueK5ENxrPCTNk6RCm+HijgbZpw="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/visualchars/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/plugins/visualchars/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/visualchars/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5872"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jFjjJIxMlLkRAawsiueK5ENxrPCTNk6RCm+HijgbZpw=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jFjjJIxMlLkRAawsiueK5ENxrPCTNk6RCm+HijgbZpw="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/wordcount/plugin.min.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/wordcount/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11922"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"03uPxSjBw6NtgZQ7Ie7kntIU6LMRSfyG7fRvazN/WYc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-03uPxSjBw6NtgZQ7Ie7kntIU6LMRSfyG7fRvazN/WYc="}]}, {"Route": "lib/tinymce/js/tinymce/plugins/wordcount/plugin.min.ra9jyp21g1.js", "AssetFile": "lib/tinymce/js/tinymce/plugins/wordcount/plugin.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11922"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"03uPxSjBw6NtgZQ7Ie7kntIU6LMRSfyG7fRvazN/WYc=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ra9jyp21g1"}, {"Name": "integrity", "Value": "sha256-03uPxSjBw6NtgZQ7Ie7kntIU6LMRSfyG7fRvazN/WYc="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/plugins/wordcount/plugin.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/skins/content/dark/content.min.c0bbxs3g2a.css", "AssetFile": "lib/tinymce/js/tinymce/skins/content/dark/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1217"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hBL1swjQ8ATLOhDRSdYFftp9VSHp1C6wKU99Up96EtU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c0bbxs3g2a"}, {"Name": "integrity", "Value": "sha256-hBL1swjQ8ATLOhDRSdYFftp9VSHp1C6wKU99Up96EtU="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/content/dark/content.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/content/dark/content.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/content/dark/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1217"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hBL1swjQ8ATLOhDRSdYFftp9VSHp1C6wKU99Up96EtU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hBL1swjQ8ATLOhDRSdYFftp9VSHp1C6wKU99Up96EtU="}]}, {"Route": "lib/tinymce/js/tinymce/skins/content/default/content.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/content/default/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1150"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA="}]}, {"Route": "lib/tinymce/js/tinymce/skins/content/default/content.min.dehv3jch66.css", "AssetFile": "lib/tinymce/js/tinymce/skins/content/default/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1150"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dehv3jch66"}, {"Name": "integrity", "Value": "sha256-K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/content/default/content.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/content/document/content.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/content/document/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1249"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OibrZO1BwCZBqzlqLa49cmMjZScEWisnx8vW4m+Ixm4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OibrZO1BwCZBqzlqLa49cmMjZScEWisnx8vW4m+Ixm4="}]}, {"Route": "lib/tinymce/js/tinymce/skins/content/document/content.min.qcb3yuv502.css", "AssetFile": "lib/tinymce/js/tinymce/skins/content/document/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1249"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OibrZO1BwCZBqzlqLa49cmMjZScEWisnx8vW4m+Ixm4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qcb3yuv502"}, {"Name": "integrity", "Value": "sha256-OibrZO1BwCZBqzlqLa49cmMjZScEWisnx8vW4m+Ixm4="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/content/document/content.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/content/tinymce-5-dark/content.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/content/tinymce-5-dark/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1220"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2K1UjR5hvgdA7exHkd7cGxyDlx5xMN4Mq7DbANzRW74=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2K1UjR5hvgdA7exHkd7cGxyDlx5xMN4Mq7DbANzRW74="}]}, {"Route": "lib/tinymce/js/tinymce/skins/content/tinymce-5-dark/content.min.o43ooccw0k.css", "AssetFile": "lib/tinymce/js/tinymce/skins/content/tinymce-5-dark/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1220"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2K1UjR5hvgdA7exHkd7cGxyDlx5xMN4Mq7DbANzRW74=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o43ooccw0k"}, {"Name": "integrity", "Value": "sha256-2K1UjR5hvgdA7exHkd7cGxyDlx5xMN4Mq7DbANzRW74="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/content/tinymce-5-dark/content.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/content/tinymce-5/content.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/content/tinymce-5/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1150"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA="}]}, {"Route": "lib/tinymce/js/tinymce/skins/content/tinymce-5/content.min.dehv3jch66.css", "AssetFile": "lib/tinymce/js/tinymce/skins/content/tinymce-5/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1150"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dehv3jch66"}, {"Name": "integrity", "Value": "sha256-K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/content/tinymce-5/content.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/content/writer/content.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/content/writer/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1171"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HcG61xkSF80A9nX7joEfy78oze7ipNnWJLcZFNyXkN0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HcG61xkSF80A9nX7joEfy78oze7ipNnWJLcZFNyXkN0="}]}, {"Route": "lib/tinymce/js/tinymce/skins/content/writer/content.min.l2coynlc21.css", "AssetFile": "lib/tinymce/js/tinymce/skins/content/writer/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1171"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HcG61xkSF80A9nX7joEfy78oze7ipNnWJLcZFNyXkN0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l2coynlc21"}, {"Name": "integrity", "Value": "sha256-HcG61xkSF80A9nX7joEfy78oze7ipNnWJLcZFNyXkN0="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/content/writer/content.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.inline.min.9a0ytxc496.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.inline.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23262"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9a0ytxc496"}, {"Name": "integrity", "Value": "sha256-D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.inline.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.inline.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.inline.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23262"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs="}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.min.brznt3mq19.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22932"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "brznt3mq19"}, {"Name": "integrity", "Value": "sha256-xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22932"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho="}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "72948"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lln0brrr3DgD34KEMqozIoVfjQJLAe4d5TFN7bH2qng=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lln0brrr3DgD34KEMqozIoVfjQJLAe4d5TFN7bH2qng="}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.min.uukcty6ctu.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "72948"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lln0brrr3DgD34KEMqozIoVfjQJLAe4d5TFN7bH2qng=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uukcty6ctu"}, {"Name": "integrity", "Value": "sha256-lln0brrr3DgD34KEMqozIoVfjQJLAe4d5TFN7bH2qng="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.shadowdom.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.shadowdom.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "509"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM="}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.shadowdom.min.oy4aiorfkf.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.shadowdom.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "509"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "oy4aiorfkf"}, {"Name": "integrity", "Value": "sha256-YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.shadowdom.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/oxide/content.inline.min.9a0ytxc496.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/oxide/content.inline.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23262"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9a0ytxc496"}, {"Name": "integrity", "Value": "sha256-D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/ui/oxide/content.inline.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/oxide/content.inline.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/oxide/content.inline.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23262"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs="}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/oxide/content.min.57uyr91r6a.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/oxide/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23321"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "57uyr91r6a"}, {"Name": "integrity", "Value": "sha256-n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/ui/oxide/content.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/oxide/content.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/oxide/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23321"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY="}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/oxide/skin.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/oxide/skin.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "72986"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Ag7NY4ZCHA7owxGaXnJXIelRzmBMSzCHDJ1rT0Abl4I=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ag7NY4ZCHA7owxGaXnJXIelRzmBMSzCHDJ1rT0Abl4I="}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/oxide/skin.min.eoxyrui399.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/oxide/skin.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "72986"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Ag7NY4ZCHA7owxGaXnJXIelRzmBMSzCHDJ1rT0Abl4I=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eoxyrui399"}, {"Name": "integrity", "Value": "sha256-Ag7NY4ZCHA7owxGaXnJXIelRzmBMSzCHDJ1rT0Abl4I="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/ui/oxide/skin.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/oxide/skin.shadowdom.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/oxide/skin.shadowdom.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "509"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM="}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/oxide/skin.shadowdom.min.oy4aiorfkf.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/oxide/skin.shadowdom.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "509"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "oy4aiorfkf"}, {"Name": "integrity", "Value": "sha256-YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/ui/oxide/skin.shadowdom.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.inline.min.9a0ytxc496.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.inline.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23262"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9a0ytxc496"}, {"Name": "integrity", "Value": "sha256-D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.inline.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.inline.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.inline.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23262"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs="}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.min.brznt3mq19.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22932"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "brznt3mq19"}, {"Name": "integrity", "Value": "sha256-xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22932"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho="}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "75477"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"a9n45qBDLmPlsAhDtfoZGv2hNHCVzT2FrFGYPVUf4fs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a9n45qBDLmPlsAhDtfoZGv2hNHCVzT2FrFGYPVUf4fs="}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.min.hdz6qpdk9t.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "75477"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"a9n45qBDLmPlsAhDtfoZGv2hNHCVzT2FrFGYPVUf4fs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hdz6qpdk9t"}, {"Name": "integrity", "Value": "sha256-a9n45qBDLmPlsAhDtfoZGv2hNHCVzT2FrFGYPVUf4fs="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "509"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM="}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.min.oy4aiorfkf.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "509"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "oy4aiorfkf"}, {"Name": "integrity", "Value": "sha256-YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.inline.min.9a0ytxc496.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.inline.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23262"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9a0ytxc496"}, {"Name": "integrity", "Value": "sha256-D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.inline.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.inline.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.inline.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23262"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs="}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.min.57uyr91r6a.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23321"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "57uyr91r6a"}, {"Name": "integrity", "Value": "sha256-n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23321"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY="}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.min.39r29kkvt5.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "75630"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iZDK3SFJICO3Vp0rP4MNGgUbVVigRqJXSWlENm0IGS4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "39r29kkvt5"}, {"Name": "integrity", "Value": "sha256-iZDK3SFJICO3Vp0rP4MNGgUbVVigRqJXSWlENm0IGS4="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "75630"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iZDK3SFJICO3Vp0rP4MNGgUbVVigRqJXSWlENm0IGS4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iZDK3SFJICO3Vp0rP4MNGgUbVVigRqJXSWlENm0IGS4="}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.shadowdom.min.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.shadowdom.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "509"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM="}]}, {"Route": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.shadowdom.min.oy4aiorfkf.css", "AssetFile": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.shadowdom.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "509"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "oy4aiorfkf"}, {"Name": "integrity", "Value": "sha256-YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.shadowdom.min.css"}]}, {"Route": "lib/tinymce/js/tinymce/themes/silver/theme.min.gm9r9yi2u9.js", "AssetFile": "lib/tinymce/js/tinymce/themes/silver/theme.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "397436"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"iIUuMQxnPVeQVw6MdduCoP8CtL8tz8Xk1GUh5XTFYx8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gm9r9yi2u9"}, {"Name": "integrity", "Value": "sha256-iIUuMQxnPVeQVw6MdduCoP8CtL8tz8Xk1GUh5XTFYx8="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/themes/silver/theme.min.js"}]}, {"Route": "lib/tinymce/js/tinymce/themes/silver/theme.min.js", "AssetFile": "lib/tinymce/js/tinymce/themes/silver/theme.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "397436"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"iIUuMQxnPVeQVw6MdduCoP8CtL8tz8Xk1GUh5XTFYx8=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iIUuMQxnPVeQVw6MdduCoP8CtL8tz8Xk1GUh5XTFYx8="}]}, {"Route": "lib/tinymce/js/tinymce/tinymce.d.0uz5kw6u8m.ts", "AssetFile": "lib/tinymce/js/tinymce/tinymce.d.ts", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "127423"}, {"Name": "Content-Type", "Value": "video/vnd.dlna.mpeg-tts"}, {"Name": "ETag", "Value": "\"qDg/T/2gGYkWtTcrrVfwPOmw2KkcVFmPuZLdPeHBomE=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0uz5kw6u8m"}, {"Name": "integrity", "Value": "sha256-qDg/T/2gGYkWtTcrrVfwPOmw2KkcVFmPuZLdPeHBomE="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/tinymce.d.ts"}]}, {"Route": "lib/tinymce/js/tinymce/tinymce.d.ts", "AssetFile": "lib/tinymce/js/tinymce/tinymce.d.ts", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "127423"}, {"Name": "Content-Type", "Value": "video/vnd.dlna.mpeg-tts"}, {"Name": "ETag", "Value": "\"qDg/T/2gGYkWtTcrrVfwPOmw2KkcVFmPuZLdPeHBomE=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qDg/T/2gGYkWtTcrrVfwPOmw2KkcVFmPuZLdPeHBomE="}]}, {"Route": "lib/tinymce/js/tinymce/tinymce.min.js", "AssetFile": "lib/tinymce/js/tinymce/tinymce.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "415254"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lOTcZxqVz8iqrI9BFx+QBG4pK4KRYH0gFUcExOiM4UE=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lOTcZxqVz8iqrI9BFx+QBG4pK4KRYH0gFUcExOiM4UE="}]}, {"Route": "lib/tinymce/js/tinymce/tinymce.min.k214tg81kt.js", "AssetFile": "lib/tinymce/js/tinymce/tinymce.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "415254"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lOTcZxqVz8iqrI9BFx+QBG4pK4KRYH0gFUcExOiM4UE=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 19:47:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k214tg81kt"}, {"Name": "integrity", "Value": "sha256-lOTcZxqVz8iqrI9BFx+QBG4pK4KRYH0gFUcExOiM4UE="}, {"Name": "label", "Value": "lib/tinymce/js/tinymce/tinymce.min.js"}]}]}