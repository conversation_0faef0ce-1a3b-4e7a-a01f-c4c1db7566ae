﻿using Admin.TaskDotNet.Dtos;
using TaskDotNet.Api_s.Controllers;
using TaskDotNet.Models;

namespace TaskDotNet.Api_s.Interfaces
{
    public interface IMovingApiService
    {
        Task<Activity> CreateMovingActivityAsync(MovingDto dto, string lang);
        Task<Activity> AddInventoryToMovingAsync(AddInventoryWithMovingRequest request);
        Task<Activity> Create(Activity activity);
        Task Update(Activity activity);

    }
}
