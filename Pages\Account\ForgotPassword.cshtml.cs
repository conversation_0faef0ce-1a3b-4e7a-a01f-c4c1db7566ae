﻿// Licensed to the .NET Foundation under one or more agreements.
// The .NET Foundation licenses this file to you under the MIT license.
#nullable disable

using Comman.Services.Interfaces;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.WebUtilities;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Text;
using TaskDotNet.Helper;
using TaskDotNet.Models;

namespace TaskDotNet.Areas.Identity.Pages.Account
{
    public class ForgotPasswordModel : PageModel
    {
        private readonly UserManager<Partner> _userManager;
        private readonly IMailService _emailSender;
        private readonly IEmailHtmlTemplateService _emailHtmlTemplateService;

        public ForgotPasswordModel(UserManager<Partner> userManager, IMailService emailSender, IEmailHtmlTemplateService emailHtmlTemplateService)
        {
            _userManager = userManager;
            _emailSender = emailSender;
            _emailHtmlTemplateService = emailHtmlTemplateService;
        }

        /// <summary>
        ///     This API supports the ASP.NET Core Identity default UI infrastructure and is not intended to be used
        ///     directly from your code. This API may change or be removed in future releases.
        /// </summary>
        [BindProperty]
        public InputModel Input { get; set; }

        /// <summary>
        ///     This API supports the ASP.NET Core Identity default UI infrastructure and is not intended to be used
        ///     directly from your code. This API may change or be removed in future releases.
        /// </summary>
        public class InputModel
        {
            /// <summary>
            ///     This API supports the ASP.NET Core Identity default UI infrastructure and is not intended to be used
            ///     directly from your code. This API may change or be removed in future releases.
            /// </summary>
            [Required]
            [EmailAddress]
            public string Email { get; set; }
        }

        public IActionResult OnGetAsync()
        {
            return Page();
        }
        public async Task<IActionResult> OnPostAsync()
        {
            if (ModelState.IsValid)
            {
                var user = await _userManager.FindByEmailAsync(Input.Email);
                if (user == null || !(await _userManager.IsEmailConfirmedAsync(user)))
                {
                    // Don't reveal that the user does not exist or is not confirmed
                    return RedirectToPage("./ForgotPasswordConfirmation");
                }

                var code = await _userManager.GeneratePasswordResetTokenAsync(user);
                code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
                var callbackUrl = Url.Page(
                    "/Account/ResetPassword",
                    pageHandler: null,
                    values: new { email = Input.Email, code },
                    protocol: Request.Scheme);
                
                var lang = CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
                string body = _emailHtmlTemplateService.GetResetPasswordemplate(user.Email, lang, callbackUrl);
                MailRequest mailRequest = new MailRequest
                {
                    ToEmail = user.Email,
                    Subject = "Task Net",
                    Body = body,

                };
                await _emailSender.SendEmailAsync(mailRequest, default);

                return RedirectToPage("./ForgotPasswordConfirmation");
            }

            return Page();
        }
    }
}
