<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="DisclaimerTitle" xml:space="preserve">
    <value>Disclaimer</value>
  </data>
  <data name="OnlineOffersTitle" xml:space="preserve">
    <value>Online Offers and Content</value>
  </data>
  <data name="OnlineOffersContent" xml:space="preserve">
    <value>The operator of the TaskDotNet.ch platforms assumes no liability for the accuracy, completeness, or quality of the information provided. Any claims arising from the use or non-use of the offered services are excluded unless there is proven intentional or gross negligence.The operator reserves the right to make changes to individual services or the entire offering without prior notice, or to temporarily or permanently discontinue publication.</value>
  </data>
  <data name="ExternalLinksTitle" xml:space="preserve">
    <value>External Links and References</value>
  </data>
  <data name="ExternalLinksContent" xml:space="preserve">
    <value>The operator accepts no responsibility for the content of external websites that are accessible via links and lie outside her area of responsibility. Liability applies only if the operator had knowledge of illegal content and it was technically possible and reasonable to prevent its use. At the time of linking, the linked pages were checked for illegal content, and no such content was apparent. The operator expressly distances herself from any later changes to the content of the linked pages.</value>
  </data>
  <data name="ValidityJurisdictionTitle" xml:space="preserve">
    <value>Validity and Jurisdiction</value>
  </data>
  <data name="ValidityJurisdictionContent" xml:space="preserve">
    <value>By using the platforms, the user accepts the General Terms and Conditions (GTC) and this disclaimer as part of the offering. Should individual clauses of this text not comply with the applicable legal situation, the remaining parts remain unaffected and valid.</value>
  </data>
  <data name="CourtJurisdiction" xml:space="preserve">
    <value>Jurisdiction is Zurich, Switzerland.</value>
  </data>
</root>
