﻿@page
@model RegisterConfirmationModel
@{
    ViewData["Title"] = "Register confirmation";
}

<div class="container-xxl">
    <div class="authentication-wrapper authentication-basic container-p-y d-flex align-items-center justify-content-center" style=" height: 100vh; ">
        <div class="authentication-inner" style="max-width:440px">
            <div class="card text-center">
                <div class="card-body px-10">
                    <!-- Logo -->
                    <div class="justify-content-center mb-3">
                        <div class="img"> <img src="~/Dashboard/assets/img/logo.png" style="width: 180px;" alt="logo"></div>
                    </div>
                    <!-- /Logo -->
                    <h3 class="mb-5" style="color:#6d9719;font-weight:bold">@SharedLocalizer["VerifyAccount"]</h3>
                    <p class="mb-4 fs-5 text-black">@SharedLocalizer["ConfirmMessage"]</p>

                    <form class="mb-3" method="post">
                        <input type="hidden" asp-for="Email" />
                        <div class="mb-7" style="text-align:left;">
                            <label for="Code" class="form-label text-black fs-5">@SharedLocalizer["ConfirmationCode"]</label>
                            <input asp-for="Code" class="form-control fs-5" placeholder="000000" />
                        </div>

                        <div class="mb-3 row justify-content-around">
                            <a asp-controller="Account" asp-action="login" class="btn btn-lg btn-outline-secondary py-2" style="border-radius: 0; width:40%">@SharedLocalizer["Cancel"]</a>
                            <input class="btn btn-lg py-2" style="border-radius: 0; background-color: #28314E; color: white;width:40%" value="@SharedLocalizer["Confirm"]" type="submit" />
                        </div>
                    </form>
                    <!-- Validation Summary -->
                    <div asp-validation-summary="All" class="text-danger"></div>

                </div>
            </div>
            <!-- /Verify -->
        </div>
    </div>
</div>


