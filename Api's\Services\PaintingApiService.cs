﻿using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Api_s.Interfaces;
using TaskDotNet.Models;

namespace TaskDotNet.Api_s.Services
{
    public class PaintingApiService : IPaintingApiService
    {

        private readonly ApplicationDbContext context;
        public PaintingApiService(ApplicationDbContext context)
        {
            this.context = context;
        }

        public async Task<Activity> Create(Activity activity)
        {
            await context.Activities.AddAsync(activity);
            context.SaveChanges();
            return activity;
        }
        public async Task Update(Activity activity)
        {
            context.Activities.Update(activity);
            await context.SaveChangesAsync();
        }
    }
}
