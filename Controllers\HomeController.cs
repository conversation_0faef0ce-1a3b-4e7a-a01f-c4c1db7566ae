﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Helper.Filters;
using TaskDotNet.Models;
using TaskDotNet.ViewModels;

namespace TaskDotNet.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly ApplicationDbContext _context;


        public HomeController(ILogger<HomeController> logger, ApplicationDbContext context)
        {
            _logger = logger;
            _context = context;
        }
        [Authorize(Roles = "Partner")]
        [ServiceFilter(typeof(ValidatePartnerAccountStatusFilter))]
        public async Task<IActionResult> IndexAsync(string? Message = null)
        {

            TempData["ModelMessage"] = Message;

            return View();
        }
    }
}
