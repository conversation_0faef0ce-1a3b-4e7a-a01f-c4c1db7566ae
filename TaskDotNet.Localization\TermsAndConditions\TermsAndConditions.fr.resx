<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="TermsAndConditionsTitle" xml:space="preserve">
    <value>Conditions générales (CG)</value>
  </data>
  <data name="TermsAndConditionsIntro" xml:space="preserve">
    <value>Les présentes conditions générales régissent les relations juridiques entre Lightsoft GmbH et ses clients. Par souci de simplification, le terme « contrat/ » est utilisé dans ces conditions, indépendamment du type de prestation fournie.</value>
  </data>
  <data name="ContractDefinitionTitle" xml:space="preserve">
    <value>Définition du contrat</value>
  </data>
  <data name="ContractDefinitionText" xml:space="preserve">
    <value>Le terme « contrat » désigne ici le consentement du client à utiliser la plateforme TaskDotNet afin de publier son besoin en un service spécifique et de recevoir des offres de prestataires potentiels. De même, les partenaires enregistrent leurs entreprises sur cette plateforme pour interagir avec les demandes des clients et entrer en contact avec eux. Cette collaboration, par l'enregistrement ou la publication d'une demande, constitue un contrat contraignant entre l'utilisateur et la plateforme.</value>
  </data>
  <data name="ApplicableConditionsTitle" xml:space="preserve">
    <value>Conditions applicables</value>
  </data>
  <data name="ApplicableConditionsText" xml:space="preserve">
    <value>Les présentes conditions générales de Lightsoft GmbH s'appliquent à toutes les personnes qui s'enregistrent en tant que partenaires afin de traiter les demandes des clients via cette plateforme. L'enregistrement est considéré comme un contrat entre l'entreprise et le partenaire, qui entre en vigueur dès l'approbation de l'enregistrement du partenaire par Lightsoft sur la plateforme.</value>
  </data>
  <data name="ContractValidityTitle" xml:space="preserve">
    <value>Validité du contrat</value>
  </data>
  <data name="ContractValidityText" xml:space="preserve">
    <value>Si une disposition des présentes conditions générales est totalement ou partiellement invalide ou nulle, la validité du contrat et des autres dispositions n'en sera pas affectée. La disposition invalide sera remplacée par une règle se rapprochant le plus possible, dans les limites légales, de l'objectif juridique et économique de la disposition initiale.</value>
  </data>
  <data name="JurisdictionTitle" xml:space="preserve">
    <value>For juridique</value>
  </data>
  <data name="JurisdictionText" xml:space="preserve">
    <value>Pour tout litige découlant du présent contrat, les tribunaux suisses sont seuls compétents, sauf dispositions légales impératives contraires. Le droit suisse est exclusivement applicable à toutes les dispositions contractuelles, y compris la soumission de demandes de services par les clients via la plateforme et leur acceptation par les partenaires par l'achat via TaskDotNet.</value>
  </data>
  <data name="ConditionsValidityTitle" xml:space="preserve">
    <value>Validité des conditions</value>
  </data>
  <data name="ConditionsValidityText" xml:space="preserve">
    <value>Les présentes conditions générales entrent en vigueur le 1er juin 2024 et perdent leur validité dès la publication d'une nouvelle version sur la plateforme. Les confirmations, déclarations ou réclamations écrites sont juridiquement contraignantes si elles sont transmises par courrier, e-mail, SMS ou WhatsApp.</value>
  </data>
  <data name="ContractingPartiesTitle" xml:space="preserve">
    <value>Parties contractantes</value>
  </data>
  <data name="ContractingPartiesText" xml:space="preserve">
    <value>Les parties contractantes au sens des présentes conditions générales sont à la fois l'invité (client ou partenaire) et la société Lightsoft GmbH.</value>
  </data>
  <data name="ScopeOfServicesTitle" xml:space="preserve">
    <value>Étendue des prestations</value>
  </data>
  <data name="ScopeOfServicesText" xml:space="preserve">
    <value>L'étendue des prestations convenues résulte de la commande individuelle publiée par le client. Les commandes dans les domaines du transport, du nettoyage, de la peinture, de l'enduisage et d'autres travaux artisanaux courants doivent être saisies exclusivement via la plateforme TaskDotNet.com.</value>
  </data>
  <data name="OrderRulesTitle" xml:space="preserve">
    <value>Règles relatives à la saisie et à la réservation des commandes</value>
  </data>
  <data name="OrderRulesText" xml:space="preserve">
    <value>Le client qui publie une commande sur la plateforme, ainsi que le partenaire qui réserve et achète cette commande, sont considérés comme les parties contractuelles responsables au sens des présentes conditions.</value>
  </data>
  <data name="PersonalDataEntryTitle" xml:space="preserve">
    <value>Saisie des données personnelles du client (le donneur d'ordre)</value>
  </data>
  <data name="PersonalDataEntryText" xml:space="preserve">
    <value>Le donneur d'ordre est tenu de fournir toutes les informations pertinentes concernant la demande ainsi que ses coordonnées de base de manière complète et véridique. Les coordonnées de base comprennent au minimum le nom complet, le code postal, le lieu de résidence et une adresse e-mail valide. D'autres données personnelles, comme le numéro de téléphone et le nom de la rue, facilitent généralement la communication et le traitement. Toutefois, Lightsoft GmbH comprend que certains donneurs d'ordre peuvent ne pas souhaiter fournir ces informations pour des raisons personnelles. La plateforme informe de manière transparente le partenaire concerné, pour chaque demande, des données personnelles fournies par le donneur d'ordre. Le partenaire décide de manière autonome s'il souhaite accepter ou refuser la demande en fonction des informations disponibles. La plateforme vérifie au minimum la validité de l'adresse e-mail fournie. Si cette condition n'est pas remplie, la demande est considérée comme incomplète et ne sera pas traitée. La vérification des coordonnées est effectuée dans l'intérêt des partenaires, qui fournissent des services en contrepartie de la réception de demandes, et vise à éviter les demandes invalides ou inexploitables.</value>
  </data>
  <data name="PartnerRightsDisclaimerTitle" xml:space="preserve">
    <value>Exclusion de responsabilité et droits du partenaire</value>
  </data>
  <data name="PartnerRightsDisclaimerText" xml:space="preserve">
    <value>La société Lightsoft GmbH décline toute responsabilité quant à l'obtention, par le partenaire, du consentement du client pour exécuter le service demandé ou à l'établissement d'une communication entre les parties. La décision finale concernant l'acceptation d'une offre de prix et le choix du prestataire revient exclusivement au client. Le partenaire n'a aucun droit au remboursement du prix d'achat d'une demande si le consentement du client n'a pas été obtenu ou si aucun contact avec le client n'a pu être établi. Il est également interdit au partenaire de remettre en question l'exactitude des données fournies par le client. La plateforme attache une grande importance à la vérification de la validité de l'adresse e-mail du client avant de rendre la demande accessible aux partenaires.</value>
  </data>
  <data name="OrderConfirmationTitle" xml:space="preserve">
    <value>Confirmation de commande et responsabilité du client</value>
  </data>
  <data name="OrderConfirmationText" xml:space="preserve">
    <value>Pour confirmer une commande, le client doit répondre à l'e-mail envoyé par la plateforme lors de la saisie de la commande. En l'absence de réponse, la commande est considérée comme incomplète. Dans ce cas, le client n'a droit à aucun service et ne peut en aucun cas faire de déclarations concernant la plateforme susceptibles de nuire à la réputation ou aux intérêts de l'entreprise.</value>
  </data>
  <data name="OrderEntryTimesTitle" xml:space="preserve">
    <value>Horaires de saisie des commandes</value>
  </data>
  <data name="OrderEntryTimesText" xml:space="preserve">
    <value>La plateforme TaskDotNet est disponible 24h/24 et 7j/7 pour la saisie des commandes. La validité de l'adresse e-mail est vérifiée à chaque commande afin de garantir une communication fiable.</value>
  </data>
  <data name="OrderForwardingTitle" xml:space="preserve">
    <value>Transmission des demandes aux partenaires</value>
  </data>
  <data name="OrderForwardingText" xml:space="preserve">
    <value>Une fois qu'une demande a été saisie avec succès, elle est immédiatement transmise aux partenaires enregistrés. Les quatre premiers partenaires ont le droit de réserver et d'acheter la demande moyennant des frais. Par la suite, la demande est clôturée et n'est plus affichée. Si le client souhaite retirer ou annuler sa demande après sa publication et son achat par des partenaires, il assume l'entier responsabilité des obligations financières qui en résultent — notamment tout droit au remboursement que les partenaires pourraient faire valoir au titre des paiements effectués pour la demande. La société Lightsoft GmbH décline toute responsabilité à cet égard envers les partenaires.</value>
  </data>
  <data name="PartnerRightsTitle" xml:space="preserve">
    <value>Droits et obligations des partenaires</value>
  </data>
  <data name="PartnerRightsText" xml:space="preserve">
    <value>Lors de l'achat d'une demande, le partenaire reçoit tous les détails de la commande, y compris les coordonnées du client – au minimum l'adresse e-mail vérifiée. Après l'achat, le partenaire n'est pas autorisé à annuler la demande ni à réclamer un remboursement du montant payé.</value>
  </data>
  <data name="PartnerCustomerCoordinationTitle" xml:space="preserve">
    <value>Coordination entre le partenaire et le client</value>
  </data>
  <data name="PartnerCustomerCoordinationText" xml:space="preserve">
    <value>L'accord concernant l'exécution de la prestation, y compris d'éventuelles visites sur site, est conclu exclusivement et directement entre le partenaire et le client, en dehors de la plateforme TaskDotNet. Ces accords sont contraignants pour les deux parties. Le rôle de la plateforme se limite uniquement à la réception et à la transmission de la demande.</value>
  </data>
  <data name="PricesCurrencyTitle" xml:space="preserve">
    <value>Prix et devise</value>
  </data>
  <data name="PricesCurrencyText" xml:space="preserve">
    <value>Les prix des commandes sont affichés clairement et de manière transparente aux partenaires. Le règlement s'effectue exclusivement en francs suisses (CHF).</value>
  </data>
  <data name="LiabilityTitle" xml:space="preserve">
    <value>Responsabilité et indemnisation</value>
  </data>
  <data name="LiabilityText" xml:space="preserve">
    <value>Le client assume l'entier responsabilité de l'exactitude et de l'exhaustivité des données saisies lors de la passation de la commande. Le partenaire est responsable de la soumission d'une offre de prix appropriée ainsi que de l'exécution correcte et ponctuelle du service convenu sans causer de dommages au client. Le partenaire exécutant est seul responsable envers le client de tous les dommages ou pertes résultant de son comportement, de celui de ses employés ou d'autres tiers mandatés. Ni ces personnes ni le partenaire lui-même ne peuvent tenir Lightsoft GmbH responsable, directement ou indirectement, pour des dommages ou des défauts d'exécution. Lightsoft GmbH décline expressément toute responsabilité juridique ou déclaration en cas de vol ou de dommages aux matériaux ou biens liés au service. La protection et l'assurance de ces objets ainsi que toute éventuelle indemnisation relèvent exclusivement de la responsabilité du partenaire exécutant.</value>
  </data>
</root>
