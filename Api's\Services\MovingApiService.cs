﻿using Admin.TaskDotNet.Dtos;
using AutoMapper;
using TaskDotNet.Comman.DataAccess;
using Comman.Services.Interfaces;
using System.Text;
using TaskDotNet.Api_s.Controllers;
using TaskDotNet.Api_s.Interfaces;
using TaskDotNet.Helper;
using TaskDotNet.Models;
using Humanizer;
using Microsoft.Extensions.Localization;
using TaskDotNet.Localization;
using System.Globalization;
using Comman.Helper.Extensions;

namespace TaskDotNet.Api_s.Services
{

    public class MovingApiService : IMovingApiService
    {
        private readonly ApplicationDbContext context;
        private readonly IMapper _mapper;
        private readonly IMailService _mailService;
        private readonly OrderNumberService _orderNumberService;
        private readonly IEmailHtmlTemplateService _emailHtmlTemplateService;

        public MovingApiService(IMapper mapper, IMailService mailService,
                             ApplicationDbContext context, IEmailHtmlTemplateService emailHtmlTemplateService)
        {
            _mapper = mapper;
            _mailService = mailService;
            _orderNumberService = new OrderNumberService();
            this.context = context;
            _emailHtmlTemplateService = emailHtmlTemplateService;
        }

        public async Task<Activity> Create(Activity activity)
        {
            await context.Activities.AddAsync(activity);
            context.SaveChanges();
            return activity;
        }
        public async Task Update(Activity activity)
        {
            context.Activities.Update(activity);
            await context.SaveChangesAsync();
        }


        public async Task<Activity> CreateMovingActivityAsync(MovingDto dto, string lang)
        {
            var companyData = context.Company.Select(m => new { m.Offers_Move, m.Price_Move }).FirstOrDefault(x => true);

            var data = _mapper.Map<Activity>(dto);
            data.ActivityType = ActivityType.Moving;
            data.Preis = companyData.Price_Move;
            data.PaymentCount = companyData.Offers_Move;
            data.Source = "TaskDotNet";

            data = await Create(data);
            data.OrderNr = _orderNumberService.GenerateOrderNumber(data.ActivityType, data.Id);
            await Update(data);

            dto.OrderNr = data.OrderNr;

            await SendThankYouEmailAsync(data, lang);

            return data;
        }

        public async Task<Activity> AddInventoryToMovingAsync(AddInventoryWithMovingRequest request)
        {
            var companyData = context.Company.Select(m => new { m.Offers_Move, m.Price_Move }).FirstOrDefault(x => true);
            var activity = _mapper.Map<Activity>(request.MovingDto);

            var inventoryItems = _mapper.Map<List<ActivityInventoryItem>>(request.InventoryItems);

            activity.ActivityType = ActivityType.Moving;
            activity.Preis = companyData.Price_Move;
            activity.PaymentCount = companyData.Offers_Move;

            activity.InventoryItems = inventoryItems;
            activity.Source = "TaskDotNet";

            activity = await Create(activity);

            activity.OrderNr = _orderNumberService.GenerateOrderNumber(activity.ActivityType, activity.Id);
            await Update(activity);

            request.MovingDto.OrderNr = activity.OrderNr;
            await SendThankYouEmailAsync(activity, request.Lang);


            return activity;
        }


        private async Task SendThankYouEmailAsync(Activity data, string lang)
        {
            string body = _emailHtmlTemplateService.GetCustomerThankYouTemplate(data.Salute.GetDisplayName(), data.Name, lang);
            MailRequest mailRequest = new()
            {
                ToEmail = data.Email,
                Subject = "TaskDotNet",
                Body = body
            };
            await _mailService.SendEmailAsync(mailRequest, default);
        }
    }
}

