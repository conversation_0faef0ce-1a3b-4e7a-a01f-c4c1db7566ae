﻿@page
@model LoginModel

@{
    ViewData["Title"] = "Log in";
}



<!-- Content -->
<section style="height:100vh">
    <div>
        <div>
            <div class="d-flex justify-content-center align-items-center vh-100" style="background-color: lightblue;">
                <div class="row g-0" style="width:80%;height: 90%; background-color: white;">
                    <div class="col-md-6 d-flex align-items-center">
                        <div class="card-body text-black" style="padding:3.5rem">
                            <div class="w-100 text-center">
                                <img src="~/Dashboard/assets/img/logo.png" style="width:350px" />
                            </div>

                            <h1 class="mt-10 text-center" style="color:#2439b3;font-weight: 800;font-size: 35px;line-height: 1;">@SharedLocalizer["WelcomeBack"]</h1>
                            <p class="my-10 fs-4">@SharedLocalizer["LoginHeader"]</p>

                            <form id="formAuthentication" asp-controller="Account" asp-action="Login" class="mb-3 w-100">
                                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                                <div class="mb-3">
                                    <label for="Email" style="font-size:20px" class="form-label">@SharedLocalizer["Email"] </label>
                                    <input type="text" asp-for="Input.Email"
                                           class="form-control"
                                           placeholder="@SharedLocalizer["EnterYourEmail"]" />
                                </div>
                                <div class="mb-3 form-password-toggle">
                                    <div class="d-flex justify-content-between">
                                        <label class="form-label" style="font-size:20px" for="password">@SharedLocalizer["Password"]</label>
                                        <a href="auth-forgot-password-basic.html">
                                            <a style="font-size:18px;color:#2439b3" asp-controller="Account" asp-action="ForgotPassword">@SharedLocalizer["ForgotPassword"]?</a>
                                        </a>
                                    </div>
                                    <div class="input-group input-group-merge">
                                        <input asp-for="Input.Password"
                                               class="form-control"
                                               placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                                               aria-describedby="password" />
                                        <span class="input-group-text cursor-pointer"><i class="bx bx-hide"></i></span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" asp-for="Input.RememberMe" type="checkbox" />
                                        <label class="form-check-label" asp-for="Input.RememberMe"> @SharedLocalizer["Remember Me"] </label>
                                    </div>
                                </div>

                                <div class="mb-3 mt-10">
                                    <input style="background-color: #2439b3;color: white;font-weight: 800;font-size: 20px" class="btn btn-primary d-grid mx-auto w-50" value="@SharedLocalizer["Login"]" type="submit" />
                                </div>
                                <p class="text-center">
                                    <span>@SharedLocalizer["HaveAccount"]?</span>
                                    <a style="font-size:18px;color:#2439b3" asp-controller="Account" asp-action="Register">
                                        <span>@SharedLocalizer["Register here"]..</span>
                                    </a>
                                </p>
                            </form>
                        </div>
                    </div>
                    <div class="d-none d-md-block col-md-6 h-100">
                        <img src="~/Dashboard/assets/img/Login2.png"
                             alt="login form" class="" style="width:100%; height:100%;object-fit: fill;" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
