﻿@using Comman.Helper.Extensions
@model EmailToCustomerViewModel

@{
    ViewData["Title"] = "Send Message To The Customer";

    string currentLanguage = System.Globalization.CultureInfo.CurrentCulture.Name;

    var filePath = currentLanguage switch
    {
        "de-DE" => "de",
        "en-US" => "en",
        "it-IT" => "it",
        "fr-FR" => "fr",
        _ => "en"
    };

}

<style>
    .tox-promotion, .tox-statusbar__branding {
        display: none !important;
    }
</style>

<div class="content-wrapper">
    <!-- Content -->

    <div class="container-fluid flex-grow-1 ">
        <h3 class="fw-bold text-white py-3 mb-4"><a title="back" class="text-white" asp-action="ActivityDetails" asp-route-id="@Model.Activity.Id"><i class='bx bxs-left-arrow-circle' style="font-size:2.1rem"></i></a> @SharedLocalizer["SendMessageToTheCustomer"]</h3>

        <div class="row">
            <div class="col-md-12">

                <div class="card mb-4">
                    <!-- Account -->
                    <div class="card-body">
                        <div class="w-100">
                            <h3 class="text-primary border border-1 border-primary text-center mx-auto my-3 w-50">
                                @SharedLocalizer["NewRequest"]: #@Model.Activity.OrderNr
                            </h3>
                        </div>
                        <div class="">
                            <div class="row w-75 mx-auto">
                                <div class="col-md-3">
                                    <p>@SharedLocalizer["Category"]:</p>
                                    <p>@SharedLocalizer["Customer"]:</p>
                                </div>
                                <div class="col-md-3">
                                    <p class="text-black fw-bold">@Model.Activity.ActivityType.GetDisplayName()</p>
                                    <p class="text-black fw-bold">@Model.Activity.Name <br /> @Model.Activity.PostBox @Model.Activity.City </p>
                                </div>
                                <div class="col-md-3">
                                    <p>@SharedLocalizer["ExecutionDate"]:</p>
                                    <p>@SharedLocalizer["Email"]:</p>
                                    <p>@SharedLocalizer["Phone"]:</p>
                                </div>
                                <div class="col-md-3">
                                    <p class="text-black fw-bold">@Model.Activity.MovingDate.ToString("dd.MM.yyyy")</p>
                                    <p class="text-black fw-bold">@Model.Activity.Email</p>
                                    <p class="text-black fw-bold">@Model.Activity.Phone</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-primary" style="height: 3px"></div>
                        <div class="my-3 form-group">
                            <label for="Subject" class="form-label fs-5">@SharedLocalizer["Subject"]</label>
                            <input id="Subject" type="text" value="@Model.Subject" class="form-control" required />

                        </div>
                        <div class="form-group row">
                            <div class="col-md-12 my-3">
                                <textarea id="TextContent" class="form-control editor-text" rows="7"> @Model.TextContent</textarea>
                            </div>
                        </div>

                    </div>
                    <div class="w-100">
                        <button type="button" class="btn btn-lg btn-primary mx-auto d-block mb-5" id="SendTextsEmail">
                            @SharedLocalizer["Send"]
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- / Content -->
    </div>
</div>


@section Scripts {
    <script src="~/lib/tinymce/js/tinymce/tinymce.min.js"></script>
    <script>
        $(document).ready(function () {

            tinymce.init({
                selector: '.editor-text',
                plugins: [
                    'advlist', 'autolink', 'link', 'image', 'lists', 'charmap', 'preview', 'anchor', 'pagebreak',
                    'searchreplace', 'wordcount', 'visualblocks', 'visualchars', 'code', 'fullscreen', 'insertdatetime',
                    'media', 'table', 'emoticons', 'template', 'help'
                ],
                toolbar: 'undo redo | fontfamily fontsize | bold italic | alignleft aligncenter alignright alignjustify | ' +
                    'bullist numlist outdent indent | link | print preview fullscreen | ' +
                    'forecolor backcolor emoticons | help',
                menu: {
                    file: { title: 'File', items: ' preview | export print ' },
                    insert: { title: 'Insert', items: 'link charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents' },
                },
                menubar: 'file edit view insert format tools table',
                language: '@filePath',
                content_style: "body { line-height: 1; }",
            });

            $("#SendTextsEmail").on("click", async function (event) {
                // Prevent the form from submitting initially
                event.preventDefault();

                // Show the loader
                $('#loader').show();

                try {
                    // Get the content from TinyMCE editor
                    const textContent = tinyMCE.get('TextContent').getContent();
                    const subject = $('#Subject').val();
                    const email = '@Model.Activity.Email'; // Assuming the email field has an ID of 'Email'

                    // Validate inputs
                    if (!textContent || !subject || !email) {
                        throw new Error("Please fill out all fields.");
                    }

                    // Prepare the form data
                    const formData = {
                        Subject: subject,
                        TextContent: textContent,
                        Email: email
                    };

                    // Send the AJAX request
                    const response = await $.ajax({
                        type: "POST",
                        url: "/PurchasedActivites/EmailToCustomer",
                        contentType: "application/json",
                        data: JSON.stringify(formData),
                        headers: {
                            "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() // Add CSRF token
                        }
                    });

                    // Check the response
                    if (response.success) {
                        window.location.href = "/PurchasedActivites/Index";
                    } else {
                        alert(response.message); // Display error message
                    }
                } catch (error) {
                    console.error(error);
                    alert("An error occurred: " + error.message); // Provide feedback to the user
                } finally {
                    // Hide the loader regardless of success or failure
                    $('#loader').hide();
                }
            });

        });


    </script>

}