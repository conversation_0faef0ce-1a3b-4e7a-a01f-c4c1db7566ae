﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Globalization;
using TaskDotNet.Models;

namespace TaskDotNet.Helper.Filters
{
    public class ValidatePartnerAccountStatusFilter : IAsyncActionFilter
    {
        private readonly UserManager<Partner> _userManager;
        private const string AdminEmail = "<EMAIL>";

        public ValidatePartnerAccountStatusFilter(UserManager<Partner> userManager)
        {
            _userManager = userManager;
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            var partnerEmail = context.HttpContext.User.Identity?.Name;

            if (string.IsNullOrEmpty(partnerEmail))
            {
                await next();
                return;
            }

            var user = await _userManager.FindByEmailAsync(partnerEmail);
            if (user == null)
            {
                await next();
                return;
            }

            // Check partner data
            if (!await CheckForPartnerData(context, user))
            {
                context.Result = new RedirectToActionResult("Index", "Partner", null);
                return;
            }
            

            // Check if user is blocked and handle accordingly
            if (user.Status == PartnerStatus.Blocked)
            {
                var controllerName = context.RouteData.Values["controller"]?.ToString();
                var actionName = context.RouteData.Values["action"]?.ToString();

                // Skip filter logic for Home/Index
                if (controllerName == "Home" && actionName == "Index")
                {
                    await next();
                    return;
                }

                context.Result = new RedirectToActionResult("Index", "Home", new { Message = GetBlockedUserMessage(user) });
                return;
            }

            await next();
        }

        private string GetBlockedUserMessage(Partner user)
        {
            var culture = CultureInfo.CurrentUICulture.Name;

            return user.Evaluation == PartnerEvaluation.New
                ? GetLocalizedMessage(culture, "NewAccountMessage")
                : GetLocalizedMessage(culture, "SuspendedAccountMessage");
        }

        private static string GetLocalizedMessage(string culture, string messageKey)
        {
            var messages = new Dictionary<string, Dictionary<string, string>>
            {
                { "en-US", new Dictionary<string, string>
                    {
                        { "NewAccountMessage", "Please wait to receive an email from the administrator after your account has been activated." },
                        { "SuspendedAccountMessage", $"Your access to our platform has been restricted due to policy violations. If you believe this is a mistake or need more information, please contact us at {AdminEmail}." }
                    }
                },
                { "de-DE", new Dictionary<string, string>
                    {
                        { "NewAccountMessage", "Bitte warten Sie auf eine E-Mail vom Administrator, nachdem Ihr Konto aktiviert wurde." },
                        { "SuspendedAccountMessage", $"Ihr Zugang zu unserer Plattform wurde aufgrund von Richtlinienverstößen eingeschränkt. Wenn Sie glauben, dass dies ein Fehler ist oder weitere Informationen benötigen, kontaktieren Sie uns bitte unter {AdminEmail}." }
                    }
                },
                { "it-IT", new Dictionary<string, string>
                    {
                        { "NewAccountMessage", "Attendi di ricevere un'email dall'amministratore dopo che il tuo account è stato attivato." },
                        { "SuspendedAccountMessage", $"Il tuo accesso alla nostra piattaforma è stato limitato a causa di violazioni delle politiche. Se ritieni che si tratti di un errore o hai bisogno di ulteriori informazioni, contattaci all'indirizzo {AdminEmail}." }
                    }
                },
                { "fr-FR", new Dictionary<string, string>
                    {
                        { "NewAccountMessage", "Veuillez attendre de recevoir un e-mail de l'administrateur après l'activation de votre compte." },
                        { "SuspendedAccountMessage", $"Votre accès à notre plateforme a été restreint en raison de violations de la politique. Si vous pensez qu'il s'agit d'une erreur ou si vous avez besoin de plus d'informations, veuillez nous contacter à l'adresse {AdminEmail}." }
                    }
                }
            };

            // Fallback to English if culture not found
            if (!messages.ContainsKey(culture))
                culture = "en-US";

            return messages[culture].TryGetValue(messageKey, out var message)
                ? message
                : "Message not found.";
        }


        private async Task<bool> CheckForPartnerData(ActionExecutingContext context, Partner user)
        {
            var isCompletedData = context.HttpContext.Session.GetString("IsCompletedData");

            if (string.IsNullOrEmpty(isCompletedData))
            {
                context.HttpContext.Session.SetString("IsCompletedData", user.IsCompletedData.ToString());

                if (!user.IsCompletedData)
                {
                    return false;
                }
            }
            else if (bool.TryParse(isCompletedData, out bool completed) && !completed)
            {
                return false;
            }

            return true;
        }
    }
}
