﻿using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using System.ComponentModel.DataAnnotations;
using TaskDotNet.Helper;
using TaskDotNet.Models;

namespace Admin.TaskDotNet.Dtos
{
    public class PartnerDto
    {
        public Salute Salute { get; set; }
        [Required]
        public string CompanyName { get; set; }
        public string? PName { get; set; }
        [Required]
        public string PStreet { get; set; }
        [Required]
        public string PPostBox { get; set; }
        [Required]
        public string PCity { get; set; }
        [Required]
        public string Phone { get; set; }
        public string? Mobile { get; set; }
        public string? Website { get; set; }
        public string? PEmail { get; set; }
        public decimal? PSaldo { get; set; }
        public string? Language { get; set; }
        [Required]
        public DateTime StartDate { get; set; } = DateTime.Now.Date;
        [Required]
        public string Land { get; set; }
        public string? IBAN { get; set; }
        public string? Bank { get; set; }
        public string? Owner { get; set; }
        [Required]
        public string UID { get; set; }
        public bool IsCompletedData { get; set; }
        [ValidateNever]
        public PartnerActivities PartnerActivities { get; set; }
        [ValidateNever]
        public PartnerCountries PartnerCountries { get; set; }
    }
}
