﻿using AutoMapper;
using Comman.Helper.Extensions;
using Comman.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Api_s.Interfaces;
using TaskDotNet.Api_s.Responses;
using TaskDotNet.Api_s.Services;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.DTOs;
using TaskDotNet.Helper;
using TaskDotNet.Models;
using TaskDotNetal.Api_s.Models;

namespace TaskDotNet.Api_s
{
    [Route("api/[controller]")]
    [ApiController]
    public class CleaningController : ControllerBase
    {

        #region ctor
        private readonly IMapper mapper;
        private readonly IMailService mailService;
        private readonly IEmailHtmlTemplateService emailHtmlTemplateService;
        private readonly ICleaningApiService cleaningService;
        private readonly OrderNumberService _orderNumberService;
        private readonly ApplicationDbContext _context;

        public CleaningController(IMapper mapper, IMailService mailService, IEmailHtmlTemplateService emailHtmlTemplateService, ICleaningApiService cleaningService, ApplicationDbContext context)
        {
            this.mapper = mapper;
            this.mailService = mailService;
            this.emailHtmlTemplateService = emailHtmlTemplateService;
            this.cleaningService = cleaningService;
            _orderNumberService = new OrderNumberService();
            _context = context;
        }

        #endregion

        #region Create
        [HttpPost("CreateCleaning")]
        public async Task<IActionResult> CreateCleaning(CleaningDto dto, string lang)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new CustomResponse { Code = "400", Message = "Invalid request data." });
            }

            var companyData = await _context.Company.Select(m => new { m.Offers_Clean, m.Price_Clean }).FirstOrDefaultAsync();
            var kanton = _context.PostBoxCities.Where(m => m.PostBox == dto.PostBox)
                .Select(m => m.Kanton)
                .FirstOrDefault();

            dto.Kanton = kanton;
            var data = mapper.Map<Activity>(dto);
            data.ActivityType = ActivityType.Cleaning;
            data.Preis = companyData.Price_Clean;
            data.PaymentCount = companyData.Offers_Clean;
            data.Source = "TaskDotNet";

            data = await cleaningService.Create(data);

            data.OrderNr = _orderNumberService.GenerateOrderNumber(data.ActivityType, data.Id);
            dto.OrderNr = data.OrderNr;

            await cleaningService.Update(data);

            var ordersCount = await _context.OrdersCount.FirstOrDefaultAsync(m => m.WebsiteName == "TaskDotNet");
            ordersCount.T_Clean += 1;


            var notification = new ActivityNotification()
            {
                ActivityType = data.ActivityType,
                City = data.City,
                Name = data.Name,
                PostBox = data.PostBox,
                Salute = data.Salute,
                ExcuteDate = data.CleaningDate,
            };
            _context.ActivityNotifications.Add(notification);

            await _context.SaveChangesAsync();

            // Prepare the response object
            var response = new ActivityResponse
            {
                Code = "200",
                Status = "Success",
                Message = "Cleaning activity created successfully!",
                Data = data
            };

            string body = emailHtmlTemplateService.GetCustomerThankYouTemplate(data.Salute.GetDisplayName(), data.Name, lang);
            var mailRequest = new MailRequest
            {
                ToEmail = dto.Email,
                Subject = "TaskDotNet",
                Body = body
            };
            await mailService.SendEmailAsync(mailRequest, default);

            return Ok(response);


        }
        #endregion

    }
}
