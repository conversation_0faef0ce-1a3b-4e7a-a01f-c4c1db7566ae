<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="DisclaimerTitle" xml:space="preserve">
    <value>Esclusione di responsabilità</value>
  </data>
  <data name="OnlineOffersTitle" xml:space="preserve">
    <value>Offerte e contenuti online</value>
  </data>
  <data name="OnlineOffersContent" xml:space="preserve">
    <value>La gestora delle piattaforme TaskDotNet.ch non si assume alcuna responsabilità per l'accuratezza, la completezza o la qualità delle informazioni fornite. Qualsiasi pretesa derivante dall'uso o dal mancato uso dei servizi offerti è esclusa, salvo dolo o colpa grave dimostrata. La gestora si riserva il diritto di apportare modifiche a singoli servizi o all'intera offerta senza preavviso, oppure di sospendere temporaneamente o definitivamente la pubblicazione.</value>
  </data>
  <data name="ExternalLinksTitle" xml:space="preserve">
    <value>Link e riferimenti esterni</value>
  </data>
  <data name="ExternalLinksContent" xml:space="preserve">
    <value>La gestora non si assume alcuna responsabilità per i contenuti dei siti esterni accessibili tramite link e che non rientrano nella propria responsabilità. La responsabilità sussiste solo se la gestora era a conoscenza di contenuti illegali ed era tecnicamente possibile e ragionevole impedirne l'uso. Al momento della creazione dei link, le pagine collegate sono state controllate e non presentavano contenuti illegali. La gestora prende espressamente le distanze da eventuali modifiche successive ai contenuti di tali pagine collegate.</value>
  </data>
  <data name="ValidityJurisdictionTitle" xml:space="preserve">
    <value>Validità e foro competente</value>
  </data>
  <data name="ValidityJurisdictionContent" xml:space="preserve">
    <value>Utilizzando le piattaforme, l'utente accetta le Condizioni Generali (CG) e la presente esclusione di responsabilità come parte integrante dell'offerta. Se alcune disposizioni di questo testo non sono conformi alla normativa vigente, le restanti parti rimangono valide.</value>
  </data>
  <data name="CourtJurisdiction" xml:space="preserve">
    <value>Il foro competente è Zurigo, Svizzera.</value>
  </data>
</root>
