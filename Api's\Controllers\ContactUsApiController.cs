﻿using Admin.TaskDotNet.Dtos;
using AutoMapper;
using TaskDotNet.Comman.DataAccess;
using Comman.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using TaskDotNet.Helper;
using TaskDotNet.Models;
using TaskDotNetal.Api_s.Models;
using Microsoft.EntityFrameworkCore;
using System.Net.Http;
using System.Text.Json;

namespace TaskDotNet.Api_s.Controllers
{

    [Route("api/[controller]")]
    [ApiController]
    public class ContactUsApiController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly IMailService _mailService;
        private readonly IEmailHtmlTemplateService _emailHtmlTemplateService;
        private readonly ApplicationDbContext _context;
        private readonly HttpClient _httpClient;

        public ContactUsApiController(
            IMapper mapper,
            IMailService mailService,
            IEmailHtmlTemplateService emailHtmlTemplateService,
            ApplicationDbContext context,
            HttpClient httpClient)
        {
            _mapper = mapper;
            _mailService = mailService;
            _emailHtmlTemplateService = emailHtmlTemplateService;
            _context = context;
            _httpClient = httpClient;
        }

        [HttpPost("ContactUs")]
        public async Task<IActionResult> CreateAsync([FromBody] ContactUsDto dto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new CustomResponse { Code = "400", Message = "Invalid request data." });
            }

            if (!await IsRecaptchaValidAsync(dto.RecaptchaToken))
            {
                return BadRequest(new CustomResponse { Code = "400", Message = "Invalid Recaptcha." });
            }

            var companyEmail = await _context.Company.Select(x => x.Email).FirstOrDefaultAsync();

            var contactUsEntity = _mapper.Map<ContactUs>(dto);
            _context.Add(contactUsEntity);
            await _context.SaveChangesAsync();

            var response = new CustomResponse
            {
                Code = "200",
                Status = "Success",
                Message = "Contact request created successfully!"
            };

            var thankYouTemplate = _emailHtmlTemplateService.GetThankYouTemplate("",dto.Name,dto.lang);
            var companyEmailBody = $@"
                <div style=""font-family: Arial, sans-serif; font-size: 14px; color: #333;"">
                    <h2>New Contact Message</h2>
                    <p><strong>From:</strong> {dto.Name}</p>
                    <p><strong>Email:</strong> <a href=""mailto:{dto.Email}"">{dto.Email}</a></p>
                    <p><strong>Phone:</strong> {dto.PhoneNumber}</p>
                    <hr style=""border: 1px solid #ccc;"" />
                    <p><strong>Message:</strong></p>
                    <div style=""background-color: #f9f9f9; border: 1px solid #ddd; padding: 15px; margin-top: 10px; white-space: pre-wrap;"">
                        {dto.Message}
                    </div>
                    <hr style=""border: 1px solid #ccc; margin-top: 30px;"" />
                    <p style=""font-size: 12px; color: #999;"">This message was sent from the contact form on TaskDotNet.</p>
                </div>";

            await _mailService.SendEmailAsync(companyEmail, $"New Contact (TaskDotNet) From {dto.Name}", companyEmailBody);
            await _mailService.SendEmailAsync(dto.Email, "TaskDotNet", thankYouTemplate);
            return Ok(response);
        }

        private async Task<bool> IsRecaptchaValidAsync(string token)
        {
            var secretKey = "6LcmWC8rAAAAAIcbV-Y_fghRBuMVu5gUr48i8Wk0";
            var url = $"https://www.google.com/recaptcha/api/siteverify?secret={secretKey}&response={token}";
            var response = await _httpClient.GetStringAsync(url);

            var recaptchaResult = JsonSerializer.Deserialize<RecaptchaResponseObj>(response, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return recaptchaResult?.Success == true;
        }

    }
    class RecaptchaResponseObj
    {
        public bool Success { get; set; }
        public string Challenge_ts { get; set; }
        public string Hostname { get; set; }
    }

}
