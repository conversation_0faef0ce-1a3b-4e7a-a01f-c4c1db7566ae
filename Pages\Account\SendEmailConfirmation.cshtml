﻿@page
@model SendEmailConfirmationModel
@{
    ViewData["Title"] = "Send email confirmation";
}


<div class="container-xxl">
    <div class="authentication-wrapper authentication-basic container-p-y d-flex align-items-center justify-content-center" style=" height: 100vh; ">
        <div class="authentication-inner w-50">
            <div class="card text-center">
                <div class="card-header d-flex align-items-baseline p-3" style="background-color: #145960;">
                    <div class="col-4">
                        <img class="w-100" src="~/dashboard/assets/img/logo.png" />
                    </div>
                    <div class="col-8">
                        <h2 class="text-white">@SharedLocalizer["EmailConfirmationMessage1"]</h2>
                        <p class="text-white">@SharedLocalizer["EmailConfirmationMessage2"]</p>
                    </div>
                </div>
                <div class="card-body">
                    <img class="w-50" src="~/dashboard/assets/img/illustrations/in-prograss.png" />
                    <h5 class="card-title fs-2">@SharedLocalizer["EmailConfirmationMessage3"]</h5>
                    <p class="card-text fs-3">
                        @SharedLocalizer["EmailConfirmationMessage4"] <span class="card-text fs-2" style="color:#2439b3;font-weight:bold">@Model.Email</span>
                        <br />
                        @SharedLocalizer["EmailConfirmationMessage5"]
                    </p>
                </div>
                <form method="post">
                    <div class="card-footer d-flex justify-content-end">
                        <input type="hidden" asp-for="Email" />
                        <a asp-controller="Account" asp-action="login" class="btn btn-lg btn-outline-secondary mx-3">@SharedLocalizer["Cancel"]</a>
                        <button type="submit" class="btn btn-lg btn-primary">@SharedLocalizer["SendEmail"]</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

