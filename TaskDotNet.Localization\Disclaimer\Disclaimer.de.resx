<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="DisclaimerTitle" xml:space="preserve">
    <value>Haftungsausschluss</value>
  </data>
  <data name="OnlineOffersTitle" xml:space="preserve">
    <value>Onlineangebote und Inhalte</value>
  </data>
  <data name="OnlineOffersContent" xml:space="preserve">
    <value>Die Betreiberin der Plattformen TaskDotNet.ch übernimmt keine Gewähr für die Richtigkeit, Vollständigkeit oder Qualität der bereitgestellten Informationen. Ansprüche aus der Nutzung oder Nichtnutzung der angebotenen Dienstleistungen sind ausgeschlossen, es sei denn, es liegt nachweislich vorsätzliches oder grob fahrlässiges Verschulden vor. Die Betreiberin behält sich vor, ohne vorherige Ankündigung Änderungen an einzelnen Dienstleistungen oder am gesamten Angebot vorzunehmen oder die Veröffentlichung vorübergehend oder endgültig einzustellen.</value>
  </data>
  <data name="ExternalLinksTitle" xml:space="preserve">
    <value>Externe Links und Verweise</value>
  </data>
  <data name="ExternalLinksContent" xml:space="preserve">
    <value>Die Betreiberin übernimmt keine Verantwortung für Inhalte externer Webseiten, die über Links erreichbar sind und ausserhalb ihres Verantwortungsbereichs liegen. Eine Haftung besteht nur, wenn die Betreiberin von rechtswidrigen Inhalten Kenntnis hatte und es ihr technisch möglich und zumutbar gewesen wäre, deren Nutzung zu verhindern.Zum Zeitpunkt der Verlinkung wurden die verlinkten Seiten auf rechtswidrige Inhalte überprüft, und es waren keine solchen Inhalte erkennbar. Die Betreiberin distanziert sich ausdrücklich von nachträglichen Änderungen der Inhalte verlinkter Seiten.</value>
  </data>
  <data name="ValidityJurisdictionTitle" xml:space="preserve">
    <value>Wirksamkeit und Gerichtsstand</value>
  </data>
  <data name="ValidityJurisdictionContent" xml:space="preserve">
    <value>Mit der Nutzung der Plattformen akzeptiert der Benutzer die vorliegenden Allgemeinen Geschäftsbedingungen (AGB) und diesen Haftungsausschluss als Bestandteil des Angebots. Sollten einzelne Formulierungen dieses Textes nicht der geltenden Rechtslage entsprechen, bleiben die übrigen Teile davon unberührt und weiterhin gültig.</value>
  </data>
  <data name="CourtJurisdiction" xml:space="preserve">
    <value>Gerichtsstand ist Zürich, Schweiz.</value>
  </data>
</root>
