﻿using PostFinanceCheckout.Model;
using TaskDotNet.Models;

namespace TaskDotNet.Services.Interfaces
{
    public interface IPostFinancePaymentService
    {
        Transaction CreateTransaction(Partner user, decimal amount, string url);
        Transaction CreateTransaction(Partner user, decimal amount, string baseUrl, string itemName, string successUrl, string failedUrl);
        string GetPaymentPageUrl(long? transactionId);
        Transaction GetTransaction(long transactionId);
    }
}