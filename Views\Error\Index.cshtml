﻿  @model ErrorViewModel

@{
    Layout = "/Views/Shared/_LoginLayout.cshtml";
    ViewData["Title"] = Model.Title;
}

@{
    var imgSrc = "";

    if (Model.StatusCode == 401 || Model.StatusCode == 403)
    {
        imgSrc = "/dashboard/assets/img/illustrations/girl-with-laptop-light.png";
    }
    else if (Model.StatusCode == 404)
    {
        imgSrc = "/dashboard/assets/img/illustrations/page-misc-error-light.png";
    }
    else
    {
        imgSrc = "/dashboard/assets/img/illustrations/page-misc-error-light2.png";
    }
}

<style>
    .misc-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        min-height: calc(100vh - 1.5rem*2);
        text-align: center
    }
</style>

<!-- Content -->
<!-- Error -->
<div class="container-xxl container-p-y">
    <div class="misc-wrapper">
        <h1 class="mb-2 mx-2" style="line-height: 6rem;font-size: 6rem;">@Model.StatusCode</h1>
        <h4 class="mb-2 mx-2">@Model.Title</h4>
        <p class="mb-6 mx-2">@Model.Message</p>
        <a asp-controller="Home" asp-action="Index" class="btn btn-primary">@SharedLocalizer["Back to Home"]</a>
        <div class="mt-6">
            <img src="@imgSrc" alt="page-misc-error-light" width="500" class="img-fluid">
        </div>
    </div>
</div>
<!-- /Error -->
<!-- / Content -->