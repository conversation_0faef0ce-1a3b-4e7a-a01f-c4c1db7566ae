﻿using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Api_s.Interfaces;
using TaskDotNet.Models;

namespace TaskDotNet.Api_s.Services
{
    public class WorkersApiService : IWorkersApiService
    {
        private readonly ApplicationDbContext context;
        public WorkersApiService(ApplicationDbContext context)
        {
            this.context = context;
        }

        public async Task<Activity> Create(Activity activity)
        {
            await context.Activities.AddAsync(activity);
            context.SaveChanges();
            return activity;
        }

        public void Update(Activity activity)
        {
            context.Activities.Update(activity);
            context.SaveChanges();
        }
    }
}
