/* _content/TaskDotNet/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://docs.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-agvfp3lfxp] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-agvfp3lfxp] {
  color: #0077cc;
}

.btn-primary[b-agvfp3lfxp] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-agvfp3lfxp], .nav-pills .show > .nav-link[b-agvfp3lfxp] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-agvfp3lfxp] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-agvfp3lfxp] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-agvfp3lfxp] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-agvfp3lfxp] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-agvfp3lfxp] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
