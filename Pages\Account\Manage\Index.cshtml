﻿@page
@model IndexModel
@{
    ViewData["Title"] = "Settings";
    Layout = "/Views/Shared/_Layout.cshtml";
}


<!-- Content -->

<div class="container-xxl">
    <div class="container-p-y">
        <partial name="_StatusMessage" for="StatusMessage" />
        <div>
            <div class="card">
                <div class="card-body">
                    <h4 class="mb-2">@SharedLocalizer["Change password"]</h4>
                    <form method="post" class="mb-3">
                        <div asp-validation-summary="All" class="text-danger"></div>

                        <div class="mb-3 form-password-toggle">
                            <div class="d-flex justify-content-between">
                                <label class="form-label" asp-for="Input.OldPassword">@SharedLocalizer["Old Paassword"]</label>
                                <a href="auth-forgot-password-basic.html">
                                </a>
                            </div>
                            <div class="input-group input-group-merge">
                                <input type="password" asp-for="Input.OldPassword"
                                       class="form-control"
                                       placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                                       aria-describedby="password" />
                                <span class="input-group-text cursor-pointer"><i class="bx bx-hide"></i></span>
                            </div>
                        </div>

                        <div class="mb-3 form-password-toggle">
                            <div class="d-flex justify-content-between">
                                <label class="form-label" asp-for="Input.Password">@SharedLocalizer["New Password"]</label>
                                <a href="auth-forgot-password-basic.html">
                                </a>
                            </div>
                            <div class="input-group input-group-merge">
                                <input type="password" asp-for="Input.Password"
                                       class="form-control"
                                       placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                                       aria-describedby="password" />
                                <span class="input-group-text cursor-pointer"><i class="bx bx-hide"></i></span>
                            </div>
                        </div>

                        <div class="mb-3 form-password-toggle">
                            <div class="d-flex justify-content-between">
                                <label class="form-label" asp-for="Input.ConfirmPassword">@SharedLocalizer["Confirm New Password"]</label>
                                <a href="auth-forgot-password-basic.html">
                                </a>
                            </div>
                            <div class="input-group input-group-merge">
                                <input asp-for="Input.ConfirmPassword"
                                       class="form-control"
                                       placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                                       aria-describedby="password" />
                                <span class="input-group-text cursor-pointer"><i class="bx bx-hide"></i></span>
                            </div>
                        </div>

                        <div class="mb-3 d-flex">
                            <input class="btn btn-primary d-grid " value="@SharedLocalizer["Save"]" type="submit" />
                            <a asp-controller="Home" asp-action="Index" class="btn btn-secondary mx-2">@SharedLocalizer["Cancel"]</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>



@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
