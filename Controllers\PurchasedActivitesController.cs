﻿using Admin.TaskDotNet.Dtos;
using AutoMapper;
using Comman.Helper.Extensions;
using Comman.Services.Interfaces;
using Humanizer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Text;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Helper;
using TaskDotNet.Helper.Filters;
using TaskDotNet.Localization;
using TaskDotNet.Models;
using TaskDotNet.ViewModels;

namespace TaskDotNet.Controllers
{
    [Authorize(Roles = "Partner")]
    public class PurchasedActivitesController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IMapper mapper;
        private readonly UserManager<Partner> _userManager;
        private readonly IStringLocalizer<SharedResource> SharedLocalizer;
        private readonly IMailService _emailSender;


        public PurchasedActivitesController(ApplicationDbContext context, IMapper mapper, UserManager<Partner> userManager, IStringLocalizer<SharedResource> sharedLocalizer, IMailService emailSender)
        {
            _context = context;
            this.mapper = mapper;
            _userManager = userManager;
            SharedLocalizer = sharedLocalizer;
            _emailSender = emailSender;
        }

        [ServiceFilter(typeof(ValidatePartnerAccountStatusFilter))]
        public async Task<IActionResult> Index(PaginatedRequest request)
        {
            var partnerActivity = await _context.PartnerActivities.FirstOrDefaultAsync(p => p.PartnerId == _userManager.GetUserId(User));

            List<ActivityType> partnerActivities = GetPartnerActivities(partnerActivity);
            var now = DateTime.Now;

            var query = _context.Movements.AsQueryable().AsNoTracking()
                .AsNoTracking()
                .Where(m => m.Purchase_date >= new DateTime(now.Year, now.Month, 1));

            query = query.Where(p => p.PartnerId == _userManager.GetUserId(User)).Include(m => m.Activity);

            if (request.SearchTerm != 0)
            {
                query = query.Where(m => m.ActivityType == (ActivityType)request.SearchTerm);
            }

            int totalCount = query.Count();

            var movements = await query.OrderByDescending(a => a.ActivityType == ActivityType.Cleaning ? a.Activity.CleaningDate : a.Activity.MovingDate)
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToListAsync();

            ViewBag.PartnerActivities = partnerActivities.Select(m => new SelectListItem { Value = ((int)m).ToString(), Text = m.GetDisplayName() });

            ViewBag.Salodo = _context.Partners.Where(p => p.Id == _userManager.GetUserId(User)).Select(p => p.Saldo).FirstOrDefault();
            return View(new PaginatedListResponse<Movement>(
                data: movements ?? Enumerable.Empty<Movement>(),
                totalCount: totalCount,
                pageNumber: request.PageNumber,
                pageSize: request.PageSize,
                searchTerm: request.SearchTerm
            ));

        }

        #region Details
        public async Task<IActionResult> ActivityDetails(int id)
        {

            var model = await _context.MovementActivitys.Where(m => m.Id == id)
                .Include(m => m.InventoryItems)
                .FirstOrDefaultAsync();

            if (model == null)
            {
                return NotFound();
            }

            string currentLanguage = HttpContext.Features.Get<IRequestCultureFeature>().RequestCulture.Culture.TwoLetterISOLanguageName;

            ViewBag.InventoryHtml = GetInventoryHtml(model.InventoryItems, currentLanguage);

 
            return model.ActivityType switch
            {
                ActivityType.Moving => View("MovingActivityDetails", model),
                ActivityType.Cleaning => View("CleaningActivityDetails", model),
                ActivityType.MovingAndCleaning => View("MovingAndCleaningActivityDetails", model),
                ActivityType.PaintingAndGisper => View("PaintingAndGisperActivityDetails", model),
                _ => View("WorkersActivityDetails", model),
            };

        }
        #endregion

        #region EmailToCustomer
        public async Task<IActionResult> EmailToCustomer(int id)
        {
            var activity = await _context.MovementActivitys.Where(m => m.Id == id)
               .FirstOrDefaultAsync();
            if (activity == null)
            {
                return NotFound();
            }
            var partner = await _context.Partners.Where(p => p.Id == _userManager.GetUserId(User)).FirstOrDefaultAsync();
            if (partner == null)
            {
                return NotFound();
            }
            var execDate = activity.ActivityType == ActivityType.Cleaning ? activity.CleaningDate : activity.MovingDate;

            var (subject, textContent) = GetEmailContent(execDate, partner);

            var model = new EmailToCustomerViewModel
            {
                Activity = activity,
                Subject = subject,
                TextContent = textContent
            };

            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> EmailToCustomer([FromBody] EmailRequestModel model)
        {
            // Validate the input data
            if (!ModelState.IsValid || string.IsNullOrWhiteSpace(model.Email) || string.IsNullOrWhiteSpace(model.Subject))
            {
                return Json(new { success = false, message = SharedLocalizer["InvalidInput"] });
            }

            try
            {
                // Create the MailRequest object
                var mailRequest = new MailRequest
                {
                    ToEmail = model.Email,
                    Subject = model.Subject,
                    Body = model.TextContent
                };

                // Send the email asynchronously
                await _emailSender.SendEmailAsync(mailRequest, default);

                // Return success response
                return Json(new { success = true, message = SharedLocalizer["EmailSentSuccessfully."] });
            }
            catch (Exception ex)
            {
                // Return a generic error message to avoid exposing sensitive details
                return Json(new { success = false, message = SharedLocalizer["SomeThingWentWrong"] });
            }
        }

        #endregion

        private static List<ActivityType> GetPartnerActivities(PartnerActivities partnerActivities)
        {


            return partnerActivities.GetType().GetProperties()
                .Where(p => p.PropertyType == typeof(bool) && (bool)p.GetValue(partnerActivities))
                .Select(p => Enum.Parse(typeof(ActivityType), p.Name))
                .Cast<ActivityType>()
                .ToList();

        }

        private string GetInventoryHtml(List<MovementActivityInventoryItem> inventoryData, string lang)
        {

            StringBuilder servicesHtml = new();

            string furnitureHtml = string.Empty;
            if (inventoryData.Any())
            {
                var table = new StringBuilder();
                table.Append($@"
                    <div class='col'>
                        <h4 style='font-siye=20px;font-weight: 600;text-align: center;text-decoration: underline;'>{SharedLocalizer["Inventory list"]}:</h4>
                        <table class='table table-bordered table-responsive table-striped' style='border-collapse: collapse;width:100%;'>
                            <thead style='background-color: #008284; color:white; font-weight: bolder;'>
                                <tr>
                                    <th style='padding: 8px; text-align: left; font-weight: bold;color: white;'>{SharedLocalizer["Space"]}</th>
                                    <th style='padding: 8px; text-align: left; font-weight: bold;color: white;'>{SharedLocalizer["Items"]}</th>
                                    <th style='padding: 8px; text-align: left; font-weight: bold;color: white;'>{SharedLocalizer["Number"]}</th>
                                    <th style='padding: 8px; text-align: left; font-weight: bold;color: white;'>{SharedLocalizer["Total volume"]}</th>
                                </tr>
                            </thead>
                            <tbody>
                ");

                decimal totalVolumes = 0;

                var groupedInventory = inventoryData
                    .Where(item => !string.IsNullOrEmpty(item.Category))
                    .GroupBy(item => item.Category)
                    .ToList();

                foreach (var room in groupedInventory)
                {

                    string? roomName = room.Key;

                    var furnitureList = room.ToList();

                    foreach (var furniture in furnitureList)
                    {
                        string? furnitureName = lang switch
                        {
                            "de" => furniture.GermanName,
                            "en" => furniture.EnglishName,
                            "it" => furniture.ItalianName,
                            "fr" => furniture.FrenchName,
                            _ => furniture.EnglishName
                        };

                        var furnitureQuantity = furniture.Count;
                        double furnitureTotalVolumes = furniture.Total ?? 0;

                        table.Append("<tr>");
                        if (furniture == furnitureList.First())
                        {
                            table.Append($"<td rowspan='{furnitureList.Count}' style='border: 1px solid #ddd; padding: 8px;'>{roomName}</td>");
                        }

                        table.Append($@"
                            <td style='border: 1px solid #ddd; padding: 8px;'>{furnitureName}</td>
                            <td style='border: 1px solid #ddd; padding: 8px;'>{furnitureQuantity} {furniture.Unit}</td>
                            <td style='border: 1px solid #ddd; padding: 8px;'>{furnitureTotalVolumes.ToString("0.00")} m3</td>
                        ");

                        totalVolumes += (decimal)furnitureTotalVolumes;

                        table.Append("</tr>");
                    }

                }

                table.Append($@"
                                <tr><td colspan='4' style='border: 1px solid #ddd; padding: 8px;'></td></tr>
                                <tr class='total-weight-row' style='font-weight: bold;'>
                                    <td colspan='3' style='text-align: right; border: 1px solid #ddd; padding: 8px;'>{SharedLocalizer["Total volume"]}</td>
                                    <td style='text-align: center; border: 1px solid #ddd; padding: 8px;'>{totalVolumes.ToString("0.00")} m3</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                ");

                furnitureHtml = table.ToString();
            }
            return furnitureHtml;
        }

        private (string subject, string textContent) GetEmailContent(DateTime ExecDate, Partner partner)
        {
            // Get the current language (e.g., "de", "en", "fr", "it")
            var currentLanguage = CultureInfo.CurrentCulture.TwoLetterISOLanguageName;

            // Define email content templates for each language
            var emailTemplates = new Dictionary<string, (string subject, string textContent)>
        {
                // German Template
                {
                    "de", (
                        subject: "Besichtigungstermin",
                        textContent: $@"<p>Sehr geehrte Damen und Herren,</p>
                        <p>Vielen Dank für Ihre Abfrage im Hinblick auf den {ExecDate:dd.MM.yyyy}.</p>
                        <p>Ich würde die Wohnung gerne besichtigen, um mir einen besseren Eindruck zu verschaffen und Ihnen anschliessend ein passendes Angebot unterbreiten zu können.</p>
                        <p>Könnten Sie mir bitte einen Termin für eine unverbindliche Besichtigung von etwa 15 Minuten vorschlagen, der Ihnen gut passt?</p>
                        <p>Ich freue mich auf Ihre Rückmeldung und danke Ihnen im Voraus.</p>
                        <p>Mit freundlichen Grüssen,</p>
                        <p>{partner.CompanyName}<br>
                        {partner.PStreet}<br>
                        {partner.PPostBox} {partner.PCity}</p>
                        <p>{partner.Website}<br>
                        {partner.Email}<br>
                        {partner.Phone}<br>
                        {partner.Mobile}</p>"
                    )
                },

                // English Template
                {
                    "en", (
                        subject: "Viewing Appointment",
                        textContent: $@"<p>Dear Sir or Madam,</p>
                        <p>Thank you for your inquiry regarding {ExecDate:dd.MM.yyyy}.</p>
                        <p>I would like to view the apartment in order to get a better impression and then provide you with a suitable offer.</p>
                        <p>Could you please suggest a time for a non-binding viewing of approximately 15 minutes that suits you?</p>
                        <p>I look forward to your response and thank you in advance.</p>
                        <p>Kind regards,</p>
                        <p>{partner.CompanyName}<br>
                        {partner.PStreet}<br>
                        {partner.PPostBox} {partner.PCity}</p>
                        <p>{partner.Website}<br>
                        {partner.Email}<br>
                        {partner.Phone}<br>
                        {partner.Mobile}</p>"
                    )
                },

                // French Template
                {
                    "fr", (
                        subject: "Rendez-vous de visite",
                        textContent: $@"<p>Madame, Monsieur,</p>
                        <p>Merci pour votre message concernant le {ExecDate:dd.MM.yyyy}.</p>
                        <p>Je souhaiterais visiter l’appartement afin de me faire une meilleure idée et de pouvoir ensuite vous proposer une offre adaptée.</p>
                        <p>Pourriez-vous me proposer un créneau pour une visite non contraignante d’environ 15 minutes, selon vos disponibilités ?</p>
                        <p>Dans l’attente de votre réponse, je vous remercie par avance.</p>
                        <p>Cordialement,</p>
                        <p>{partner.CompanyName}<br>
                        {partner.PStreet}<br>
                        {partner.PPostBox} {partner.PCity}</p>
                        <p>{partner.Website}<br>
                        {partner.Email}<br>
                        {partner.Phone}<br>
                        {partner.Mobile}</p>"
                    )
                },

                // Italian Template
                {
                    "it", (
                        subject: "Appuntamento per sopralluogo",
                        textContent: $@"<p>Egregi Signori,</p>
                        <p>Grazie per la vostra comunicazione in merito al {ExecDate:dd.MM.yyyy}.</p>
                        <p>Vorrei visitare l’appartamento per farmi un’idea più precisa e potervi poi fare un’offerta adeguata.</p>
                        <p>Potreste gentilmente propormi un appuntamento per un sopralluogo non vincolante di circa 15 minuti, in un momento a voi comodo?</p>
                        <p>Resto in attesa di un vostro gentile riscontro e vi ringrazio anticipatamente.</p>
                        <p>Cordiali saluti,</p>
                        <p>{partner.CompanyName}<br>
                        {partner.PStreet}<br>
                        {partner.PPostBox} {partner.PCity}</p>
                        <p>{partner.Website}<br>
                        {partner.Email}<br>
                        {partner.Phone}<br>
                        {partner.Mobile}</p>"
                    )
                }
            };
            // Return the template for the current language, defaulting to English if not found
            return emailTemplates.ContainsKey(currentLanguage)
                ? emailTemplates[currentLanguage]
                : emailTemplates["en"];
        }
    }
    public class EmailRequestModel
    {
        public string Email { get; set; }

        public string Subject { get; set; }

        public string TextContent { get; set; }
    }
}
