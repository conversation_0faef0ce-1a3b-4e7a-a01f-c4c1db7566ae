﻿using Microsoft.Extensions.Localization;
using TaskDotNet.Localization;

namespace TaskDotNet.Models
{
    public class ErrorViewModel
    {
        private readonly IStringLocalizer<SharedResource> _localizer;
        public int StatusCode { get; set; }
        public string Message { get; set; }
        public string Title { get; set; }

        // Constructor to automatically assign title and message based on status code
        public ErrorViewModel(int statusCode, IStringLocalizer<SharedResource> localizer)
        {
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));

            StatusCode = statusCode;
            Title = GetErrorTitle(statusCode);
            Message = GetErrorMessage(statusCode);

        }

        // Method to get the error message based on the status code

        private string GetErrorMessage(int statusCode)
        {
            return statusCode switch
            {
                StatusCodes.Status400BadRequest => _localizer["BadRequestMessage"],
                StatusCodes.Status401Unauthorized => _localizer["UnauthorizedMessage"],
                StatusCodes.Status403Forbidden => _localizer["ForbiddenMessage"],
                StatusCodes.Status404NotFound => _localizer["NotFoundMessage"],
                _ => _localizer["InternalServerErrorMessage"],
            };
        }

        // Method to get the error title based on the status code
        private string GetErrorTitle(int statusCode)
        {
            return statusCode switch
            {
                StatusCodes.Status400BadRequest => _localizer["BadRequestTitle"],
                StatusCodes.Status401Unauthorized => _localizer["UnauthorizedTitle"],
                StatusCodes.Status403Forbidden => _localizer["ForbiddenTitle"],
                StatusCodes.Status404NotFound => _localizer["NotFoundTitle"],
                _ => _localizer["InternalServerErrorTitle"],
            };
        }
    }

}
