﻿@page
@model ForgotPasswordModel
@{
    ViewData["Title"] = "Forgot your password?";
}


<div class="container-xxl">
    <div class="authentication-wrapper authentication-basic container-p-y">
        <div class="authentication-inner">
            <div class="card">
                <div class="card-body">

                    <div class="app-brand justify-content-center">
                        <a class="app-brand-link gap-2">
                            <div class="img"> <img src="~/Dashboard/assets/img/logo.png" style="width: 200px;" alt=""></div>
                        </a>
                    </div>

                    <h4 class="mb-2">@SharedLocalizer["ForgotPassword"]? 🔒</h4>
                    <p class="mb-4">@SharedLocalizer["ForgotPasswordMessage"]</p>
                    <form  class="mb-3" method="POST">
                        <div class="mb-3">
                            <label asp-for="Input.Email" class="form-label">@SharedLocalizer["Email"]</label>
                            <input asp-for="Input.Email" class="form-control" placeholder="@SharedLocalizer["EnterYourEmail"]" autofocus />
                            <span asp-validation-for="Input.Email" class="text-danger"></span>
                        </div>
                        <input type="submit" value="@SharedLocalizer["SendResetLink"]" class="btn btn-primary d-grid w-100" />
                    </form>
                    <div class="text-center">
                        <a asp-controller="Account" asp-action="Login" class="d-flex align-items-center justify-content-center">
                            <i class="bx bx-chevron-left scaleX-n1-rtl bx-sm"></i>
                            @SharedLocalizer["Cancel"]
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
