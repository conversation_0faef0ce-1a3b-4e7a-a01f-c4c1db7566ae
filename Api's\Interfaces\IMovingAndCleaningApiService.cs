﻿using Admin.TaskDotNet.Dtos;
using TaskDotNet.Api_s.Controllers;
using TaskDotNet.Models;

namespace TaskDotNet.Api_s.Interfaces
{
    public interface IMovingAndCleaningApiService
    {
        Task<Activity> Create(Activity activity);
        Task Update(Activity activity);
        Task<Activity> CreateMovingAndCleaningActivityAsync(MovingCleaningDto dto, string lang);
        Task<Activity> AddInventoryToMovingAndCleaningAsync(AddInventoryWithMovingAndCleaningRequest request);


    }
}