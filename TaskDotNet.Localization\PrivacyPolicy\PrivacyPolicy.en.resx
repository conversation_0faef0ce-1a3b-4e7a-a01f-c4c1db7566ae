<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="PrivacyPolicyTitle" xml:space="preserve">
    <value>Privacy Policy</value>
  </data>
  <data name="WhoAreWeTitle" xml:space="preserve">
    <value>Who are we?</value>
  </data>
  <data name="WhoAreWeContent" xml:space="preserve">
    <value>This website and its content are provided by a private individual. The responsible party under data protection laws, especially the EU General Data Protection Regulation (GDPR), is: Oberhauserstrasse 3, 8152 Glattbrugg, Switzerland. E-mail: <EMAIL>, Website: www.TaskDotNet.com</value>
  </data>
  <data name="WhatDataTitle" xml:space="preserve">
    <value>What personal data we collect and why</value>
  </data>
  <data name="WhatDataContent1" xml:space="preserve">
    <value>When you access this site, general information is automatically collected and stored in server log files. This includes: browser type, operating system, ISP domain name, your IP address, and similar data.</value>
  </data>
  <data name="WhatDataContent2" xml:space="preserve">
    <value>We collect this data to: Ensure smooth connection to the website, Ensure proper use of our site, Evaluate system security and stability, For other administrative purposes.</value>
  </data>
  <data name="WhatDataContent3" xml:space="preserve">
    <value>Your data will not be used to personally identify you. It is used only for statistical analysis to improve the site and underlying technology.</value>
  </data>
  <data name="StorageDurationTitle" xml:space="preserve">
    <value>Storage duration</value>
  </data>
  <data name="StorageDurationContent" xml:space="preserve">
    <value>Data is deleted when it is no longer needed for its original purpose—usually at the end of your session.</value>
  </data>
  <data name="CommentsTitle" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="CommentsContent1" xml:space="preserve">
    <value>When visitors leave comments, we collect the data shown in the comment form, along with IP address and browser user-agent string for spam detection.</value>
  </data>
  <data name="CommentsContent2" xml:space="preserve">
    <value>Your email address may be anonymized (hashed) and sent to Gravatar to check if you use it. Gravatar Privacy Policy: https://automattic.com/privacy/ After approval, your profile picture becomes public with your comment.</value>
  </data>
  <data name="ContactFormsTitle" xml:space="preserve">
    <value>Contact forms</value>
  </data>
  <data name="ContactFormsContent" xml:space="preserve">
    <value>Information entered in contact forms (name, email) is stored to reply to your inquiry. Other fields are optional.</value>
  </data>
  <data name="CookiesTitle" xml:space="preserve">
    <value>Cookies</value>
  </data>
  <data name="CookiesContent" xml:space="preserve">
    <value>If you are logged in, a temporary cookie will be set to determine whether your browser accepts cookies. This cookie contains no personal data and is deleted when you close your browser.</value>
  </data>
  <data name="WebFontsTitle" xml:space="preserve">
    <value>Use of Script Libraries (Google Web Fonts)</value>
  </data>
  <data name="WebFontsContent" xml:space="preserve">
    <value>To display content uniformly, we use Google Web Fonts (Google LLC). More info: https://www.google.com/policies/privacy/</value>
  </data>
  <data name="AnalyticsTitle" xml:space="preserve">
    <value>Use of Google Analytics</value>
  </data>
  <data name="AnalyticsContent" xml:space="preserve">
    <value>This site uses Google Analytics for web analysis. Your IP is anonymized and not linked to other Google data.</value>
  </data>
  <data name="ChangesTitle" xml:space="preserve">
    <value>Changes to our privacy policy</value>
  </data>
  <data name="ChangesContent" xml:space="preserve">
    <value>We reserve the right to update this policy to meet legal requirements. The new version will apply on your next visit.</value>
  </data>
  <data name="QuestionsTitle" xml:space="preserve">
    <value>Questions about data protection</value>
  </data>
  <data name="QuestionsContent" xml:space="preserve">
    <value>Contact: Oberhauserstrasse 3, 8152 Glattbrugg, Switzerland. E-mail: <EMAIL></value>
  </data>
</root>
