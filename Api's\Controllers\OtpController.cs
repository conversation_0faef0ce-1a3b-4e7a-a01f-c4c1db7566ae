﻿using Admin.TaskDotNet.Dtos;
using Comman.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using TaskDotNetal.Api_s.Models;

namespace TaskDotNet.Api_s.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class OtpController : ControllerBase
    {
        private readonly IMailService _mailService;
        private readonly IEmailHtmlTemplateService _emailHtmlTemplateService;
        private readonly IOTPVerificationService _otpVerificationService;

        public OtpController(
            IMailService mailService,
            IEmailHtmlTemplateService emailHtmlTemplateService,
            IOTPVerificationService otpVerificationService)
        {
            _mailService = mailService;
            _emailHtmlTemplateService = emailHtmlTemplateService;
            _otpVerificationService = otpVerificationService;
        }

        [HttpPost("SendOtp")]
        public async Task<IActionResult> SendOtp([FromBody] string email, string lang)
        {
            var otp = await _otpVerificationService.CreateAsync(email);
            string body = await _emailHtmlTemplateService.GetOTPTemplateAsync(email, otp.OTP, lang);

            await _mailService.SendEmailAsync(email, "OTP for Email Verification", body);

            var response = new CustomResponse
            {
                Code = "200",
                Status = "Success",
                Message = "OTP created and sent successfully!"
            };
            return Ok(response);
        }

        [HttpPost("VerifyOTP")]
        public async Task<IActionResult> VerifyOTP([FromBody] OTPVerificationDto dto)
        {
            var otp = await _otpVerificationService.GetByEmailAsync(dto.Email);

            if (otp == null || otp.OTP != dto.OTP)
            {
                return BadRequest(new CustomResponse
                {
                    Code = "400",
                    Status = "Error",
                    Message = "Invalid OTP, something went wrong!"
                });
            }

            await _otpVerificationService.DeleteAsync(otp);

            return Ok(new CustomResponse
            {
                Code = "200",
                Status = "Success",
                Message = "OTP verified successfully!"
            });
        }
    }
}
