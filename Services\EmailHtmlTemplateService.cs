﻿using Admin.TaskDotNet.Dtos;
using Comman.Helper.Extensions;
using Comman.Services.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using MimeKit;
using System.Drawing;
using TaskDotNet.DTOs;
using TaskDotNet.Helper.Extensions;
using TaskDotNet.Localization;

namespace TaskDotNet.Services
{
    public class EmailHtmlTemplateService : IEmailHtmlTemplateService
    {
        #region Ctor
        private readonly IConfiguration _configuration;
        private readonly IWebHostEnvironment _environment;
        private readonly IStringLocalizer<SharedResource> SharedLocalizer;
        public EmailHtmlTemplateService(IConfiguration configuration, IWebHostEnvironment environment, IStringLocalizer<SharedResource> sharedLocalizer)
        {
            _configuration = configuration;
            _environment = environment;
            SharedLocalizer = sharedLocalizer;
        }
        #endregion

        #region Actvate Email Template
        public async Task<string> GetActvateEmailTemplate(string Email, string callbackUrl)
        {
            var EmailImages = _configuration.GetSection("EmailImages");

            var headerImage = EmailImages["companyLogo"];

            var pathToFile = $"{_environment.WebRootPath}\\Templates\\ActivateEmail.html";
            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {

                builder.HtmlBody = await SourceReader.ReadToEndAsync();

            }
            string messageBody = string.Format(builder.HtmlBody, Email, callbackUrl);
            return messageBody;

        }
        #endregion

        #region ThankYou Email Template
        public string GetThankYouTemplate(string salute, string name, string lang)
        {

            var pathToFile = $"{_environment.WebRootPath}\\Templates\\ThankYou-{lang}.html";
            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {

                builder.HtmlBody = SourceReader.ReadToEnd();

            }
            string messageBody = string.Format(builder.HtmlBody, salute, name);
            return messageBody;

        }
        #endregion 
        
        #region Customer ThankYou Email Template
        public string GetCustomerThankYouTemplate(string salute, string name, string lang)
        {

            var pathToFile = $"{_environment.WebRootPath}\\Templates\\ThankYouCustomer-{lang}.html";
            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {

                builder.HtmlBody = SourceReader.ReadToEnd();

            }
            string messageBody = string.Format(builder.HtmlBody, salute, name);
            return messageBody;

        }
        #endregion

        #region OTP Email Template
        public async Task<string> GetOTPTemplateAsync(string name, string otp, string lang)
        {
            var EmailImages = _configuration.GetSection("EmailImages");
            var headerImage = EmailImages["companyLogo"];

            var pathToFile = $"{_environment.WebRootPath}\\Templates\\OTP-{lang}.html";
            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {

                builder.HtmlBody = await SourceReader.ReadToEndAsync();

            }
            string messageBody = string.Format(builder.HtmlBody, otp);
            return messageBody;

        }
        #endregion

        #region  Reset Password Template
        public string GetResetPasswordemplate(string name, string lang, string callbacklink)
        {

            var pathToFile = $"{_environment.WebRootPath}\\Templates\\ResetPassword-{lang}.html";
            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {

                builder.HtmlBody = SourceReader.ReadToEnd();

            }
            string messageBody = string.Format(builder.HtmlBody, name, callbacklink);
            return messageBody;

        }
        #endregion

        #region Admin Email For New Partner Template
        public string GetAdminEmailForNewPartner(string Email, string CompanyName, DateTime StartDate)
        {
            var EmailImages = _configuration.GetSection("EmailImages");
            var headerImage = EmailImages["companyLogo"];

            var pathToFile = $"{_environment.WebRootPath}\\Templates\\AdminMessgAfterRegister.html";

            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {
                builder.HtmlBody = SourceReader.ReadToEnd();
            }

            string messageBody = builder.HtmlBody
                .Replace("{{0}}", headerImage)
                .Replace("{{CompanyName}}", CompanyName)
                .Replace("{{Email}}", Email)
                .Replace("{{StartDate}}", StartDate.ToString("dd.MM.yyyy"));

            return messageBody;
        }
        #endregion

    }

}
