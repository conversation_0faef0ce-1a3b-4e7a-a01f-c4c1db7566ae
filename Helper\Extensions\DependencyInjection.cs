﻿

using Admin.TaskDotNet.Services;
using TaskDotNet.Comman.DataAccess;
using Comman.Services.Interfaces;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Api_s.Interfaces;
using TaskDotNet.Api_s.Services;
using TaskDotNet.Helper.Filters;
using TaskDotNet.Helper.Mapper;
using TaskDotNet.Models;
using TaskDotNet.Services;
using TaskDotNet.Services.Interfaces;

namespace TaskDotNet.Helper.Extensions
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddServices(this IServiceCollection services, IConfiguration configurationParam)
        {
            services.AddDbContext<ApplicationDbContext>(options =>
                 options.UseSqlServer(configurationParam.GetConnectionString("DefultConnection"), b => b.MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName))
            );


            services.ConfigureApplicationCookie(options =>
            {
                options.AccessDeniedPath = new PathString("/Home/Error/401");
            });


            services.AddSession(options =>
            {
                options.IdleTimeout = TimeSpan.FromMinutes(60);
                options.Cookie.HttpOnly = true;
                options.Cookie.IsEssential = true;
                options.Cookie.Name = "TaskDotNet";
            });


            services.AddAutoMapper(x => x.AddProfile(new DomainProfile()));


            services.AddIdentity<Partner, IdentityRole>(options =>
            {
                // Default Password settings.
                options.Password.RequireDigit = false;
                options.Password.RequireLowercase = false;
                options.Password.RequireNonAlphanumeric = false;
                options.Password.RequireUppercase = false;
                options.Password.RequiredLength = 6;
                options.Password.RequiredUniqueChars = 0;
                options.User.RequireUniqueEmail = true;

                options.Lockout.AllowedForNewUsers = true;
                options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
                options.Lockout.MaxFailedAccessAttempts = 5;

                options.SignIn.RequireConfirmedEmail = true;
            })
            .AddEntityFrameworkStores<ApplicationDbContext>()
            .AddDefaultTokenProviders();



            #region Services.
            services.AddScoped<IMailService, MailService>();
            services.AddScoped<IEmailHtmlTemplateService, EmailHtmlTemplateService>();
            services.AddScoped<IOTPVerificationService, OTPVerificationService>();
            services.AddScoped<ValidatePartnerAccountStatusFilter>();
            services.AddScoped<IMovingService, MovingService>();
            services.AddScoped<IGisperService, GisperService>();
            services.AddScoped<IPaintingService, PaintingService>();
            services.AddScoped<ICleaningService, CleaningService>();
            services.AddScoped<IActivityService, ActivityService>();
            services.AddScoped<IInventoryService, InventoryService>();
            services.AddScoped<ICompanyService, CompanyService>();
            services.AddScoped<IArchiveService, ArchiveService>();
            services.AddScoped<IMovingApiService, MovingApiService>();
            services.AddScoped<IPaintingApiService, PaintingApiService>();
            services.AddScoped<ICleaningApiService, CleaningApiService>();
            services.AddScoped<IMovingAndCleaningApiService, MovingAndCleaningApiService>();
            services.AddScoped<IWorkersApiService, WorkersApiService>();
            services.AddScoped<IPostFinancePaymentService, PostFinancePaymentService>();
            #endregion Services.

            return services;
        }

    }
}
