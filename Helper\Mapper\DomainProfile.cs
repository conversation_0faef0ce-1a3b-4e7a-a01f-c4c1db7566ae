﻿using Admin.TaskDotNet.Dtos;
using AutoMapper;
using TaskDotNet.DTOs;
using TaskDotNet.Models;

namespace TaskDotNet.Helper.Mapper
{
    public class DomainProfile : Profile
    {
        public DomainProfile()
        {
            CreateMap<Activity, WorkersDto>();
            CreateMap<WorkersDto, Activity>();
            //---------------
            CreateMap<Activity, PaintingDto>();
            CreateMap<PaintingDto, Activity>();
            //---------------
            CreateMap<Activity, CleaningDto>();
            CreateMap<CleaningDto, Activity>();
            //---------------
            CreateMap<Activity, MovingDto>();
            CreateMap<MovingDto, Activity>();
            //---------------
            CreateMap<Activity, MovingCleaningDto>();
            CreateMap<MovingCleaningDto, Activity>();
            //---------------
            CreateMap<Activity, ActivityDto>();
            CreateMap<ActivityDto, Activity>();
            //---------------
            CreateMap<Payment, PaymentDto>();
            CreateMap<PaymentDto, Payment>();
            //---------------
            CreateMap<ActivityOrder, ActivityOrderDto>();
            CreateMap<ActivityOrderDto, ActivityOrder>();
            //---------------
            CreateMap<Inventory, InventoryDto>();
            CreateMap<InventoryDto, Inventory>();
            //---------------
            CreateMap<Inventory, InventoryApiDto>();
            CreateMap<InventoryApiDto, Inventory>();
            //---------------
            CreateMap<ActivityInventoryItem, InventoryItemApiDto>();
            CreateMap<InventoryItemApiDto, ActivityInventoryItem>();
            //---------------
            CreateMap<Company, CompanyDto>();
            CreateMap<CompanyDto, Company>();
            //---------------
            CreateMap<Archive, ArchiveDto>();
            CreateMap<ArchiveDto, Archive>();
            //---------------
            CreateMap<Movement, MovementDto>();
            CreateMap<MovementDto, Movement>();
            //---------------
            CreateMap<OTPVerification, OTPVerificationDto>();
            CreateMap<OTPVerificationDto, OTPVerification>();
            //---------------
            CreateMap<ContactUs, ContactUsDto>();
            CreateMap<ContactUsDto, ContactUs>();
            //---------------
            CreateMap<EmailText, EmailTextDto>();
            CreateMap<EmailTextDto, EmailText>();
            //---------------
            CreateMap<Partner, PartnerDto>();
            CreateMap<PartnerDto, Partner>();
            //---------------

            // Map Activity to MovementActivity
            CreateMap<Activity, MovementActivity>()
                .ForMember(dest => dest.Id, opt => opt.Ignore());

            // Map ActivityInventoryItem to MovementActivityInventoryItem
            CreateMap<ActivityInventoryItem, MovementActivityInventoryItem>()
                .ForMember(dest => dest.Id, opt => opt.Ignore());
        }
    }
}

